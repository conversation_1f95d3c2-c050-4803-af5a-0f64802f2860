{"name": "<PERSON><PERSON><PERSON>", "license": "MIT", "private": true, "packageManager": "bun@1.2.5", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}}, "dependencies": {"@blocknote/core": "^0.29.1", "@blocknote/mantine": "^0.29.1", "@blocknote/react": "^0.29.1", "@blocknote/server-util": "latest", "@codemirror/lang-javascript": "^6.2.2", "@hookform/resolvers": "^5.0.1", "@monaco-editor/react": "^4.6.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.0", "@tabler/icons-react": "^2.44.0", "@types/bcrypt": "^5.0.2", "@types/dom-to-image": "^2.6.7", "@types/formidable": "^3.4.5", "@types/html2canvas": "^1.0.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/react-dom": "^19.1.5", "@vercel/analytics": "^0.1.11", "@vercel/blob": "^1.0.1", "@vercel/examples-ui": "^1.0.5", "@wooorm/starry-night": "^2.1.1", "bcrypt": "^6.0.0", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^5.56.0", "crypto": "^1.0.1", "date-fns": "^2.30.0", "dom-to-image": "^2.6.0", "eventsource-parser": "^2.0.1", "file-saver": "^2.0.5", "formidable": "^3.5.4", "hast-util-to-jsx-runtime": "^2.0.0", "highlight.js": "^11.10.0", "html2canvas": "^1.4.1", "iconsax-react": "^0.0.8", "iconsax-reactjs": "^0.0.8", "idb": "^7.1.1", "js-cookie": "^3.0.5", "katex": "latest", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "mermaid": "latest", "mongodb": "^6.1.0", "next": "^14.2.5", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "posthog-js": "^1.77.1", "prism-react-renderer": "^2.0.6", "prismjs": "^1.29.0", "raw-loader": "^4.0.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-codemirror2": "^8.0.0", "react-cookie": "^4.1.1", "react-datepicker": "^7.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-markdown": "latest", "react-node-to-string": "^0.1.1", "react-query": "^3.39.3", "react-syntax-highlighter": "latest", "react-virtuoso": "^4.12.6", "react-wrap-balancer": "^0.1.5", "recharts": "^2.15.2", "rehype-highlight": "^6.0.0", "rehype-katex": "latest", "rehype-raw": "^6.1.1", "remark-breaks": "^4.0.0", "remark-gfm": "latest", "remark-math": "latest", "remark-toc": "^8.0.1", "resend": "^4.5.1", "swiper": "^11.2.8", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.0", "zod": "^3.24.4"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@storybook/addon-essentials": "^8.6.5", "@storybook/addon-onboarding": "^8.6.5", "@storybook/blocks": "^8.6.5", "@storybook/experimental-addon-test": "^8.6.5", "@storybook/experimental-nextjs-vite": "8.6.5", "@storybook/react": "^8.6.5", "@storybook/test": "^8.6.5", "@tailwindcss/typography": "^0.5.13", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/mixpanel-browser": "^2.47.1", "@types/node": "^17.0.45", "@types/react": "latest", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-syntax-highlighter": "^15.5.7", "@types/uuid": "^9.0.2", "@vitest/browser": "^3.0.8", "@vitest/coverage-v8": "^3.0.8", "autoprefixer": "^10.4.14", "eslint": "^9.9.0", "eslint-config-next": "^14.2.5", "eslint-plugin-storybook": "^0.11.4", "fake-indexeddb": "^4.0.2", "jest": "^29.7.0", "playwright": "^1.51.0", "postcss": "^8.4.21", "storybook": "^8.6.5", "tailwindcss": "^3.2.7", "ts-jest": "^29.3.1", "turbo": "^1.8.3", "typescript": "^5.5.4", "vercel": "^32.2.4", "vitest": "^3.0.8", "@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}}