import { ExternalProvider } from "./externalProvider";

export type AppSettingTheme = "system" | "dark" | "light";

export interface AppearanceColorAdvanced {
  [key: string]: string;
}

export interface AppearanceColorSetting {
  primary: string;
  secondary?: string;
  tertiary?: string;
  quaternary?: string;
}

export interface BorderRadius {
  topLeft: string; // e.g. "0.5rem"
  topRight: string; // e.g. "0.5rem"
  bottomLeft: string; // e.g. "0.5rem"
  bottomRight: string; // e.g. "0.5rem"
}

export interface AppearanceSetting {
  theme: AppSettingTheme;

  colorPalette: {
    light: AppearanceColorSetting;
    dark: AppearanceColorSetting;
  };

  advanced: {
    light: AppearanceColorAdvanced;
    dark: AppearanceColorAdvanced;
  };

  typography: {
    fontFamily: string;
    baseFontSize: string; // e.g. "16px"
    fontWeight: {
      normal: number;
      bold: number;
    };
  };

  button: {
    borderRadius: BorderRadius;
    padding: string;
    textStyle: {
      fontSize: string;
      fontFamily: string;
      fontWeight: string;
    };
    hoverStyle: {
      backgroundColor: string;
      textColor: string;
    };
  };

  card: {
    borderRadius: BorderRadius;
    hoverStyle: {
      backgroundColor: string;
      textColor: string;
    };
    shadowStyle: {
      boxShadow: string;
    };
  };

  logo: {
    url: string;
    height?: string; // optional, e.g. "40px"
    alt?: string;
  };

  component: {
    chatSection: {
      background: string;
    };
    sidebar: {
      background: string;
    };
    chatInput: {
      background: string;
    };

    temp_dynamic: Record<string, string>;
  };

  icon: {
    source?: "iconsax";
    customIcons?: {
      [key: string]: string;
    };
  };

  toolbar?: string[];
}

export interface AISetting {}

export interface AppSetting {
  appearance: AppearanceSetting;
  ai: {
    streamAIResponse: boolean;
    autoGenerateChatTitle: boolean;
    autoContinueResponse: boolean;
    autoSaveChat: boolean;
    submitWithEnter: boolean;
    autoFocusInput: boolean;
    defaultTone: "neutral" | "friendly" | "professional" | "sarcastic";
    defaultSystemPrompt: string;
    defaultModel: string;
    enableWebAccess: boolean;
    enablePlugins: boolean;
    smartRetry: boolean;
    showTokenUsage: boolean;
  };
  data: {
    analytics: {
      enabled: boolean;
      provider?: ExternalProvider;
      isCustom: boolean;
    };
    storage: {
      enabled: boolean;
      provider?: ExternalProvider;
      isCustom: boolean;
    };
  };
  audio: {
    textToSpeech: {
      enabled: boolean;
      playButtonForMessage: boolean;
      autoPlay: boolean;
      provider?: ExternalProvider;
      isCustom: boolean;
    };
    speechToText: {
      enabled: boolean;
      language: "auto" | string;
      autoRecord: boolean;
      continueRecordAfterAI: boolean;
      autoSend: boolean;
      provider?: ExternalProvider;
      isCustom: boolean;
    };
  };
}
