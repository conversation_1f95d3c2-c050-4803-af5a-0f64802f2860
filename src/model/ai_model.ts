export interface Model {
  id: string;
  name: string;
  vendor: string;
  keepLocal: {
    required: string[];
    optional: string[];
  };
  endpoint: string;
  headers: Record<string, string>;
  features: ExtendedFeature;
  config: ModelConfiguration;
  model_vendor_api?: string;
}

export interface ExtendedFeature {
  plugin: boolean;
  vision: boolean;
}

export interface ModelConfiguration {
  model: string;
  systemInstruction: string;
  streamResponse: boolean;
  contextLimit: number;
  temperature?: number; // Sampling temperature, 0 to 1
  top_p?: number; // Nucleus sampling probability, 0 to 1
  frequencyPenalty?: number; // Penalty for repeating tokens
  presencePenalty?: number; // Penalty for introducing new tokens
  maxTokens?: number; // Maximum number of tokens in the response
  stopSequences?: string; // Sequences where the model should stop generating
  logprobs?: number; // Number of log probabilities to include in the response
  n?: number; // Number of completions to generate
}
