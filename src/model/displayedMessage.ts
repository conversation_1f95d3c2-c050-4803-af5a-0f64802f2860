import { Plugin } from "@/model/plugin";
import { PromptData } from "./promptData";
import { Role } from "./sendMessage";

export type TomioAgent = Role;

export interface DisplayedMessage {
  messageId: string;
  role: TomioAgent;
  content: string;
  timestamp: Date;
  prompt: PromptData | null;
  isWriting: boolean;
  modelName: string;
  searchEngineQueries: string[];
  plugins: Plugin[];
  info?: {
    type: "info" | "error" | "warning";
    content: string;
    meta: { [key: string]: any };
  };
}
