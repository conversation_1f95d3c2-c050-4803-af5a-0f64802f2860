import MongoStringsRepository from '@/repo/backend/strings/mongo';
import MongoShareDocumentsRepository from '@/repo/backend/shareDocuments/mongo';
import { serverTranslation } from '@/lib/i18n/server';
import MarkdownContent from '@/components/MarkdownContent';
import '@blocknote/core/style.css';

interface DocumentStaticProps {
  params: { id: string; lang?: string };
}

export default async function DocumentStatic({ params }: DocumentStaticProps) {
  const { id, lang = 'en' } = params;

  const [shareRepository, stringsRepository] = [
    new MongoShareDocumentsRepository(),
    new MongoStringsRepository(),
  ];

  const document = await shareRepository.getSharedDocuments(id);

  if (!document) {
    const { t } = await serverTranslation(lang, ['sharedPage']);

    const [notFoundTitle, notFoundDescription] = await Promise.all([
      stringsRepository.getString('sharedPage.notFound.title', lang) ||
        t('notFound.title'),
      stringsRepository.getString('sharedPage.notFound.description', lang) ||
        t('notFound.description'),
    ]);

    return (
      <div className='p-10 text-center'>
        <h1 className='text-3xl font-semibold text-red-600'>{notFoundTitle}</h1>
        <p className='mt-2 text-gray-600'>{notFoundDescription}</p>
      </div>
    );
  }

  const { blocks = [] } = document;

  const jsonBody = JSON.stringify({ blocks: blocks });

  const response = await fetch(
    `${process.env.EXPRESS_RENDERER_BASE_URL}/documents/render/${id}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: jsonBody,
    }
  );

  const data = await response.json();
  const html = data.html.html as string;

  const wrapperClass = 'p-6 mx-auto';

  if (html) {
    return (
      <div className={wrapperClass}>
        <div dangerouslySetInnerHTML={{ __html: html }} />
      </div>
    );
  }

  return (
    <div className={wrapperClass}>
      <pre>{JSON.stringify(blocks, null, 2)}</pre>
    </div>
  );
}
