"use client";

import React from "react";
import { PromptLibraryProvider } from "@/context/usePromptLibrary";
import { ScrollArea } from "@/components/ui/scroll-area";
import { twMerge } from "tailwind-merge";
import { ChatMessage } from "@/components/ChatUI/TomioChat/ChatMessage";
import { WIDTH_WHEN_ONE_SIDEBAR_DISPLAYED } from "@/components/ChatUI/TomioChat/ChatView";
import { DisplayedMessage } from "@/model/displayedMessage";
import { PopupProvider } from "@/context/usePopUp";

interface SharePageClientComponentProps {
  messages: DisplayedMessage[];
}

const SharePageClientComponent: React.FC<SharePageClientComponentProps> = ({
  messages,
}) => {
  return (
    <PopupProvider>
      <PromptLibraryProvider>
        <ScrollArea className={twMerge("w-full h-full")}>
          <div className={twMerge("flex flex-col w-full  items-center")}>
            {messages.map((message, index) => (
              <div
                id={`chat-message-${message.messageId}`}
                key={`chat-message-${index}`}
                className={WIDTH_WHEN_ONE_SIDEBAR_DISPLAYED}
              >
                {message.content && (
                  <ChatMessage
                    messageId={message.messageId}
                    message={message}
                  />
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </PromptLibraryProvider>
    </PopupProvider>
  );
};

export default SharePageClientComponent;
