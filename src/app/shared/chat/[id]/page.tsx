import MongoStringsRepository from "@/repo/backend/strings/mongo";
import MongoShareChatsRepository from "@/repo/backend/shareChats/mongo";
import { notFound } from "next/navigation";
import Link from "next/link";
import SharePageClientComponent from "./client-component";
import { DisplayedMessage } from "@/model/displayedMessage";
import { Button } from "@/components/ui/button";
import { serverTranslation } from "@/lib/i18n/server";

interface SharedPageProps {
  params: { id: string; lang?: string };
}

// ✅ Reusable Banner Component (local to this file)
const StartNewChatBanner = ({
  title,
  description,
}: {
  title: string;
  description: string;
}) => (
  <div className="w-full flex justify-center mx-auto my-10">
    <div className="flex flex-col bg-purple-100 border border-purple-200 p-10 w-[800px] text-center rounded-lg space-y-4">
      <h2 className="text-xl font-semibold text-purple-800">{title}</h2>
      <p className="text-sm text-purple-700">{description}</p>
      <Link href="/" passHref target="_blank">
        <Button
          className="w-full sm:w-auto bg-purple-600 hover:bg-purple-700 text-white text-lg px-8"
          size="lg"
        >
          🧠 Start a New Chat
        </Button>
      </Link>
    </div>
  </div>
);

export default async function SharedPage({ params }: SharedPageProps) {
  const { id, lang = "en" } = params;

  const repository = new MongoShareChatsRepository();
  const sharedData = await repository.getSharedMessages(id);

  const { t } = await serverTranslation(lang, ["sharedPage"]);

  let introText = t("introText");
  let bannerTitle = t("banner.title");
  let bannerDescription = t("banner.description");

  const stringsRepository = new MongoStringsRepository();

  const introOverride = await stringsRepository.getString(
    "sharedPage.introText",
    lang
  );
  if (introOverride) introText = introOverride;

  const bannerTitleOverride = await stringsRepository.getString(
    "sharedPage.banner.title",
    lang
  );
  if (bannerTitleOverride) bannerTitle = bannerTitleOverride;

  const bannerDescOverride = await stringsRepository.getString(
    "sharedPage.banner.description",
    lang
  );
  if (bannerDescOverride) bannerDescription = bannerDescOverride;

  if (!sharedData) {
    const notFoundTitle =
      (await stringsRepository.getString("sharedPage.notFound.title", lang)) ||
      t("notFound.title");

    const notFoundDescription =
      (await stringsRepository.getString(
        "sharedPage.notFound.description",
        lang
      )) || t("notFound.description");

    const notFoundBannerTitle =
      (await stringsRepository.getString(
        "sharedPage.notFound.bannerTitle",
        lang
      )) || t("notFound.bannerTitle");

    const notFoundBannerDescription =
      (await stringsRepository.getString(
        "sharedPage.notFound.bannerDescription",
        lang
      )) || t("notFound.bannerDescription");

    return (
      <div className="bg-white rounded-lg overflow-hidden">
        <div className="px-4 py-12 text-center max-w-2xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            TomioAI.com
          </h1>
          <div className="text-2xl font-bold text-gray-800 mb-4">{notFoundTitle}</div>
          <p className="text-lg text-gray-600 mb-6 whitespace-pre-line">
            {notFoundDescription}
          </p>
          <StartNewChatBanner
            title={notFoundBannerTitle}
            description={notFoundBannerDescription}
          />
        </div>
      </div>
    );
  }

  const messages: DisplayedMessage[] = sharedData.messages.map((message) => ({
    messageId: message.messageId,
    role: message.role,
    content: message.content,
    timestamp: new Date(message.timestamp),
    prompt: message.prompt || null,
    isWriting: message.isWriting || false,
    modelName: message.modelName || "",
    searchEngineQueries: message.searchEngineQueries || [],
    plugins: message.plugins || [],
    info: message.info || undefined,
  }));

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
      <div className="px-4 py-8 text-center">
        <h1 className="text-3xl font-semibold text-gray-800 mb-4">
          TomioAI.com
        </h1>
        <p className="text-lg text-gray-600">{introText}</p>

        <StartNewChatBanner
          title={bannerTitle}
          description={bannerDescription}
        />
      </div>

      <SharePageClientComponent messages={messages} />

      <div className="mt-12">
        <StartNewChatBanner
          title={bannerTitle}
          description={bannerDescription}
        />
      </div>

      <div className="text-xs text-gray-500 py-4 text-center">
        Shared at: {new Date(sharedData.createdAt).toLocaleString()}
      </div>
    </div>
  );
}
