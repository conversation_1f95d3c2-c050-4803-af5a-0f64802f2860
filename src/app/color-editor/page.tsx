"use client";

import React, { useState } from "react";

type ColorPath = {
  path: string[]; // relative path within the group
  theme: "light" | "dark" | "common";
  value: string;
};

// Checks whether a string looks like a color
function isColor(value: any): boolean {
  return (
    typeof value === "string" &&
    (value.startsWith("#") || value.startsWith("--"))
  );
}

// A simple deep clone (assuming JSON-safe object)
function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

// Recursively extract color values from an object, appending keys to basePath
function getColorPaths(
  obj: any,
  theme: "light" | "dark" | "common",
  basePath: string[] = []
): ColorPath[] {
  let result: ColorPath[] = [];
  for (const key in obj) {
    const val = obj[key];
    const path = [...basePath, key];
    if (isColor(val)) {
      result.push({ path, theme, value: val });
    } else if (typeof val === "object" && val !== null) {
      result = result.concat(getColorPaths(val, theme, path));
    }
  }
  return result;
}

// Updates a nested key inside an object given the full path array
function setNestedValue(obj: any, path: string[], newValue: string): void {
  const lastKey = path[path.length - 1];
  const parent = path.slice(0, -1).reduce((acc, key) => acc[key], obj);
  parent[lastKey] = newValue;
}

// Defines one color entry (a row of values for light and dark)
interface GroupColorEntry {
  key: string; // the relative key (joined by "."), e.g. "primary" or "primaryForeground"
  light?: { value: string; path: string[] };
  dark?: { value: string; path: string[] };
  // for common groups, the same value is used for both columns
  common?: { value: string; path: string[] };
}

// Defines one group (parent key) that appears in the JSON in order
interface Group {
  groupName: string; // e.g. "color", "colorDark", "advanced", "component", etc.
  order: number; // position from the JSON object's keys order
  colors: GroupColorEntry[];
}

// Compute the groups from the JSON data
function computeGroups(data: any): Group[] {
  const groups: Group[] = [];
  const rootKeys = Object.keys(data);

  rootKeys.forEach((groupKey, index) => {
    // We'll only consider groups that are objects
    if (typeof data[groupKey] !== "object" || data[groupKey] === null) return;

    // Prepare an empty group for collecting colors
    const group: Group = { groupName: groupKey, order: index, colors: [] };

    // Special handling for known keys:
    if (groupKey === "advanced") {
      // For "advanced", assume it has "light" and "dark" sub-objects.
      const lightColors = data.advanced.light
        ? getColorPaths(data.advanced.light, "light", [])
        : [];
      const darkColors = data.advanced.dark
        ? getColorPaths(data.advanced.dark, "dark", [])
        : [];
      // Create a union of relative keys from both themes.
      const keysSet = new Set<string>();
      lightColors.forEach((cp) => keysSet.add(cp.path.join(".")));
      darkColors.forEach((cp) => keysSet.add(cp.path.join(".")));
      Array.from(keysSet).forEach((relKey) => {
        const relPath = relKey.split(".");
        const lightEntry = lightColors.find(
          (cp) => cp.path.join(".") === relKey
        );
        const darkEntry = darkColors.find(
          (cp) => cp.path.join(".") === relKey
        );
        group.colors.push({
          key: relKey,
          light: lightEntry ? { value: lightEntry.value, path: ["advanced", "light", ...relPath] } : undefined,
          dark: darkEntry ? { value: darkEntry.value, path: ["advanced", "dark", ...relPath] } : undefined,
        });
      });
    } else if (groupKey === "color" || groupKey === "colorDark") {
      // For these, they belong to one theme: "color" → light, "colorDark" → dark.
      const theme = groupKey === "color" ? "light" : "dark";
      const colors = getColorPaths(data[groupKey], theme, []);
      colors.forEach((cp) => {
        group.colors.push({
          key: cp.path.join("."),
          ...(theme === "light"
            ? { light: { value: cp.value, path: [groupKey, ...cp.path] } }
            : { dark: { value: cp.value, path: [groupKey, ...cp.path] } }),
        });
      });
    } else {
      // For any other group (like "component", etc.), treat them as common.
      const colors = getColorPaths(data[groupKey], "common", []);
      colors.forEach((cp) => {
        group.colors.push({
          key: cp.path.join("."),
          common: { value: cp.value, path: [groupKey, ...cp.path] },
        });
      });
    }

    if (group.colors.length > 0) {
      groups.push(group);
    }
  });

  // Sort groups by their order in the JSON
  groups.sort((a, b) => a.order - b.order);
  return groups;
}

export default function ColorEditorPage() {
  const [jsonText, setJsonText] = useState<string>("");
  const [data, setData] = useState<any | null>(null);

  // Update JSON from the textarea input
  const handleJsonInput = (text: string) => {
    setJsonText(text);
    try {
      const parsed = JSON.parse(text);
      setData(parsed);
    } catch {
      setData(null);
    }
  };

  // Handles color changes; it determines from the group name how to update the JSON
  const handleColorChange = (
    groupName: string,
    theme: "light" | "dark" | "common",
    relativePath: string[],
    newValue: string
  ) => {
    if (!data) return;
    const updated = deepClone(data);
    if (groupName === "advanced") {
      if (theme === "light" || theme === "dark") {
        setNestedValue(
          updated.advanced[theme],
          relativePath,
          newValue
        );
      }
    } else if (groupName === "color" || groupName === "colorDark") {
      // In these groups, the entire value is under that key.
      setNestedValue(updated, [groupName, ...relativePath], newValue);
    } else {
      // For common groups, update the value in that group (assumed same for both themes)
      setNestedValue(updated, [groupName, ...relativePath], newValue);
    }
    setData(updated);
    setJsonText(JSON.stringify(updated, null, 2));
  };

  // Compute groups from current JSON data.
  const groups = data ? computeGroups(data) : [];

  return (
    <div className="p-6 font-sans space-y-6 bg-white text-black">
      <h1 className="text-2xl font-bold">🎨 Color Palette JSON Editor</h1>

      <div className="flex space-x-2">
        <textarea
          className="w-1/2 h-64 p-2 font-mono border rounded"
          value={jsonText}
          onChange={(e) => handleJsonInput(e.target.value)}
          placeholder="Paste your color palette JSON here"
        />
        <textarea
          className="w-1/2 h-64 p-2 font-mono border rounded"
          value={data ? JSON.stringify(data, null, 2) : ""}
          readOnly
          placeholder="Updated JSON here"
        />
      </div>

      {data && groups.length > 0 && (
        <>
          {groups.map((group) => (
            <div key={group.groupName} className="mb-6">
              <h2 className="text-xl font-semibold mb-2">{group.groupName}</h2>
              <div className="grid grid-cols-4 gap-4 text-sm font-mono items-center">
                <div className="font-semibold col-span-1">Color Key</div>
                <div className="font-semibold">Light</div>
                <div className="font-semibold">Dark</div>
                <div className="font-semibold">Preview</div>
                {group.colors.map((entry) => {
                  // Determine the value and update paths for each column.
                  const lightVal =
                    entry.light?.value ||
                    entry.common?.value ||
                    "";
                  const darkVal =
                    entry.dark?.value ||
                    entry.common?.value ||
                    "";
                  // Relative path for update comes from the entry
                  const lightPath = entry.light
                    ? entry.light.path.slice(1) // remove groupName
                    : entry.common
                    ? entry.common.path.slice(1)
                    : [];
                  const darkPath = entry.dark
                    ? entry.dark.path.slice(1)
                    : entry.common
                    ? entry.common.path.slice(1)
                    : [];
                  return (
                    <React.Fragment key={entry.key}>
                      <div className="col-span-1">{entry.key}</div>
                      <div className="flex items-center gap-2">
                        <input
                          className="w-28 p-1 border rounded"
                          value={lightVal}
                          onChange={(e) =>
                            handleColorChange(
                              group.groupName,
                              entry.light ? "light" : "common",
                              lightPath,
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          className="w-28 p-1 border rounded"
                          value={darkVal}
                          onChange={(e) =>
                            handleColorChange(
                              group.groupName,
                              entry.dark ? "dark" : "common",
                              darkPath,
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div
                        className="w-6 h-6 rounded border"
                        style={{
                          background:
                            lightVal || darkVal || "transparent",
                        }}
                      />
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          ))}
        </>
      )}
    </div>
  );
}
