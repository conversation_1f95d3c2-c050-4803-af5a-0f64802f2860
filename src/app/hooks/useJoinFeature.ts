import { useState } from 'react';

function useJoinFeature() {
    const [buttonLoading, setButtonLoading] = useState(false);

    const joinFeature = async (email: string, feature: string) => {
        try {
            setButtonLoading(true);

            const response = await fetch("/api/join_feature", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ email, feature: feature }),
            });

            if (!response.ok) {
                throw new Error("Failed to add to the waitlist");
            }

            const { referal } = await response.json();

            setButtonLoading(false);
            return referal;
        } catch (error) {
            setButtonLoading(false);
            throw error;
        }
    };

    return { buttonLoading, joinFeature };
}

export default useJoinFeature;
