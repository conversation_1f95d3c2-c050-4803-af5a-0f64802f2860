import Link from "next/link"
import { notFound } from "next/navigation"
import type { Metada<PERSON> } from "next"

import { db } from "@/lib/db"
import { ResetPasswordForm } from "@/components/auth/reset-password-form"

export const metadata: Metadata = {
  title: "Reset Password",
  description: "Reset your password",
}

interface ResetPasswordPageProps {
  params: {
    token: string
  }
}

export default async function ResetPasswordPage({ params }: ResetPasswordPageProps) {
  const { token } = params

  const passwordResetToken = await db.passwordResetToken.findUnique({
    where: {
      token,
    },
  })

  if (!passwordResetToken) {
    notFound()
  }

  // Check if the token has expired
  const now = new Date()
  if (passwordResetToken.expires < now) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">Token expired</h1>
            <p className="text-sm text-muted-foreground">Your password reset token has expired.</p>
          </div>
          <p className="px-8 text-center text-sm text-muted-foreground">
            <Link href="/forgot-password" className="hover:text-brand underline underline-offset-4">
              Request a new token
            </Link>
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Reset Password</h1>
          <p className="text-sm text-muted-foreground">Enter your new password</p>
        </div>
        <ResetPasswordForm token={token} />
        <p className="px-8 text-center text-sm text-muted-foreground">
          <Link href="/login" className="hover:text-brand underline underline-offset-4">
            Back to login
          </Link>
        </p>
      </div>
    </div>
  )
}
