import Link from "next/link";
import { Sparkles, MessageSquare, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useServerLoginStatus } from "@/app/hooks/useLoginStatus";
import { cookies, type UnsafeUnwrappedCookies } from "next/headers";

interface NavbarProps {
  title: string;
  navLinks: {
    href: string;
    label: string;
  }[];
  alternatePageLink?: {
    href: string;
    label: string;
  };
}

export function Navbar({ title, navLinks, alternatePageLink }: NavbarProps) {
  const cookieStore = (cookies() as unknown as UnsafeUnwrappedCookies);
  const { token } = useServerLoginStatus(cookieStore);
  // const token = ""
  const isLoggedIn = token !== "";
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Sparkles className="h-6 w-6 text-purple-600" />
          <span className="text-xl font-bold">{title}</span>
        </div>

        <nav className="hidden md:flex items-center gap-6">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className="text-sm font-medium hover:text-purple-600 transition-colors"
            >
              {link.label}
            </Link>
          ))}
          {alternatePageLink && (
            <Link
              href={alternatePageLink.href}
              className="text-sm font-medium hover:text-purple-600 transition-colors"
            >
              {alternatePageLink.label}
            </Link>
          )}
        </nav>

        <div className="flex items-center gap-4">
          {isLoggedIn ? (
            <>
              <Button variant="primary-outline" size="sm">
                <Link href="/chat" className="flex items-center gap-1">
                  <MessageSquare className="w-4 h-4" />
                  Chat
                </Link>
              </Button>
              <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                <Link href="/dashboard" className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  Dashboard
                </Link>
              </Button>
            </>
          ) : (
            <>
              <Button variant="primary-outline" size="sm">
                <Link href="/login">Log in</Link>
              </Button>
              <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                <Link href="/signup">Sign up</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
