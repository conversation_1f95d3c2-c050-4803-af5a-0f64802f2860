import { Clock, Users, Presentation, MessageSquare } from "lucide-react"

import { Navbar } from "@/app/components/layout/navbar"
import { Footer } from "@/app/components/layout/footer"
import { HeroSection } from "@/components/sections/hero-section"
import { DemoExpectations } from "@/components/sections/demo-expectations"
import { VideoPlayer } from "@/components/sections/video-player"
import { PreparationChecklist } from "@/components/sections/preparation-checklist"
import { CalendlyEmbed } from "@/components/sections/calendly-embed"
import { DemoFAQ } from "@/components/sections/demo-faq"

export default function DemoPage() {
  // Navbar configuration
  const navLinks = [
    { href: "#what-to-expect", label: "What to Expect" },
    { href: "#preparation", label: "How to Prepare" },
    { href: "#book-demo", label: "Book a Demo" },
    { href: "#faq", label: "FAQ" },
  ]

  // Hero section configuration
  const heroProps = {
    title: (
      <>
        Experience <span className="text-purple-600">AIFlow</span> in Action
      </>
    ),
    subtitle: "See how our AI assistant platform can transform your workflow with a personalized demo",
    primaryButtonText: "Book Your Demo Now",
    secondaryButtonText: "Watch Demo Video",
    features: ["30-minute personalized session", "Live Q&A with experts", "Custom solution recommendations"],
    imageAlt: "AIFlow Demo Session",
  }

  // Demo expectations configuration
  const expectationsProps = {
    title: "What to Expect During Your Demo",
    subtitle:
      "Our personalized demo sessions are designed to show you exactly how AIFlow can work for your specific needs",
    expectations: [
      {
        icon: Clock,
        title: "30-Minute Session",
        description: "A focused session that respects your time while covering all essential features",
      },
      {
        icon: Presentation,
        title: "Product Walkthrough",
        description: "See the platform in action with real-world examples relevant to your use case",
      },
      {
        icon: Users,
        title: "Personalized Approach",
        description: "Tailored demonstration based on your industry, team size, and specific challenges",
      },
      {
        icon: MessageSquare,
        title: "Q&A Session",
        description: "Dedicated time to answer all your questions and address specific concerns",
      },
    ],
  }

  // Preparation checklist configuration
  const questionsChecklistProps = {
    title: "Questions to Prepare",
    subtitle: "To get the most out of your demo, consider preparing these questions",
    items: [
      {
        title: "Integration Capabilities",
        description: "Ask about how AIFlow integrates with your existing tools and workflows",
      },
      {
        title: "Customization Options",
        description: "Inquire about the level of customization available for your specific use cases",
      },
      {
        title: "Security and Compliance",
        description: "Learn about our security measures and compliance with industry standards",
      },
      {
        title: "Onboarding Process",
        description: "Understand the steps involved in implementing AIFlow in your organization",
      },
      {
        title: "Pricing and ROI",
        description: "Get clarity on pricing structure and expected return on investment",
      },
    ],
  }

  const goalsChecklistProps = {
    title: "Goals to Define",
    subtitle: "Clarify what you want to achieve with AIFlow to make your demo more effective",
    items: [
      {
        title: "Primary Use Case",
        description: "Define the main problem you're trying to solve with an AI assistant",
      },
      {
        title: "Success Metrics",
        description: "Determine how you'll measure the success of implementing AIFlow",
      },
      {
        title: "Team Structure",
        description: "Consider which teams or departments will be using the platform",
      },
      {
        title: "Timeline Expectations",
        description: "Have a rough idea of when you'd like to implement the solution",
      },
      {
        title: "Budget Considerations",
        description: "Know your budget constraints to discuss appropriate plans",
      },
    ],
  }

  // FAQ configuration
  const faqProps = {
    title: "Frequently Asked Questions",
    subtitle: "Common questions about our demo process and what happens next",
    faqs: [
      {
        question: "How long does the demo typically last?",
        answer:
          "Our demos are designed to be efficient and respectful of your time. They typically last 30 minutes, with about 20 minutes for the demonstration and 10 minutes for questions and answers. If you need more time, we're happy to schedule a follow-up session.",
      },
      {
        question: "Do I need to prepare anything before the demo?",
        answer:
          "While no preparation is required, you'll get the most value if you think about your specific needs and challenges beforehand. Consider the questions and goals outlined on this page, and feel free to share any specific use cases you'd like us to address during the demo.",
      },
      {
        question: "Can I invite other team members to the demo?",
        answer:
          "We encourage you to invite key stakeholders who would be involved in the decision-making process or who would be using AIFlow. Just let us know in advance how many people will be joining so we can tailor the session accordingly.",
      },
      {
        question: "Will the demo be recorded?",
        answer:
          "We don't automatically record demos to protect your privacy. However, if you'd like a recording for reference or to share with team members who couldn't attend, we can arrange that with your permission.",
      },
      {
        question: "What happens after the demo?",
        answer:
          "After the demo, we'll follow up with additional resources based on your interests. If you're ready to move forward, we can discuss next steps such as a free trial, implementation timeline, or custom pricing options. There's no pressure – we're here to help when you're ready.",
      },
      {
        question: "Can the demo be customized to my industry or use case?",
        answer:
          "Yes! We specialize in tailoring our demos to your specific industry and use cases. When you book a demo, you'll have the option to provide information about your needs, which helps us prepare a relevant demonstration.",
      },
    ],
  }

  // Footer configuration
  const footerLinks = [
    {
      category: "Product",
      items: [
        { label: "Features", href: "/#features" },
        { label: "Pricing", href: "/#pricing" },
        { label: "Enterprise", href: "/landing-enterprise" },
      ],
    },
    {
      category: "Resources",
      items: [
        { label: "Documentation", href: "#" },
        { label: "Blog", href: "#" },
        { label: "Support", href: "#" },
      ],
    },
    {
      category: "Company",
      items: [
        { label: "About", href: "#" },
        { label: "Contact", href: "#" },
        { label: "Privacy", href: "#" },
      ],
    },
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar title="AIFlow" navLinks={navLinks} alternatePageLink={{ href: "/", label: "Home" }} />

      <main className="flex-1">
        {/* Hero Section */}
        <HeroSection {...heroProps} />

        {/* What to Expect Section */}
        <section id="what-to-expect" className="py-12 bg-gray-50 dark:bg-gray-900">
          <div className="container px-4 md:px-6">
            <DemoExpectations {...expectationsProps} />

            <div className="mt-16 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-center mb-8">Watch Our Demo Overview</h3>
              <VideoPlayer
                videoUrl="/demo-video.mp4" // Replace with your actual video URL
                title="Learn what to expect in your personalized AIFlow demo session"
                posterUrl="/placeholder.svg?height=720&width=1280" // Replace with your actual poster image
              />
              <p className="text-center text-sm text-gray-500 mt-4">
                This video provides a brief overview of what you'll experience in your personalized demo session.
              </p>
            </div>
          </div>
        </section>

        {/* Preparation Section */}
        <section id="preparation" className="py-16">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">How to Prepare</h2>
                <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed dark:text-gray-400">
                  Get the most out of your demo by preparing these questions and defining your goals
                </p>
              </div>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
              <PreparationChecklist {...questionsChecklistProps} />
              <PreparationChecklist {...goalsChecklistProps} />
            </div>
          </div>
        </section>

        {/* Calendly Section */}
        <section id="book-demo" className="py-16 bg-gray-50 dark:bg-gray-900">
          <div className="container px-4 md:px-6">
            <CalendlyEmbed
              url="https://calendly.com/your-calendly-link" // Replace with your actual Calendly URL
              title="Book Your Personalized Demo"
              subtitle="Select a convenient time for your 30-minute personalized demo session"
            />
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="py-16">
          <div className="container px-4 md:px-6">
            <DemoFAQ {...faqProps} />
          </div>
        </section>
      </main>

      <Footer
        title="AIFlow"
        description="Build an AI assistant that works like you do. Customize everything. Use your own model. Grow smarter."
        links={footerLinks}
      />
    </div>
  )
}
