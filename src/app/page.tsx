import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CheckCircle2,
} from "lucide-react";

import { Navbar } from "@/app/components/layout/navbar"
import { Footer } from "@/app/components/layout/footer"
import { HeroSection } from "@/components/sections/hero-section";
import { HowItWorksSection } from "@/components/sections/how-it-works-section";
import { FeaturesSection } from "@/components/sections/features-section";
import { TargetAudienceSection } from "@/components/sections/target-audience-section";
import { PricingSection } from "@/components/sections/pricing-section";
import { DemoBookingSection } from "@/components/sections/demo-booking-section";

export default function GeneralLandingPage() {
  // Navbar configuration
  const navLinks = [
    { href: "#features", label: "Features" },
    { href: "#how-it-works", label: "How It Works" },
    { href: "#pricing", label: "Pricing" },
  ];

  // Hero section configuration
  const heroProps = {
    title: (
      <>
        Build an AI assistant that works{" "}
        <span className="text-purple-600">like you do</span>
      </>
    ),
    subtitle: "Customize everything. Use your own model. Grow smarter.",
    primaryButtonText: "Start customizing your AI now",
    secondaryButtonText: "Book a 1-on-1 demo",
    features: [
      "No coding required",
      "Connect any AI model",
      "Detailed analytics",
    ],
    imageAlt: "AI Assistant Dashboard",
  };

  // How it works section configuration
  const howItWorksProps = {
    title: "How It Works",
    subtitle: "Create your custom AI assistant in three simple steps",
    steps: [
      {
        number: 1,
        title: "Choose Your Models",
        description:
          "Connect OpenAI, Claude, Gemini, or any other AI model you prefer to use.",
      },
      {
        number: 2,
        title: "Design Your Workflow",
        description:
          "Customize how your AI responds, what data it accesses, and how it processes information.",
      },
      {
        number: 3,
        title: "Deploy & Optimize",
        description:
          "Launch your assistant and use analytics to continuously improve its performance.",
      },
    ],
  };

  // Features section configuration
  const featuresProps = {
    title: "Key Features",
    subtitle: "Everything you need to build the perfect AI assistant",
    features: [
      {
        icon: Zap,
        title: "Fully Customizable Workflows",
        description:
          "Design complex AI workflows with our intuitive drag-and-drop interface. No coding required.",
      },
      {
        icon: Settings,
        title: "Multi-Model Support",
        description:
          "Connect and use any AI model from OpenAI, Claude, Gemini, and more in a single workflow.",
      },
      {
        icon: BarChart3,
        title: "Detailed Analytics",
        description:
          "Track usage, performance, and user satisfaction to continuously improve your AI assistant.",
      },
      {
        icon: Users,
        title: "Team Collaboration",
        description:
          "Work together with your team to build, test, and refine your AI assistants.",
      },
      {
        icon: Sparkles,
        title: "Easy Integration",
        description:
          "Embed your AI assistant anywhere with our API, widgets, and pre-built integrations.",
      },
      {
        icon: CheckCircle2,
        title: "Version Control",
        description:
          "Keep track of changes and roll back to previous versions of your AI assistant when needed.",
      },
    ],
  };

  // Target audience section configuration
  const targetAudienceProps = {
    title: "Perfect For",
    subtitle:
      "Designed for freelancers, creators, remote workers, agencies, and solopreneurs",
    audiences: [
      {
        title: "Freelancers & Solopreneurs",
        description:
          "Automate repetitive tasks, manage client communications, and scale your business without hiring.",
      },
      {
        title: "Content Creators",
        description:
          "Generate ideas, research topics, and create content outlines with an AI assistant that understands your style.",
      },
      {
        title: "Remote Workers",
        description:
          "Stay organized, manage your schedule, and boost productivity with a personalized AI workflow.",
      },
      {
        title: "Agencies",
        description:
          "Create custom AI solutions for clients, streamline project management, and deliver better results faster.",
      },
    ],
  };

  // Pricing section configuration
  const pricingProps = {
    title: "Simple Pricing",
    subtitle:
      "Start from only IDR 10,000/month with our limited early access offer",
    pricing: {
      title: "Early Access",
      price: "IDR 10,000",
      period: "/month",
      features: [
        "Unlimited AI assistants",
        "Connect to any AI model",
        "Basic analytics",
        "Priority support",
      ],
      buttonText: "Get Early Access",
      warningText: "⚠️ Prices will rise soon — grab this low rate now!",
    },
    alternateLink: {
      text: "Looking for Enterprise pricing? Click here →",
      href: "/landing-enterprise",
    },
  };

  // Demo booking section configuration
  const demoBookingProps = {
    title: "See It In Action",
    subtitle:
      "Book a personalized demo to see how our platform can work for your specific needs",
    buttonText: "Book a 1-on-1 Demo",
    disclaimer: "No obligation. Cancel anytime.",
  };

  // Footer configuration
  const footerLinks = [
    {
      category: "Product",
      items: [
        { label: "Features", href: "#" },
        { label: "Pricing", href: "#" },
        { label: "Roadmap", href: "#" },
      ],
    },
    {
      category: "Company",
      items: [
        { label: "About", href: "#" },
        { label: "Blog", href: "#" },
        { label: "Careers", href: "#" },
      ],
    },
    {
      category: "Legal",
      items: [
        { label: "Privacy", href: "#" },
        { label: "Terms", href: "#" },
        { label: "Cookie Policy", href: "#" },
      ],
    },
  ];

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar
        title="AIFlow"
        navLinks={navLinks}
        alternatePageLink={{
          href: "/landing-enterprise",
          label: "Enterprise",
        }}
      />

      <main className="flex-1">
        <HeroSection {...heroProps} />
        <HowItWorksSection {...howItWorksProps} />
        <FeaturesSection {...featuresProps} />
        <TargetAudienceSection {...targetAudienceProps} />
        <PricingSection {...pricingProps} />
        <DemoBookingSection {...demoBookingProps} />
      </main>

      <Footer
        title="AIFlow"
        description="Build an AI assistant that works like you do. Customize everything. Use your own model. Grow smarter."
        links={footerLinks}
      />
    </div>
  );
}
