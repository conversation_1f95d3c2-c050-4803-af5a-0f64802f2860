import { hash } from "bcrypt"
import { NextResponse } from "next/server"
import { z } from "zod"

import { db } from "@/lib/db"
import { newPasswordSchema } from "@/lib/validations/auth"

export async function POST(req: Request) {
  try {
    const json = await req.json()
    const body = newPasswordSchema.parse(json)
    const { token } = json

    if (!token) {
      return NextResponse.json({ error: "Token is required" }, { status: 400 })
    }

    // Find the token in the database
    const passwordResetToken = await db.passwordResetToken.findUnique({
      where: {
        token,
      },
    })

    if (!passwordResetToken) {
      return NextResponse.json({ error: "Invalid token" }, { status: 400 })
    }

    // Check if the token has expired
    const now = new Date()
    if (passwordResetToken.expires < now) {
      return NextResponse.json({ error: "Token has expired" }, { status: 400 })
    }

    // Hash the new password
    const hashedPassword = await hash(body.password, 10)

    // Update the user's password
    await db.user.update({
      where: {
        email: passwordResetToken.email,
      },
      data: {
        password: hashedPassword,
      },
    })

    // Delete the token
    await db.passwordResetToken.delete({
      where: {
        id: passwordResetToken.id,
      },
    })

    return NextResponse.json({ message: "Password updated successfully" }, { status: 200 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 })
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
