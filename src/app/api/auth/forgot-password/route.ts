// app/api/auth/reset-password/route.ts

import { randomUUID } from "crypto";
import { NextResponse } from "next/server";
import { z } from "zod";
import { promises as fs } from "fs";
import path from "path";

import { db } from "@/lib/db";
import { forgotPasswordSchema } from "@/lib/validations/auth";
import { Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY);

export const runtime = "nodejs";

export async function POST(req: Request) {
  try {
    const json = await req.json();
    const body = forgotPasswordSchema.parse(json);

    const { email } = body;

    const user = await db.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json({ message: "Password reset email sent" }, { status: 200 });
    }

    const token = randomUUID();
    const expires = new Date(Date.now() + 3600 * 1000); // 1 hour

    await db.passwordResetToken.create({
      data: {
        email,
        token,
        expires,
      },
    });

    const resetLink = `${process.env.NEXTAUTH_URL}/reset-password?token=${token}`;


    const filePath = path.join(process.cwd(), "emails", "templates", "reset-password.html");
    let html = await fs.readFile(filePath, "utf8");

    html = html.replace("{{reset_link}}", resetLink);


    await resend.emails.send({
      from: "TomioAI <<EMAIL>>",
      to: email,
      subject: "Reset your password",
      html,
    });

    return NextResponse.json(
      { message: "Password reset email sent" },
      { status: 200 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 });
    }

    console.error("Error sending reset email:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
