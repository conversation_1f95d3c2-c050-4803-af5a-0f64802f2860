import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"
import { z } from "zod"

import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { onboardingSchema } from "@/lib/validations/auth"

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const json = await req.json()
    const body = onboardingSchema.parse(json)

    const { name, jobTitle, company, bio, location, website } = body

    // Update user
    await db.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        name,
      },
    })

    // Update or create profile
    await db.profile.upsert({
      where: {
        userId: session.user.id,
      },
      update: {
        jobTitle,
        company,
        bio,
        location,
        website,
        onboarded: true,
      },
      create: {
        userId: session.user.id,
        jobTitle,
        company,
        bio,
        location,
        website,
        onboarded: true,
      },
    })

    return NextResponse.json({ message: "Onboarding completed successfully" }, { status: 200 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 })
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
