import { compare } from "bcrypt"
import { getServerSession } from "next-auth"
import { NextResponse } from "next/server"

import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { password } = await req.json()

    if (!password) {
      return NextResponse.json({ error: "Password is required" }, { status: 400 })
    }

    // Get the user with their current password
    const user = await db.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        password: true,
      },
    })

    if (!user?.password) {
      return NextResponse.json({ error: "Password is required" }, { status: 400 })
    }

    // Check if the password is correct
    const passwordMatch = await compare(password, user.password)

    if (!passwordMatch) {
      return NextResponse.json({ error: "Password is incorrect" }, { status: 400 })
    }

    // Delete the user
    await db.user.delete({
      where: {
        id: session.user.id,
      },
    })

    return NextResponse.json({ message: "Account deleted successfully" }, { status: 200 })
  } catch (error) {
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
