import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>2, <PERSON>, Layers } from "lucide-react"

import { Navbar } from "@/app/components/layout/navbar"
import { Footer } from "@/app/components/layout/footer"
import { HeroSection } from "@/components/sections/hero-section"
import { TrustedBySection } from "@/components/sections/trusted-by-section"
import { FeaturesSection } from "@/components/sections/features-section"
import { PricingSection } from "@/components/sections/pricing-section"
import { SuccessStoriesSection } from "@/components/sections/success-stories-section"
import { EnterpriseCTASection } from "@/components/sections/enterprise-cta-section"

export default function EnterpriseLandingPage() {
  // Navbar configuration
  const navLinks = [
    { href: "#features", label: "Features" },
    { href: "#pricing", label: "Pricing" },
    { href: "#customers", label: "Customers" },
  ]

  // Hero section configuration
  const heroProps = {
    title: (
      <>
        Enterprise AI Assistants <span className="text-purple-600">Your Way</span>
      </>
    ),
    subtitle:
      "Customize, monitor, and scale AI assistants across your organization with complete control and security.",
    primaryButtonText: "Schedule Enterprise Demo",
    secondaryButtonText: "Contact Sales",
    features: ["Brand customization", "Usage monitoring", "Enterprise security"],
    imageAlt: "Enterprise AI Dashboard",
  }

  // Trusted by section configuration
  const trustedByProps = {
    title: "Trusted by teams at these companies",
    companies: [
      { alt: "Company Logo 1", imageUrl: "/placeholder.svg?height=48&width=128" },
      { alt: "Company Logo 2", imageUrl: "/placeholder.svg?height=48&width=128" },
      { alt: "Company Logo 3", imageUrl: "/placeholder.svg?height=48&width=128" },
      { alt: "Company Logo 4", imageUrl: "/placeholder.svg?height=48&width=128" },
      { alt: "Company Logo 5", imageUrl: "/placeholder.svg?height=48&width=128" },
    ],
  }

  // Features section configuration
  const featuresProps = {
    title: "Enterprise Features",
    subtitle: "Everything your organization needs to deploy, customize, and manage AI assistants at scale",
    features: [
      {
        icon: Palette,
        title: "Brand Customization",
        description: "Customize the UI to match your brand identity with your colors, logos, and design elements.",
      },
      {
        icon: LineChart,
        title: "Usage Monitoring",
        description: "Track AI usage across your organization with detailed analytics and reporting dashboards.",
      },
      {
        icon: Layers,
        title: "Custom UI Builder",
        description:
          "Create tailored interfaces for different departments and employee roles with our no-code builder.",
      },
      {
        icon: Shield,
        title: "Enterprise Security",
        description:
          "SOC 2 compliance, SSO integration, role-based access control, and data encryption at rest and in transit.",
      },
      {
        icon: Building2,
        title: "Multi-Department Support",
        description:
          "Create separate workspaces for different departments with customized AI assistants for each team.",
      },
      {
        icon: Lock,
        title: "Data Privacy Controls",
        description:
          "Control what data your AI can access and ensure sensitive information stays within your organization.",
      },
    ],
  }

  // Pricing section configuration
  const pricingProps = {
    title: "Enterprise Pricing",
    subtitle: "Transparent, per-user pricing with all enterprise features included",
    pricing: {
      title: "Enterprise Plan",
      price: "IDR 25,000",
      period: "/user/month",
      features: [
        "Unlimited AI assistants",
        "Complete brand customization",
        "Advanced usage analytics",
        "Custom UI builder",
        "Enterprise security features",
        "Dedicated account manager",
        "Priority 24/7 support",
      ],
      buttonText: "Contact Sales",
      warningText: "⚠️ Limited time offer — Prices will increase soon!",
      footerText: "Volume discounts available for organizations with 100+ users.",
    },
    alternateLink: {
      text: "Looking for individual pricing? Click here →",
      href: "/",
    },
  }

  // Success stories section configuration
  const successStoriesProps = {
    title: "Customer Success Stories",
    subtitle: "See how leading organizations are transforming their operations with AIFlow",
    stories: [
      {
        company: {
          name: "TechCorp International",
          industry: "Technology",
          logoUrl: "/placeholder.svg?height=48&width=48",
        },
        quote:
          "AIFlow Enterprise has transformed how our 500+ employees interact with AI. The custom UI for each department and usage analytics have been game-changers for productivity.",
        person: {
          name: "Sarah Johnson",
          title: "CTO, TechCorp International",
        },
        results: "42% increase in productivity, 30% cost reduction",
      },
      {
        company: {
          name: "Global Finance Group",
          industry: "Financial Services",
          logoUrl: "/placeholder.svg?height=48&width=48",
        },
        quote:
          "The security features and brand customization capabilities of AIFlow Enterprise allowed us to deploy AI assistants that our compliance team approved and our employees love.",
        person: {
          name: "Michael Chen",
          title: "Head of Innovation, Global Finance Group",
        },
        results: "65% faster customer response time, 28% higher satisfaction",
      },
      {
        company: {
          name: "HealthPlus Systems",
          industry: "Healthcare",
          logoUrl: "/placeholder.svg?height=48&width=48",
        },
        quote:
          "We needed an AI solution that could handle our strict data privacy requirements. AIFlow Enterprise delivered that and more with their customizable interfaces for different medical departments.",
        person: {
          name: "Dr. Amelia Rodriguez",
          title: "Director of Digital Transformation, HealthPlus",
        },
        results: "3.5 hours saved per doctor daily, 22% reduction in admin costs",
      },
    ],
    ctaText: "View All Case Studies",
  }

  // CTA section configuration
  const ctaSectionProps = {
    title: "Ready to transform your organization?",
    subtitle:
      "Get started with AIFlow Enterprise today at just IDR 25,000 per user per month. Our team will help you customize and deploy AI assistants tailored to your organization's needs.",
    primaryButtonText: "Schedule a Demo",
    secondaryButtonText: "Contact Sales",
    warningText: "⚠️ Limited time offer — Lock in current pricing before our upcoming increase!",
  }

  // Footer configuration
  const footerLinks = [
    {
      category: "Product",
      items: [
        { label: "Features", href: "#" },
        { label: "Pricing", href: "#" },
        { label: "Security", href: "#" },
      ],
    },
    {
      category: "Company",
      items: [
        { label: "About", href: "#" },
        { label: "Customers", href: "#" },
        { label: "Contact", href: "#" },
      ],
    },
    {
      category: "Legal",
      items: [
        { label: "Privacy", href: "#" },
        { label: "Terms", href: "#" },
        { label: "DPA", href: "#" },
      ],
    },
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar
        title="AIFlow Enterprise"
        navLinks={navLinks}
        alternatePageLink={{ href: "/", label: "Individual" }}
      />

      <main className="flex-1">
        <HeroSection {...heroProps} />
        <TrustedBySection {...trustedByProps} />
        <FeaturesSection {...featuresProps} />
        <PricingSection {...pricingProps} />
        <SuccessStoriesSection {...successStoriesProps} />
        <EnterpriseCTASection {...ctaSectionProps} />
      </main>

      <Footer
        title="AIFlow Enterprise"
        description="Enterprise-grade AI assistants customized for your organization. Brand customization, usage monitoring, and complete control."
        links={footerLinks}
      />
    </div>
  )
}
