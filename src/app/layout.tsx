import { cookies } from "next/headers";
import { Suspense } from "react";
import { Analytics } from "@vercel/analytics/react";
import "@/styles/globals.css";
import defaultAppearanceSetting from "@/consts/defaultAppearanceSetting.json";
import { AppearanceSetting } from "@/model/appSetting";
import { Metadata } from "next";
import { ThemeStyle } from "@/components/ThemeStyle";
import { getAppearanceCssUrl } from "@/lib/get-appearance-css";
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: "Tomio AI",
  description:
    "Tomio AI - Revolutionizing the future with artificial intelligence solutions.",
  metadataBase: new URL("https://tomioai.com"),
  openGraph: {
    title: "Tomio AI",
    description:
      "Tomio AI - Revolutionizing the future with artificial intelligence solutions.",
    url: "https://tomioai.com",
    images: [
      {
        url: "https://tomioai.com/og-image.png",
        width: 1200,
        height: 630,
        alt: "Tomio AI - Revolutionizing the Future",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tomio AI",
    description:
      "Tomio AI - Revolutionizing the future with artificial intelligence solutions.",
    images: ["https://tomioai.com/og-image.png"],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const theme = cookieStore.get("theme")?.value ?? null;
  const appearanceId = cookieStore.get("appearance_id")?.value ?? null;

  const appearance = defaultAppearanceSetting as unknown as AppearanceSetting;

  return (
    <html id="root" lang="en" className={theme || undefined}>
      <head>
        <ThemeStyle appearance={appearance} />
        {appearanceId && (
          <link
            rel="stylesheet"
            href={getAppearanceCssUrl(appearanceId)}
            key="dynamic-appearance-css"
            id="dy"
          />
        )}
      </head>
      <body>
        <Suspense>{children}</Suspense>
        <Toaster />
        <Analytics />
      </body>
    </html>
  );
}
