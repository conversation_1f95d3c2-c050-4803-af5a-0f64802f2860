"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, Github, Check, AlertCircle, Loader2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function AddItemPage() {
  const [url, setUrl] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState("")
  const [itemPreview, setItemPreview] = useState<any>(null)

  const handleValidate = (e: React.FormEvent) => {
    e.preventDefault()
    setIsValidating(true)
    setError("")

    // Simulate GitHub URL validation
    setTimeout(() => {
      setIsValidating(false)

      // For demo purposes, show a preview if URL contains "github.com"
      if (url.includes("github.com")) {
        setItemPreview({
          name: url.split("/").pop() || "Repository Name",
          description: "A tool for enhancing LLM capabilities with advanced processing features.",
          author: url.split("/")[3] || "username",
          category: "Tools",
          compatibility: "v1.5+",
          lastUpdated: new Date().toISOString().split("T")[0],
        })
      } else {
        setError("Invalid GitHub URL. Please enter a valid GitHub repository URL.")
      }
    }, 2000)
  }

  const handleSubmit = () => {
    setIsSubmitting(true)

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      setIsSuccess(true)
    }, 2000)
  }

  return (
    <div className="container mx-auto py-6 max-w-3xl">
      <div className="flex items-center mb-6">
        <Link href="/marketplace">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Button>
        </Link>
      </div>

      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add GitHub Item</h1>
          <p className="text-muted-foreground mt-2">Add a single item from a GitHub repository to the marketplace.</p>
        </div>

        {isSuccess ? (
          <Alert className="bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-900">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle className="text-green-800 dark:text-green-300">Item Added Successfully</AlertTitle>
            <AlertDescription className="text-green-700 dark:text-green-400">
              The GitHub item has been added to the marketplace. It will be reviewed by our team before being made
              public.
              <div className="mt-4">
                <Link href="/marketplace">
                  <Button>Return to Marketplace</Button>
                </Link>
              </div>
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-8">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <h2 className="text-xl font-semibold">GitHub Repository URL</h2>

              <form onSubmit={handleValidate} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="url">Repository URL</Label>
                  <div className="flex items-center space-x-2">
                    <Github className="h-5 w-5 text-muted-foreground" />
                    <Input
                      id="url"
                      placeholder="https://github.com/username/repository"
                      required
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    The URL to the GitHub repository containing the item you want to add.
                  </p>
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={isValidating || !url.trim()}>
                    {isValidating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Validating...
                      </>
                    ) : (
                      "Validate Repository"
                    )}
                  </Button>
                </div>
              </form>
            </div>

            {itemPreview && (
              <>
                <Separator />

                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Repository Preview</h2>

                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>{itemPreview.name}</CardTitle>
                          <CardDescription>by {itemPreview.author}</CardDescription>
                        </div>
                        <Badge>{itemPreview.category}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p>{itemPreview.description}</p>
                      <div className="flex items-center gap-4 mt-4 text-sm text-muted-foreground">
                        <div>Last updated: {itemPreview.lastUpdated}</div>
                        <div>Compatibility: {itemPreview.compatibility}</div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <div className="text-sm text-muted-foreground">
                        <Github className="h-4 w-4 inline mr-1" />
                        {url}
                      </div>
                      <Button onClick={handleSubmit} disabled={isSubmitting}>
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Adding...
                          </>
                        ) : (
                          "Add to Marketplace"
                        )}
                      </Button>
                    </CardFooter>
                  </Card>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Repository Requirements</AlertTitle>
                    <AlertDescription>
                      To be added to the marketplace, the repository should include:
                      <ul className="list-disc pl-5 mt-2 space-y-1">
                        <li>
                          A <code>metadata.json</code> file with item details
                        </li>
                        <li>A README.md with installation and usage instructions</li>
                        <li>Compatibility information</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

