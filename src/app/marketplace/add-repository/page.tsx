"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft, <PERSON>ith<PERSON>, Check, AlertCircle } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function AddRepositoryPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError("")

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      // Randomly succeed or fail for demo purposes
      if (Math.random() > 0.3) {
        setIsSuccess(true)
      } else {
        setError("There was an error validating the repository. Please check the URL and try again.")
      }
    }, 2000)
  }

  return (
    <div className="container mx-auto py-6 max-w-3xl">
      <div className="flex items-center mb-6">
        <Link href="/marketplace">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Button>
        </Link>
      </div>

      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add Repository</h1>
          <p className="text-muted-foreground mt-2">
            Add your own repository to the marketplace to share your tools, prompts, and models with the community.
          </p>
        </div>

        {isSuccess ? (
          <Alert className="bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-900">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle className="text-green-800 dark:text-green-300">Repository Added Successfully</AlertTitle>
            <AlertDescription className="text-green-700 dark:text-green-400">
              Your repository has been added to the marketplace. It will be reviewed by our team before being made
              public.
              <div className="mt-4">
                <Link href="/marketplace">
                  <Button>Return to Marketplace</Button>
                </Link>
              </div>
            </AlertDescription>
          </Alert>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-8">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Repository Information</h2>

              <div className="space-y-2">
                <Label htmlFor="name">Repository Name</Label>
                <Input id="name" placeholder="My Awesome Tools" required />
                <p className="text-xs text-muted-foreground">A clear, descriptive name for your repository.</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="A collection of tools for enhancing productivity with LLMs."
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Describe what users can expect to find in your repository.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="url">Repository URL</Label>
                <div className="flex items-center space-x-2">
                  <Github className="h-5 w-5 text-muted-foreground" />
                  <Input
                    id="url"
                    placeholder="https://github.com/username/repository"
                    required
                    pattern="https:\/\/github\.com\/[\w-]+\/[\w-]+"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  The URL to your GitHub repository. Must be a public repository.
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Repository Structure</h2>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Repository Requirements</AlertTitle>
                <AlertDescription>
                  Your repository must follow our structure guidelines to be properly indexed:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>
                      Include a <code>manifest.json</code> file in the root directory
                    </li>
                    <li>
                      Each item must have its own directory with a <code>metadata.json</code> file
                    </li>
                    <li>All items must specify compatibility information</li>
                    <li>README.md file with installation and usage instructions</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="p-4 border rounded-lg bg-muted/30">
                <h3 className="text-lg font-medium mb-2">Example Repository Structure</h3>
                <pre className="text-sm overflow-x-auto p-2 bg-black/80 text-white rounded">
                  {`repository/
├── manifest.json
├── README.md
├── item1/
│   ├── metadata.json
│   ├── index.js
│   └── README.md
├── item2/
│   ├── metadata.json
│   ├── main.py
│   └── README.md
└── ...`}
                </pre>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Contact Information</h2>

              <div className="space-y-2">
                <Label htmlFor="email">Contact Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" required />
                <p className="text-xs text-muted-foreground">We'll use this to contact you about your repository.</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website (Optional)</Label>
                <Input id="website" type="url" placeholder="https://your-website.com" />
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Add Repository"}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}

