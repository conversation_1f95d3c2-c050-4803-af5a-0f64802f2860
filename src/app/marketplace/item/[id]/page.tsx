import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Download, Star, Clock, Calendar, User, Tag, Check, Info, Github, Play } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { DirectInstall } from "@/components/marketplace/direct-install"
import { ItemPreview } from "@/components/marketplace/item/[id]/item-preview"

interface MarketplaceItemPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function MarketplaceItemPage(props: MarketplaceItemPageProps) {
  const params = await props.params;
  // In a real application, you would fetch the item data based on the ID
  const item = marketplaceItems.find((item) => item.id === params.id) || marketplaceItems[0]

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center gap-2">
        <Link href="/marketplace">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Button>
        </Link>
      </div>

      <div className="grid md:grid-cols-3 gap-8">
        <div className="md:col-span-2 space-y-8">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold tracking-tight">{item.title}</h1>
              <Badge variant="default" className="text-sm">
                {item.category}
              </Badge>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center">
                <Star className="h-5 w-5 fill-primary text-primary mr-1" />
                <span className="font-medium">{item.rating}</span>
              </div>
              <div className="flex items-center text-muted-foreground">
                <Download className="h-4 w-4 mr-1" />
                {item.downloads.toLocaleString()} downloads
              </div>
              {item.githubUrl && (
                <div className="flex items-center text-muted-foreground">
                  <Github className="h-4 w-4 mr-1" />
                  <Link href={item.githubUrl} target="_blank" className="text-sm hover:underline">
                    View on GitHub
                  </Link>
                </div>
              )}
            </div>
            <p className="text-muted-foreground">{item.description}</p>
          </div>

          <div className="relative h-[300px] w-full rounded-lg overflow-hidden border">
            <Image src={item.image || "/placeholder.svg"} alt={item.title} fill className="object-cover" />
          </div>

          <Tabs defaultValue="description">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="installation">Installation</TabsTrigger>
              <TabsTrigger value="preview">Try It</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>
            <TabsContent value="description" className="p-4 border rounded-lg mt-2 space-y-4">
              <h2 className="text-xl font-semibold">About {item.title}</h2>
              <p>
                {item.title} is a powerful tool designed to enhance your LLM experience. It provides advanced
                functionality that allows you to work more efficiently and effectively with large language models.
              </p>
              <p>
                This {item.category.toLowerCase()} integrates seamlessly with your existing workflow, providing
                intuitive controls and powerful features that make it an essential addition to your toolkit.
              </p>
              <h3 className="text-lg font-semibold mt-6">Key Features</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Intuitive user interface for easy interaction</li>
                <li>Advanced customization options to suit your specific needs</li>
                <li>Seamless integration with existing workflows</li>
                <li>Regular updates and improvements based on user feedback</li>
                <li>Comprehensive documentation and support</li>
              </ul>
              <h3 className="text-lg font-semibold mt-6">Use Cases</h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>Streamline your content creation process</li>
                <li>Enhance your data analysis capabilities</li>
                <li>Improve the accuracy and relevance of your LLM outputs</li>
                <li>Automate repetitive tasks to save time and effort</li>
              </ul>

              {item.githubUrl && (
                <>
                  <h3 className="text-lg font-semibold mt-6">GitHub Repository</h3>
                  <p>
                    This item is hosted on GitHub. You can view the source code, contribute, or report issues by
                    visiting the repository.
                  </p>
                  <div className="mt-2">
                    <Link href={item.githubUrl} target="_blank">
                      <Button variant="primary-outline" size="sm">
                        <Github className="h-4 w-4 mr-2" />
                        View Repository
                      </Button>
                    </Link>
                  </div>
                </>
              )}
            </TabsContent>
            <TabsContent value="installation" className="p-4 border rounded-lg mt-2 space-y-4">
              <h2 className="text-xl font-semibold">Installation Instructions</h2>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg bg-muted/50">
                  <h3 className="text-lg font-medium mb-2">Option 1: Direct Installation</h3>
                  <p className="mb-2">
                    Click the "Install Now" button to install this item directly to your local environment.
                  </p>
                  <DirectInstall item={item} />
                </div>

                <div className="p-4 border rounded-lg bg-muted/50">
                  <h3 className="text-lg font-medium mb-2">Option 2: Manual Installation</h3>
                  <p className="mb-2">Download the package and import it manually:</p>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>
                      <p>Download the package</p>
                      <Button className="mt-1">
                        <Download className="h-4 w-4 mr-2" />
                        Download Package
                      </Button>
                    </li>
                    <li>Open your LLM client application and navigate to the "Extensions" or "Plugins" section.</li>
                    <li>Select "Import" and choose the downloaded package file.</li>
                    <li>Configure the settings according to your preferences.</li>
                    <li>Start using the item from the main interface of your application.</li>
                  </ol>
                </div>
              </div>

              {item.githubUrl && (
                <div className="mt-6 p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
                  <div className="flex items-start">
                    <Github className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-1">GitHub Installation</h3>
                      <p className="text-blue-700 dark:text-blue-400 mb-2">
                        Alternatively, you can install this item directly from GitHub using the following command:
                      </p>
                      <div className="bg-black/80 text-white p-3 rounded font-mono text-sm overflow-x-auto">
                        llm-client install {item.githubUrl.replace("https://github.com/", "")}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-6 p-4 border rounded-lg bg-yellow-50 dark:bg-yellow-950/20">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-300 mb-1">
                      Compatibility Note
                    </h3>
                    <p className="text-yellow-700 dark:text-yellow-400">
                      This item is compatible with client application version {item.compatibility} and above. Please
                      ensure your application is updated to the latest version for the best experience.
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="preview" className="p-4 border rounded-lg mt-2 space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Try Before You Install</h2>
                <Button size="sm" variant="primary-outline">
                  <Play className="h-4 w-4 mr-2" />
                  Run Demo
                </Button>
              </div>

              <div className="border rounded-lg overflow-hidden">
                <ItemPreview item={item} />
              </div>

              <div className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20 mt-4">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                  <div>
                    <h3 className="text-lg font-medium text-blue-800 dark:text-blue-300 mb-1">Preview Mode</h3>
                    <p className="text-blue-700 dark:text-blue-400">
                      This is a limited preview of the item's functionality. Some features may be restricted or
                      unavailable in preview mode. Install the full version to access all features.
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="reviews" className="p-4 border rounded-lg mt-2 space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">User Reviews</h2>
                <Button size="sm">Write a Review</Button>
              </div>
              <div className="space-y-4">
                {reviews.map((review) => (
                  <div key={review.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{review.user}</p>
                          <p className="text-xs text-muted-foreground">{review.date}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? "fill-primary text-primary" : "fill-muted text-muted-foreground"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <p className="text-sm">{review.comment}</p>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <div className="border rounded-lg p-6 space-y-6">
            <DirectInstall item={item} fullWidth />
            {item.githubUrl && (
              <Button className="w-full" variant="primary-outline" size="lg">
                <Play className="h-4 w-4 mr-2" />
                Try It First
              </Button>
            )}
            <Separator />
            <div className="space-y-4">
              <h3 className="font-semibold">Item Details</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center text-muted-foreground">
                  <Calendar className="h-4 w-4 mr-2" />
                  Last Updated
                </div>
                <div>{new Date(item.lastUpdate).toLocaleDateString()}</div>
                <div className="flex items-center text-muted-foreground">
                  <Tag className="h-4 w-4 mr-2" />
                  Category
                </div>
                <div>{item.category}</div>
                <div className="flex items-center text-muted-foreground">
                  <Check className="h-4 w-4 mr-2" />
                  Compatibility
                </div>
                <div>{item.compatibility}</div>
                <div className="flex items-center text-muted-foreground">
                  <Download className="h-4 w-4 mr-2" />
                  Downloads
                </div>
                <div>{item.downloads.toLocaleString()}</div>
                <div className="flex items-center text-muted-foreground">
                  <Clock className="h-4 w-4 mr-2" />
                  Version
                </div>
                <div>1.2.0</div>
                {item.repository && (
                  <>
                    <div className="flex items-center text-muted-foreground">
                      <Github className="h-4 w-4 mr-2" />
                      Repository
                    </div>
                    <div>{item.repository}</div>
                  </>
                )}
              </div>
            </div>
            <Separator />
            <div className="space-y-4">
              <h3 className="font-semibold">Developer Information</h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{item.developer}</span>
                </div>
                <Button variant="primary-outline" size="sm" className="w-full">
                  View Developer Profile
                </Button>
                <Button variant="ghost" size="sm" className="w-full">
                  Contact Developer
                </Button>
              </div>
            </div>
            <Separator />
            <div className="space-y-4">
              <h3 className="font-semibold">Related Items</h3>
              <div className="space-y-3">
                {relatedItems.map((relatedItem) => (
                  <Link
                    key={relatedItem.id}
                    href={`/marketplace/item/${relatedItem.id}`}
                    className="flex items-center p-2 hover:bg-muted rounded-lg transition-colors"
                  >
                    <div className="relative h-10 w-10 rounded overflow-hidden mr-3">
                      <Image
                        src={relatedItem.image || "/placeholder.svg"}
                        alt={relatedItem.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex-grow">
                      <p className="text-sm font-medium line-clamp-1">{relatedItem.title}</p>
                      <p className="text-xs text-muted-foreground">{relatedItem.category}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const marketplaceItems = [
  {
    id: "1",
    title: "Advanced Summarizer",
    description: "Automatically summarize long documents with customizable length and focus areas.",
    category: "Tools",
    developer: "AI Tools Inc.",
    lastUpdate: "2025-03-15",
    compatibility: "v2.0+",
    rating: 4.8,
    downloads: 12500,
    image: "/placeholder.svg?height=600&width=800",
    repository: "official",
  },
  {
    id: "2",
    title: "Code Explainer",
    description: "Get detailed explanations of code snippets in multiple programming languages.",
    category: "Tools",
    developer: "DevAI Solutions",
    lastUpdate: "2025-03-10",
    compatibility: "v1.5+",
    rating: 4.6,
    downloads: 8900,
    image: "/placeholder.svg?height=600&width=800",
    repository: "official",
    githubUrl: "https://github.com/devai-solutions/code-explainer",
  },
  {
    id: "3",
    title: "Creative Writing Assistant",
    description: "Generate creative content with customizable tone, style, and format options.",
    category: "Prompts",
    developer: "WriteRight",
    lastUpdate: "2025-03-05",
    compatibility: "v2.0+",
    rating: 4.9,
    downloads: 15600,
    image: "/placeholder.svg?height=600&width=800",
    repository: "official",
  },
]

const relatedItems = [
  {
    id: "2",
    title: "Code Explainer",
    category: "Tools",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "3",
    title: "Creative Writing Assistant",
    category: "Prompts",
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "4",
    title: "Data Analyzer",
    category: "Tools",
    image: "/placeholder.svg?height=100&width=100",
  },
]

const reviews = [
  {
    id: "1",
    user: "Alex Johnson",
    date: "March 20, 2025",
    rating: 5,
    comment:
      "This tool has completely transformed my workflow. The interface is intuitive, and the results are consistently impressive. Highly recommended for anyone working with large documents!",
  },
  {
    id: "2",
    user: "Sam Taylor",
    date: "March 15, 2025",
    rating: 4,
    comment:
      "Very useful tool with great features. The only reason I'm not giving it 5 stars is because of some minor performance issues when processing very large documents. Otherwise, it's excellent!",
  },
  {
    id: "3",
    user: "Jamie Rivera",
    date: "March 10, 2025",
    rating: 5,
    comment:
      "Absolutely love this tool! It has saved me countless hours of work and the results are always spot on. The developer is also very responsive to feedback and questions.",
  },
]

