import Link from "next/link";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Plus,
  Github,
  Download,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { InstallFromGithub } from "@/components/marketplace/install-from-github";
import { MarketplaceItem } from "@/components/marketplace/marketplace-item";
import { MarketplaceItemList } from "@/components/marketplace/marketplace-item-list";

export default function MarketplacePage() {
  return (
    <div className="container mx-auto py-6 space-y-8 px-4">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight">Marketplace</h1>
          <div className="flex flex-col sm:flex-row gap-2">
            <Link href="/marketplace/add-repository">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Repository
              </Button>
            </Link>
            <Link href="/marketplace/add-item">
              <Button variant="primary-outline">
                <Github className="h-4 w-4 mr-2" />
                Add GitHub Item
              </Button>
            </Link>
          </div>
        </div>
        <p className="text-muted-foreground">
          Browse, search, and install tools, prompts, and models to enhance your
          LLM experience.
        </p>
      </div>

      <div className="border rounded-lg">
        <div className="px-4 py-2 border-b">
          <h2 className="text-xl font-semibold">Popular Categories</h2>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 p-4">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/marketplace?category=${category.id}`}
              className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-muted transition-colors"
            >
              <div className="p-2 rounded-full bg-primary/10 mb-2">
                {category.icon}
              </div>
              <span className="text-sm font-medium">{category.name}</span>
              <span className="text-xs text-muted-foreground">
                {category.count} items
              </span>
            </Link>
          ))}
        </div>
      </div>

      <InstallFromGithub />

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="flex w-full max-w-sm items-center space-x-2">
          <Input
            type="search"
            placeholder="Search marketplace..."
            className="w-full"
          />
          <Button type="submit" size="icon" variant="ghost">
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="primary-outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="tools">Tools</SelectItem>
              <SelectItem value="prompts">Prompts</SelectItem>
              <SelectItem value="models">Models</SelectItem>
              <SelectItem value="extensions">Extensions</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="border rounded-lg">
        <Tabs defaultValue="grid" className="w-full">
          <div className="flex items-center justify-between px-4 py-2 border-b">
            <h2 className="text-xl font-semibold">Featured Items</h2>
            <TabsList>
              <TabsTrigger value="grid">
                <Grid3X3 className="h-4 w-4 mr-2" />
                Grid
              </TabsTrigger>
              <TabsTrigger value="list">
                <List className="h-4 w-4 mr-2" />
                List
              </TabsTrigger>
            </TabsList>
          </div>
          <TabsContent value="grid" className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {marketplaceItems.map((item) => (
                <MarketplaceItem key={item.id} item={item} />
              ))}
            </div>
          </TabsContent>
          <TabsContent value="list" className="p-4">
            <div className="space-y-4">
              {marketplaceItems.map((item) => (
                <MarketplaceItemList key={item.id} item={item} />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <div className="border rounded-lg">
        <div className="px-4 py-2 border-b">
          <h2 className="text-xl font-semibold">Community Repositories</h2>
        </div>
        <div className="p-4 space-y-4">
          {repositories.map((repo) => (
            <div key={repo.id} className="border rounded-lg p-4">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold">{repo.name}</h3>
                    {repo.verified && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full dark:bg-green-900 dark:text-green-100">
                        Verified
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {repo.description}
                  </p>
                  <div className="flex items-center gap-4 mt-2">
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Github className="h-3 w-3 mr-1" />
                      {repo.owner}
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <span>Items: {repo.itemCount}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Link href={`/marketplace/repository/${repo.id}`}>
                    <Button variant="primary-outline" size="sm">
                      View Repository
                    </Button>
                  </Link>
                  <Button size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Install All
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

const categories = [
  {
    id: "tools",
    name: "Tools",
    count: 42,
    icon: <Search className="h-5 w-5 text-primary" />,
  },
  {
    id: "prompts",
    name: "Prompts",
    count: 128,
    icon: <Filter className="h-5 w-5 text-primary" />,
  },
  {
    id: "models",
    name: "Models",
    count: 36,
    icon: <Grid3X3 className="h-5 w-5 text-primary" />,
  },
  {
    id: "extensions",
    name: "Extensions",
    count: 64,
    icon: <List className="h-5 w-5 text-primary" />,
  },
  {
    id: "workflows",
    name: "Workflows",
    count: 28,
    icon: <Search className="h-5 w-5 text-primary" />,
  },
  {
    id: "templates",
    name: "Templates",
    count: 53,
    icon: <Filter className="h-5 w-5 text-primary" />,
  },
];

const marketplaceItems = [
  {
    id: "1",
    title: "Advanced Summarizer",
    description:
      "Automatically summarize long documents with customizable length and focus areas.",
    category: "Tools",
    developer: "AI Tools Inc.",
    lastUpdate: "2025-03-15",
    compatibility: "v2.0+",
    rating: 4.8,
    downloads: 12500,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "2",
    title: "Code Explainer",
    description:
      "Get detailed explanations of code snippets in multiple programming languages.",
    category: "Tools",
    developer: "DevAI Solutions",
    lastUpdate: "2025-03-10",
    compatibility: "v1.5+",
    rating: 4.6,
    downloads: 8900,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
    githubUrl: "https://github.com/devai-solutions/code-explainer",
  },
  {
    id: "3",
    title: "Creative Writing Assistant",
    description:
      "Generate creative content with customizable tone, style, and format options.",
    category: "Prompts",
    developer: "WriteRight",
    lastUpdate: "2025-03-05",
    compatibility: "v2.0+",
    rating: 4.9,
    downloads: 15600,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "4",
    title: "Data Analyzer",
    description:
      "Extract insights from datasets with automated analysis and visualization.",
    category: "Tools",
    developer: "DataSense",
    lastUpdate: "2025-02-28",
    compatibility: "v1.8+",
    rating: 4.7,
    downloads: 7300,
    image: "/placeholder.svg?height=200&width=200",
    repository: "community-tools",
  },
  {
    id: "5",
    title: "Language Translator Pro",
    description:
      "Translate text between 50+ languages with context-aware accuracy.",
    category: "Models",
    developer: "LinguaTech",
    lastUpdate: "2025-02-20",
    compatibility: "v1.5+",
    rating: 4.5,
    downloads: 21000,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "6",
    title: "Research Assistant",
    description:
      "Find, summarize, and cite academic papers and research materials.",
    category: "Extensions",
    developer: "ScholarAI",
    lastUpdate: "2025-02-15",
    compatibility: "v2.0+",
    rating: 4.8,
    downloads: 9800,
    image: "/placeholder.svg?height=200&width=200",
    repository: "academic-tools",
  },
  {
    id: "7",
    title: "Email Composer",
    description: "Draft professional emails with customizable tone and intent.",
    category: "Prompts",
    developer: "CommunicateAI",
    lastUpdate: "2025-02-10",
    compatibility: "v1.8+",
    rating: 4.6,
    downloads: 18500,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "8",
    title: "Image Descriptor",
    description:
      "Generate detailed descriptions of images for accessibility and content creation.",
    category: "Models",
    developer: "VisionAI",
    lastUpdate: "2025-02-05",
    compatibility: "v2.0+",
    rating: 4.7,
    downloads: 6200,
    image: "/placeholder.svg?height=200&width=200",
    repository: "community-tools",
  },
];

const repositories = [
  {
    id: "official",
    name: "Official Repository",
    description:
      "The official collection of tools, prompts, and models maintained by our team.",
    owner: "LLM-App-Team",
    itemCount: 42,
    verified: true,
    githubUrl: "https://github.com/llm-app-team/official-repository",
  },
  {
    id: "community-tools",
    name: "Community Tools",
    description:
      "A collection of community-contributed tools and utilities for enhancing your LLM experience.",
    owner: "LLM-Community",
    itemCount: 28,
    verified: true,
    githubUrl: "https://github.com/llm-community/community-tools",
  },
  {
    id: "academic-tools",
    name: "Academic Research Tools",
    description: "Specialized tools for academic research and paper analysis.",
    owner: "ResearchAI-Team",
    itemCount: 15,
    verified: false,
    githubUrl: "https://github.com/researchai-team/academic-tools",
  },
  {
    id: "prompt-library",
    name: "Prompt Engineering Library",
    description:
      "A comprehensive collection of prompts for various use cases and domains.",
    owner: "PromptEngineers",
    itemCount: 76,
    verified: false,
    githubUrl: "https://github.com/promptengineers/prompt-library",
  },
];
