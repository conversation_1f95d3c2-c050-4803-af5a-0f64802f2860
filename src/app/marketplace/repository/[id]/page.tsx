import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Github, Download, Grid3X3, List, Info } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { MarketplaceItem } from "@/components/marketplace/marketplace-item"
import { MarketplaceItemList } from "@/components/marketplace/marketplace-item-list"

interface RepositoryPageProps {
  params: {
    id: string
  }
}

export default function RepositoryPage({ params }: RepositoryPageProps) {
  // In a real application, you would fetch the repository data based on the ID
  const repository = repositories.find((repo) => repo.id === params.id) || repositories[0]

  // Filter items that belong to this repository
  const repositoryItems = marketplaceItems.filter((item) => item.repository === repository.id)

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center gap-2">
        <Link href="/marketplace">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Button>
        </Link>
      </div>

      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold tracking-tight">{repository.name}</h1>
              {repository.verified && (
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full dark:bg-green-900 dark:text-green-100">
                  Verified
                </span>
              )}
            </div>
            <p className="text-muted-foreground">{repository.description}</p>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Github className="h-4 w-4 mr-1" />
                {repository.owner}
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <span>Items: {repository.itemCount}</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Link href={repository.githubUrl} target="_blank">
              <Button variant="primary-outline">
                <Github className="h-4 w-4 mr-2" />
                View on GitHub
              </Button>
            </Link>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Install All Items
            </Button>
          </div>
        </div>

        <Separator />

        <div className="grid md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-6">
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">About this Repository</h2>
              <p>
                {repository.name} is a collection of {repository.itemCount} items designed to enhance your LLM
                experience. This repository is maintained by {repository.owner} and includes a variety of tools,
                prompts, and models that can be installed individually or as a complete collection.
              </p>

              {repository.readme && (
                <div className="p-4 border rounded-lg bg-muted/30">
                  <h3 className="text-lg font-medium mb-2">README</h3>
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <div dangerouslySetInnerHTML={{ __html: repository.readme }} />
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Repository Items</h2>

              <Tabs defaultValue="grid" className="w-full">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">Showing {repositoryItems.length} items</div>
                  <TabsList>
                    <TabsTrigger value="grid">
                      <Grid3X3 className="h-4 w-4 mr-2" />
                      Grid
                    </TabsTrigger>
                    <TabsTrigger value="list">
                      <List className="h-4 w-4 mr-2" />
                      List
                    </TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="grid" className="mt-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {repositoryItems.map((item) => (
                      <MarketplaceItem key={item.id} item={item} />
                    ))}
                  </div>
                </TabsContent>
                <TabsContent value="list" className="mt-4">
                  <div className="space-y-4">
                    {repositoryItems.map((item) => (
                      <MarketplaceItemList key={item.id} item={item} />
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          <div className="space-y-6">
            <div className="border rounded-lg p-6 space-y-6">
              <div className="space-y-4">
                <h3 className="font-semibold">Repository Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Owner:</span>
                    <span>{repository.owner}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Items:</span>
                    <span>{repository.itemCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span>{repository.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Updated:</span>
                    <span>{repository.lastUpdated}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">License:</span>
                    <span>{repository.license}</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="font-semibold">Categories</h3>
                <div className="flex flex-wrap gap-2">
                  {repository.categories.map((category) => (
                    <Badge key={category} variant="default">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="font-semibold">Installation</h3>
                <div className="space-y-2">
                  <p className="text-sm">Install via command line:</p>
                  <div className="bg-black/80 text-white p-3 rounded font-mono text-xs overflow-x-auto">
                    llm-client install {repository.owner}/{repository.id}
                  </div>
                </div>
              </div>

              {repository.contributors && repository.contributors.length > 0 && (
                <>
                  <Separator />

                  <div className="space-y-4">
                    <h3 className="font-semibold">Contributors</h3>
                    <div className="flex flex-wrap gap-2">
                      {repository.contributors.map((contributor) => (
                        <div key={contributor.username} className="flex items-center gap-2 p-2 border rounded-lg">
                          <div className="relative h-6 w-6 rounded-full overflow-hidden">
                            <Image
                              src={contributor.avatar || "/placeholder.svg?height=24&width=24"}
                              alt={contributor.username}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <span className="text-sm">{contributor.username}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>

            {!repository.verified && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Community Repository</AlertTitle>
                <AlertDescription>
                  This is a community-contributed repository. Items in this repository have not been verified by our
                  team. Use caution when installing items from unverified sources.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

const repositories = [
  {
    id: "official",
    name: "Official Repository",
    description: "The official collection of tools, prompts, and models maintained by our team.",
    owner: "LLM-App-Team",
    itemCount: 42,
    verified: true,
    githubUrl: "https://github.com/llm-app-team/official-repository",
    created: "January 15, 2025",
    lastUpdated: "April 2, 2025",
    license: "MIT",
    categories: ["Tools", "Prompts", "Models", "Extensions"],
    contributors: [
      { username: "alex-dev", avatar: "/placeholder.svg?height=24&width=24" },
      { username: "sarah-ai", avatar: "/placeholder.svg?height=24&width=24" },
      { username: "mike-prompt", avatar: "/placeholder.svg?height=24&width=24" },
    ],
    readme: `
      <h1>Official LLM Tools Repository</h1>
      <p>This repository contains a collection of official tools, prompts, and models for enhancing your LLM experience.</p>
      <h2>Installation</h2>
      <p>You can install the entire repository or individual items:</p>
      <pre><code>llm-client install LLM-App-Team/official-repository</code></pre>
      <h2>Contributing</h2>
      <p>We welcome contributions! Please see our contribution guidelines for more information.</p>
    `,
  },
  {
    id: "community-tools",
    name: "Community Tools",
    description: "A collection of community-contributed tools and utilities for enhancing your LLM experience.",
    owner: "LLM-Community",
    itemCount: 28,
    verified: true,
    githubUrl: "https://github.com/llm-community/community-tools",
    created: "February 10, 2025",
    lastUpdated: "March 28, 2025",
    license: "Apache 2.0",
    categories: ["Tools", "Utilities", "Extensions"],
    contributors: [
      { username: "community-lead", avatar: "/placeholder.svg?height=24&width=24" },
      { username: "tool-builder", avatar: "/placeholder.svg?height=24&width=24" },
      { username: "extension-dev", avatar: "/placeholder.svg?height=24&width=24" },
    ],
  },
  {
    id: "academic-tools",
    name: "Academic Research Tools",
    description: "Specialized tools for academic research and paper analysis.",
    owner: "ResearchAI-Team",
    itemCount: 15,
    verified: false,
    githubUrl: "https://github.com/researchai-team/academic-tools",
    created: "March 5, 2025",
    lastUpdated: "April 1, 2025",
    license: "MIT",
    categories: ["Research", "Academic", "Analysis"],
  },
  {
    id: "prompt-library",
    name: "Prompt Engineering Library",
    description: "A comprehensive collection of prompts for various use cases and domains.",
    owner: "PromptEngineers",
    itemCount: 76,
    verified: false,
    githubUrl: "https://github.com/promptengineers/prompt-library",
    created: "January 20, 2025",
    lastUpdated: "March 25, 2025",
    license: "CC BY-SA 4.0",
    categories: ["Prompts", "Templates", "Examples"],
  },
]

const marketplaceItems = [
  {
    id: "1",
    title: "Advanced Summarizer",
    description: "Automatically summarize long documents with customizable length and focus areas.",
    category: "Tools",
    developer: "AI Tools Inc.",
    lastUpdate: "2025-03-15",
    compatibility: "v2.0+",
    rating: 4.8,
    downloads: 12500,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "2",
    title: "Code Explainer",
    description: "Get detailed explanations of code snippets in multiple programming languages.",
    category: "Tools",
    developer: "DevAI Solutions",
    lastUpdate: "2025-03-10",
    compatibility: "v1.5+",
    rating: 4.6,
    downloads: 8900,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
    githubUrl: "https://github.com/devai-solutions/code-explainer",
  },
  {
    id: "3",
    title: "Creative Writing Assistant",
    description: "Generate creative content with customizable tone, style, and format options.",
    category: "Prompts",
    developer: "WriteRight",
    lastUpdate: "2025-03-05",
    compatibility: "v2.0+",
    rating: 4.9,
    downloads: 15600,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "4",
    title: "Data Analyzer",
    description: "Extract insights from datasets with automated analysis and visualization.",
    category: "Tools",
    developer: "DataSense",
    lastUpdate: "2025-02-28",
    compatibility: "v1.8+",
    rating: 4.7,
    downloads: 7300,
    image: "/placeholder.svg?height=200&width=200",
    repository: "community-tools",
  },
  {
    id: "5",
    title: "Language Translator Pro",
    description: "Translate text between 50+ languages with context-aware accuracy.",
    category: "Models",
    developer: "LinguaTech",
    lastUpdate: "2025-02-20",
    compatibility: "v1.5+",
    rating: 4.5,
    downloads: 21000,
    image: "/placeholder.svg?height=200&width=200",
    repository: "official",
  },
  {
    id: "6",
    title: "Research Assistant",
    description: "Find, summarize, and cite academic papers and research materials.",
    category: "Extensions",
    developer: "ScholarAI",
    lastUpdate: "2025-02-15",
    compatibility: "v2.0+",
    rating: 4.8,
    downloads: 9800,
    image: "/placeholder.svg?height=200&width=200",
    repository: "academic-tools",
  },
]

