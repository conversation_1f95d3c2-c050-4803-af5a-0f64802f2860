import { Model } from "@/model/ai_model";

export const tomio_model: Model = {
  id: "tomio-model",
  name: "<PERSON><PERSON>",
  vendor: "TomioAI",
  endpoint: "/api/v1/chat/completions",
  headers: {},
  keepLocal: { required: [], optional: [] },
  features: {
    plugin: true,
    vision: false,
  },
  config: {
    model: "tomio-gpt-4",
    systemInstruction: "You are <PERSON><PERSON>, a knowledgeable assistant.",
    streamResponse: true,
    contextLimit: 8192
  },
};
