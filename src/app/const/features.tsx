import { Feature } from "@/components/FeatureList";
import {
  IconTimeDuration0 as Timer,
  IconGauge as GaugeCircle,
  IconBarrierBlock as Blocks,
  IconKey as KeyRound,
  IconRobot as Bot,
  IconBook as BookOpenText,
} from "@tabler/icons-react";


export const features: Feature[] = [
  {
    id: 1,
    icon: <Timer stroke="#4F46E5" />,
    text: "Easy to use, no usage limit",
  },
  {
    id: 2,
    icon: <KeyRound stroke="#4F46E5" />,
    text: "Use your own API key",
  },
  {
    id: 3,
    icon: <GaugeCircle stroke="#4F46E5" />,
    text: "Usage Dashboard",
  },
  {
    id: 4,
    icon: <Bot stroke="#4F46E5" />,
    text: "Prompt library, AI characters",
  },
  {
    id: 5,
    icon: <BookOpenText stroke="#4F46E5" />,
    text: "Knowledge Center",
  },
  {
    id: 6,
    icon: <Blocks stroke="#4F46E5" />,
    text: "Unlimited Plugins",
  },
];
