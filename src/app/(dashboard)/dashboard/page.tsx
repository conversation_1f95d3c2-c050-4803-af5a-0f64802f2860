import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import Link from "next/link";
import type { Metadata } from "next";

import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import DashboardHomePage from "./internal/Home";

export const metadata: Metadata = {
  title: "Dashboard",
  description: "Your dashboard",
};

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect("/login");
  }

  const user = await db.user.findUnique({
    where: {
      id: session.user.id,
    },
    include: {
      profile: true,
    },
  });

  if (!user) {
    redirect("/login");
  }

  if (!user.profile?.onboarded) {
    redirect("/onboarding");
  }

  return (
    <div className="container mx-auto py-10">
      <DashboardHomePage />
    </div>
  );
}
