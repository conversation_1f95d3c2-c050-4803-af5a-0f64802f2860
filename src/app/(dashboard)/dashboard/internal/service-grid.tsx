"use client"

import type React from "react"

import { <PERSON><PERSON><PERSON>, <PERSON>, Clock } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

// Service types
type ServiceStatus = "Available" | "Beta" | "Coming Soon"

interface Service {
  id: string
  title: string
  description: string
  status: ServiceStatus
  link: string
  icon: React.ReactNode
}

export default function ServiceGrid() {
  // Sample services data
  const services: Service[] = [
    {
      id: "skill-test",
      title: "Skill Test",
      description: "Try your skills and earn points or badges.",
      status: "Available",
      link: "/test",
      icon: <div className="bg-green-100 p-3 rounded-full text-green-600">✅</div>,
    },
    {
      id: "create-test",
      title: "Create Your Own Test",
      description: "Build a test to share with your team or audience.",
      status: "Beta",
      link: "/beta/create-test",
      icon: <div className="bg-blue-100 p-3 rounded-full text-blue-600">🔨</div>,
    },
    {
      id: "leaderboard",
      title: "Leaderboard",
      description: "Compete with others and climb the ranks.",
      status: "Coming Soon",
      link: "#",
      icon: <div className="bg-gray-100 p-3 rounded-full text-gray-600">⏳</div>,
    },
    {
      id: "super-review",
      title: "Super Review",
      description: "Get Senior feedback on your performance + AI analysis.",
      status: "Coming Soon",
      link: "#",
      icon: <div className="bg-gray-100 p-3 rounded-full text-gray-600">🧠</div>,
    },
  ]

  // Render a service card based on its status
  const renderServiceCard = (service: Service) => {
    const isAvailable = service.status === "Available"
    const isBeta = service.status === "Beta"
    const isComingSoon = service.status === "Coming Soon"

    return (
      <Card
        key={service.id}
        className={`transition-all ${isComingSoon ? "opacity-70" : "hover:shadow-md hover:border-indigo-200"}`}
      >
        <CardHeader className="flex flex-row items-center gap-4">
          {service.icon}
          <div>
            <CardTitle className="flex items-center gap-2">
              {service.title}
              {isBeta && (
                <Badge variant="primary-outline" className="ml-2 bg-blue-50 text-blue-700 border-blue-200">
                  Beta
                </Badge>
              )}
              {isComingSoon && (
                <Badge variant="primary-outline" className="ml-2 bg-gray-50 text-gray-500 border-gray-200">
                  Coming Soon
                </Badge>
              )}
            </CardTitle>
            <CardDescription>{service.description}</CardDescription>
          </div>
        </CardHeader>
        <CardFooter>
          {isAvailable && (
            <Button asChild className="w-full">
              <Link href={service.link}>
                Start Now <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          )}
          {isBeta && (
            <Button asChild variant="primary-outline" className="w-full">
              <Link href={service.link}>
                Request Access <Lock className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          )}
          {isComingSoon && (
            <Button disabled variant="primary-outline" className="w-full">
              Coming Soon <Clock className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
    )
  }

  return (
    <section>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Your Services</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">{services.map(renderServiceCard)}</div>
    </section>
  )
}
