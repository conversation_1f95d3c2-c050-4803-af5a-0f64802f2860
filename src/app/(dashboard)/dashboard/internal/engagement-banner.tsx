import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, Circle } from "lucide-react"

export default function EngagementBanner() {
  // This would come from your user data in a real app
  const userProgress = {
    profileComplete: true,
    dailyStreak: 3,
    nextFeatures: [
      { name: "Skill Test", status: "done" },
      { name: "Leaderboard", status: "in-progress" },
      { name: "Super Review", status: "planned" },
    ],
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Profile Completion */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-100">
        <CardContent className="p-4 flex items-center justify-between">
          <div>
            <h3 className="font-medium text-green-800">Profile Status</h3>
            <p className="text-sm text-green-700">
              {userProgress.profileComplete ? "Your profile is complete!" : "Complete your profile to earn 50 points"}
            </p>
          </div>
          {userProgress.profileComplete ? (
            <div className="bg-green-100 text-green-700 rounded-full p-2">
              <CheckCircle className="h-5 w-5" />
            </div>
          ) : (
            <Button size="sm" className="bg-green-600 hover:bg-green-700">
              Complete Now
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Daily Streak */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Daily Streak</h3>
              <p className="text-sm text-muted-foreground">
                You've logged in {userProgress.dailyStreak} days in a row!
              </p>
            </div>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((day) => (
                <div
                  key={day}
                  className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    day <= userProgress.dailyStreak ? "bg-indigo-100 text-indigo-600" : "bg-gray-100 text-gray-400"
                  }`}
                >
                  {day}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* What's Next Timeline */}
      <Card className="md:col-span-2">
        <CardContent className="p-4">
          <h3 className="font-medium mb-3">What's Next</h3>
          <div className="space-y-3">
            {userProgress.nextFeatures.map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                {feature.status === "done" ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : feature.status === "in-progress" ? (
                  <Circle className="h-5 w-5 text-blue-500 animate-pulse" />
                ) : (
                  <Circle className="h-5 w-5 text-gray-300" />
                )}
                <div className="flex-1">
                  <p className="text-sm font-medium">{feature.name}</p>
                </div>
                <div>
                  <Badge
                    variant="primary-outline"
                    className={`
                      ${feature.status === "done" ? "bg-green-50 text-green-700 border-green-200" : ""}
                      ${feature.status === "in-progress" ? "bg-blue-50 text-blue-700 border-blue-200" : ""}
                      ${feature.status === "planned" ? "bg-gray-50 text-gray-500 border-gray-200" : ""}
                    `}
                  >
                    {feature.status === "done" ? "Done" : feature.status === "in-progress" ? "In Progress" : "Planned"}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
