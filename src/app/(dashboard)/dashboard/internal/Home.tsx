// app/dashboard/home/<USER>
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";

import { UserCircle } from "lucide-react";
import ServiceGrid from "./service-grid";
import ReferralSection from "./referral-section";
import ChangelogSection from "./changelog-section";
import FeedbackWidget from "./feedback-widget";
import EngagementBanner from "./engagement-banner";
import { Badge } from "@/components/ui/badge";
import { getUserDashboardData } from "@/lib/db_call/getDashboardData";

export default async function DashboardHomePage() {
  const session = await getServerSession(authOptions);

  const userEmail = session?.user.email || null;
  if (!userEmail) {
    return null;
  }

  const userData = await getUserDashboardData(userEmail);
  if (!userData) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Hero Banner */}
      <div className="rounded-xl bg-gradient-to-r from-indigo-500 to-purple-600 p-8 mb-8 text-white shadow-lg relative overflow-hidden">
        <div className="absolute inset-0 bg-white/5 backdrop-blur-sm"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">
                Welcome, {userData.name} 👋
              </h1>
              <p className="text-indigo-100 mb-4">
                Thanks for being one of our first users!
              </p>
              <div className="flex items-center gap-4">
                <Badge className="bg-white/20 hover:bg-white/30 text-white border border-white/40 px-3 py-1 flex items-center gap-1">
                  <span className="animate-pulse mr-1">🌟</span>{" "}
                  {userData.badge || "Early Adopter"}
                </Badge>
                <div className="bg-white/20 rounded-full px-4 py-1 text-sm flex items-center">
                  <span className="mr-1">🏆</span> {userData.points} Points
                </div>
              </div>
            </div>
            <div className="hidden md:flex h-16 w-16 rounded-full bg-white/20 items-center justify-center">
              <UserCircle className="h-10 w-10 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Services */}
        <div className="lg:col-span-2 space-y-8">
          <ServiceGrid />
          <EngagementBanner />
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-8">
          <ReferralSection
            points={userData.referralPoints}
            referralCode={userData.referralCode}
          />
          <ChangelogSection />
          <FeedbackWidget />
        </div>
      </div>
    </div>
  );
}
