"use client"

import { useState } from "react"
import { Share2, <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface ReferralSectionProps {
  points: number
  referralCode: string | null
}

export default function ReferralSection({ points, referralCode }: ReferralSectionProps) {
  const [copied, setCopied] = useState(false)

  // Top referrers data
  const topReferrers = [
    { name: "<PERSON>", points: 450 },
    { name: "<PERSON>", points: 320 },
    { name: "<PERSON>", points: 280 },
  ]

  const referralLink = `https://yourapp.com/invite?ref=${referralCode}`

  const copyToClipboard = () => {
    navigator.clipboard.writeText(referralLink)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const shareToWhatsApp = () => {
    const text = `Check out this awesome app! Join me using my referral link: ${referralLink}`
    window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, "_blank")
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5 text-indigo-500" />
          Invite Friends
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <p className="text-sm mb-2">✨ Invite friends and earn points!</p>
          <p className="text-xs text-muted-foreground mb-4">
            You've earned <span className="font-medium text-indigo-600">{points} points</span> from referrals
          </p>

          <div className="flex gap-2 mb-4">
            <Input value={referralLink} readOnly className="text-sm" />
            <Button size="sm" onClick={copyToClipboard} variant="primary-outline">
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>

          <Button onClick={shareToWhatsApp} className="w-full bg-green-600 hover:bg-green-700">
            Share to WhatsApp
          </Button>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Top Referrers</h4>
          <div className="space-y-2">
            {topReferrers.map((referrer, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <div className="bg-indigo-100 text-indigo-800 w-6 h-6 rounded-full flex items-center justify-center text-xs">
                    {index + 1}
                  </div>
                  <span>{referrer.name}</span>
                </div>
                <span className="text-muted-foreground">{referrer.points} pts</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
