import { Bell } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface ChangelogItem {
  id: string
  emoji: string
  title: string
  date: string
  isNew?: boolean
}

export default function ChangelogSection() {
  const changelogItems: ChangelogItem[] = [
    {
      id: "1",
      emoji: "🔥",
      title: "Just launched Skill Test!",
      date: "Today",
      isNew: true,
    },
    {
      id: "2",
      emoji: "🧪",
      title: "New beta: Create your own test!",
      date: "2 days ago",
      isNew: true,
    },
    {
      id: "3",
      emoji: "🚀",
      title: "Platform officially launched",
      date: "1 week ago",
    },
  ]

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5 text-indigo-500" />
          What's New
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {changelogItems.map((item) => (
            <div key={item.id} className="flex gap-3">
              <div className="text-xl">{item.emoji}</div>
              <div>
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium">{item.title}</p>
                  {item.isNew && <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-0">New</Badge>}
                </div>
                <p className="text-xs text-muted-foreground">{item.date}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 pt-3 border-t border-border">
          <a href="/changelog" className="text-xs text-indigo-600 hover:text-indigo-800 hover:underline">
            View full changelog →
          </a>
        </div>
      </CardContent>
    </Card>
  )
}
