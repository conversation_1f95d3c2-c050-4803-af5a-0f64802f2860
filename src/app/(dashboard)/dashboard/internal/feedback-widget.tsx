"use client"

import { useState } from "react"
import { MessageSquare, Check } from "lucide-react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

export default function FeedbackWidget() {
  const [feedback, setFeedback] = useState("")
  const [selectedMood, setSelectedMood] = useState<string | null>(null)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const moods = [
    { emoji: "❤️", label: "Love it" },
    { emoji: "👍", label: "Good" },
    { emoji: "😐", label: "Neutral" },
    { emoji: "😞", label: "Needs work" },
  ]

  const handleSubmit = () => {
    // In a real app, you would send this to your backend
    console.log({ feedback, selectedMood })
    setIsSubmitted(true)

    // Reset after 3 seconds
    setTimeout(() => {
      setFeedback("")
      setSelectedMood(null)
      setIsSubmitted(false)
    }, 3000)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-indigo-500" />
          Feedback
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm mb-4">Got feedback? Help us improve!</p>

        <Dialog>
          <DialogTrigger asChild>
            <Button className="w-full">Send Feedback</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Share your feedback</DialogTitle>
              <DialogDescription>
                Your feedback helps us improve our platform. Tell us what you think!
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="flex justify-between">
                {moods.map((mood) => (
                  <Button
                    key={mood.emoji}
                    variant="primary-outline"
                    className={`flex flex-col h-auto py-2 px-4 ${
                      selectedMood === mood.emoji ? "border-indigo-500 bg-indigo-50" : ""
                    }`}
                    onClick={() => setSelectedMood(mood.emoji)}
                  >
                    <span className="text-xl mb-1">{mood.emoji}</span>
                    <span className="text-xs">{mood.label}</span>
                  </Button>
                ))}
              </div>

              <Textarea
                placeholder="Tell us what's on your mind..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="min-h-[100px]"
              />
            </div>

            <DialogFooter>
              {isSubmitted ? (
                <div className="flex items-center text-green-600 gap-2">
                  <Check className="h-4 w-4" />
                  <span>Thank you for your feedback!</span>
                </div>
              ) : (
                <Button onClick={handleSubmit} disabled={!selectedMood && !feedback}>
                  Submit Feedback
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <div className="mt-4 flex justify-between">
          {moods.map((mood) => (
            <button
              key={mood.emoji}
              className="text-xl hover:scale-125 transition-transform"
              title={mood.label}
              onClick={() => {
                setSelectedMood(mood.emoji)
                setFeedback(`Quick reaction: ${mood.label}`)
                setTimeout(() => {
                  setFeedback("")
                  setSelectedMood(null)
                }, 1000)
              }}
            >
              {mood.emoji}
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
