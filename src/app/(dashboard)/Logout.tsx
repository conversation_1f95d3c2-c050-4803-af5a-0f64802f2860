"use client";

import { signOut } from "next-auth/react";
import { LogOut } from "lucide-react";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";

export default function LogoutButton() {
  const handleLogout = () => {
    signOut({ callbackUrl: "/login" }); // POST /api/auth/signout
  };

  return (
    <DropdownMenuItem onSelect={handleLogout}>
      <LogOut className="mr-2 h-4 w-4" />
      <span>Log out</span>
    </DropdownMenuItem>
  );
}
