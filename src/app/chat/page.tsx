import ClientApp from "@/components/ClientApp";
import { SIDEBAR_COOKIE } from "@/components/SidebarCookie";
import { RootProvider } from "@/context/useRoot";
import { cookies, type UnsafeUnwrappedCookies } from "next/headers";
import defaultAppearanceSetting from "@/consts/defaultAppearanceSetting.json";
import { AppearanceColorSetting, AppearanceSetting } from "@/model/appSetting";

function Home() {
  const cookieStore = (cookies() as unknown as UnsafeUnwrappedCookies);
  const leftBarStateCookie = cookieStore.get(SIDEBAR_COOKIE.getKey("left"));
  const rightBarStateCookie = cookieStore.get(SIDEBAR_COOKIE.getKey("right"));

  return (
    <>
      <RootProvider
        value={{
          leftBar: leftBarStateCookie
            ? SIDEBAR_COOKIE.getValue(leftBarStateCookie.value)
            : true,
          rightBar: rightBarStateCookie
            ? SIDEBAR_COOKIE.getValue(rightBarStateCookie.value)
            : true,
          appearanceSetting:
            defaultAppearanceSetting as unknown as AppearanceSetting,
        }}
      >
        <ClientApp />
      </RootProvider>
    </>
  );
}

export default Home;
