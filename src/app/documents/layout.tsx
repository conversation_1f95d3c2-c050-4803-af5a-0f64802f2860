"use client";

import { useState, useRef, useEffect } from "react";
import "@/styles/globals.css";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Page {
  id: string;
  title: string;
}

const STORAGE_KEY = "my-pages";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [pages, setPages] = useState<Page[]>([]);
  const [activePageId, setActivePageId] = useState<string | null>(null);
  const [sidebarWidth, setSidebarWidth] = useState<number>(260);
  const resizerRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);

  const [hasLoaded, setHasLoaded] = useState(false); // 🆕 track if loaded

  const createPage = () => {
    const newPage: Page = {
      id: Date.now().toString(),
      title: `Untitled ${pages.length + 1}`,
    };
    setPages((prev) => [...prev, newPage]);
    setActivePageId(newPage.id);
  };

  const deletePage = (id: string) => {
    setPages((prev) => prev.filter((page) => page.id !== id));
    if (activePageId === id) {
      setActivePageId(null);
    }
  };

  const selectPage = (id: string) => {
    setActivePageId(id);
  };

  const startDragging = () => {
    isDragging.current = true;
  };

  // Load from localStorage
  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed: Page[] = JSON.parse(stored);
      setPages(parsed);
      if (parsed.length > 0) {
        setActivePageId(parsed[0].id);
      }
    }
    setHasLoaded(true); // ✅ now safe to save after
  }, []);

  // Save to localStorage (only after initial load)
  useEffect(() => {
    if (hasLoaded) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(pages));
    }
  }, [pages, hasLoaded]);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging.current) return;
      const newWidth = e.clientX;
      if (newWidth >= 180 && newWidth <= 500) {
        setSidebarWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      isDragging.current = false;
    };

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, []);

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <aside
        className="pt-4 bg-white border-r h-full flex flex-col"
        style={{ width: sidebarWidth }}
      >
        <div className="px-4">
          <h2 className="text-lg font-bold mb-4">Pages</h2>

          <Button onClick={createPage} className="mb-4 w-full">
            + New Page
          </Button>
        </div>

        <ScrollArea className="flex-1 px-4">
          <ul className="space-y-2">
            {pages.map((page) => (
              <li
                key={page.id}
                className={`flex justify-between items-center p-2 rounded cursor-pointer ${
                  activePageId === page.id ? "bg-blue-100" : "hover:bg-gray-200"
                }`}
              >
                <span onClick={() => selectPage(page.id)}>{page.title}</span>
                <button
                  onClick={() => deletePage(page.id)}
                  className="text-red-500 hover:text-red-700 ml-2"
                >
                  &times;
                </button>
              </li>
            ))}
          </ul>
        </ScrollArea>
      </aside>

      {/* Resizer */}
      <div
        ref={resizerRef}
        onMouseDown={startDragging}
        className="w-2 cursor-col-resize bg-gray-200 hover:bg-gray-400 transition-colors"
        style={{ height: "100vh" }}
      />

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        <div>{children}</div>
      </main>
    </div>
  );
}
