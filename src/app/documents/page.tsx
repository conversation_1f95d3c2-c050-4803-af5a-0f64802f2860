"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function Page() {
  const router = useRouter();

  const handleCreateNewDocument = () => {
    router.push("/documents/new"); 
  };

  return (
    <div className="w-full h-full p-20 flex flex-col items-center justify-center text-center">
      <h1 className="text-4xl font-bold mb-4">Your Knowledge, Organized</h1>
      <p className="text-lg text-gray-600 max-w-2xl mb-8">
        <strong>Document</strong> is your personal space to collect ideas, take notes,
        build knowledge libraries, and prepare a wisebase for your AI assistant. 
        Embed charts, create diagrams, collaborate and share with others — all in one place.
      </p>

      <Button onClick={handleCreateNewDocument} className="text-lg px-6 py-4">
        + Create New Document
      </Button>

      <div className="mt-12 max-w-4xl grid grid-cols-1 md:grid-cols-3 gap-6">
        <FeatureCard
          title="Take Notes Effortlessly"
          description="Write, format, and organize your thoughts with a beautiful editor."
        />
        <FeatureCard
          title="Build Your Library"
          description="Collect information, documents, and knowledge for future reference and AI learning."
        />
        <FeatureCard
          title="Share & Collaborate"
          description="Publish or share documents privately. Invite your team to work together."
        />
        <FeatureCard
          title="Embed Charts & Diagrams"
          description="Visualize your ideas. Add charts and diagrams directly into your documents."
        />
        <FeatureCard
          title="Train Your AI"
          description="Use your documents as a knowledge source for your LLM apps."
        />
        <FeatureCard
          title="Secure and Organized"
          description="Your documents are safe, structured, and easily accessible whenever you need them."
        />
      </div>
    </div>
  );
}

function FeatureCard({ title, description }: { title: string; description: string }) {
  return (
    <div className="p-6 border rounded-xl hover:shadow-lg transition">
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}
