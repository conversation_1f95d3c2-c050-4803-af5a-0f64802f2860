"use client";

import { JSX, useCallback, useState } from "react";
import DocumentEditor from "@rendererVite/DocumentEditor";
import { MyBlock, schema } from "@rendererVite/DocumentEditor/schema";
import "@blocknote/core/fonts/inter.css";
import "@blocknote/mantine/style.css";
import { useParams, useRouter } from "next/navigation";
import Analytics from "@/services/analytics";
import { BlockNoteEditor } from "@blocknote/core";
import { Button } from "@/components/ui/button";
import Link from "next/link"; // Import Link for breadcrumb

const jsxToDOM = (jsx: JSX.Element): HTMLElement => {
  const div = document.createElement("div");
  div.innerHTML = jsx.props.children as string;
  return div;
};

export default function Page() {
  const router = useRouter();
  const { id } = useParams() as { id: string };
  const storageKey = `editor-${id}`;
  const [isLoading, setIsLoading] = useState(false);

  const shareDocument = async () => {
    try {
      setIsLoading(true);
      const saved = localStorage.getItem(storageKey);
      if (!saved) {
        alert("Please create the document.");
        return;
      }

      const parsed: MyBlock[] = JSON.parse(saved);
      const blocksToShare = parsed;

      if (!blocksToShare || blocksToShare.length === 0) {
        alert("No blocks to share. Sorry!!");
        return;
      }

      const response = await fetch("/api/v1/generate-share-docs-url", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ blocks: blocksToShare }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate share link");
      }

      const { id } = await response.json();

      Analytics.getInstance().track("Share Document", {
        messageCount: blocksToShare.length,
      });

      const url = `/shared/document/${id}`;
      window.open(url, "_blank");
    } catch (error: any) {
      console.error("Error sharing document", error);
      Analytics.getInstance().track("Failed Share Document", {
        error: error.message,
      });
      alert("Error sharing Document");
    } finally {
      setIsLoading(false);
    }
  };

  // Example breadcrumb path
  const breadcrumbs = [
    { label: "Root", href: "/documents" },
    { label: id, href: `/documents/${id}` }, // current document
  ];

  return (
    <div className="w-full h-full">
      <header className="sticky top-0 z-[9999] flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-2 px-12 bg-white">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          {breadcrumbs.map((crumb, index) => (
            <div key={crumb.href} className="flex items-center">
              {index > 0 && <span className="mx-2">/</span>}
              <Link
                href={crumb.href}
                className="hover:underline hover:text-black transition"
              >
                {crumb.label}
              </Link>
            </div>
          ))}
        </nav>

        {/* Share Button */}
        <div className="flex justify-end">
          <Button
            onClick={shareDocument}
            disabled={isLoading}
            className="flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <svg
                className="w-5 h-5 animate-spin text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                />
              </svg>
            ) : (
              "Share"
            )}
          </Button>
        </div>
      </header>

      <DocumentEditor storageKey={storageKey} />
    </div>
  );
}
