import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Smile, LogIn, Users, Home } from "lucide-react"

export default function LogoutPage() {
  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-purple-50 to-white dark:from-purple-950 dark:to-background px-4 text-center">
      <Smile className="w-16 h-16 text-purple-600 mb-4" />
      <h1 className="text-3xl sm:text-4xl font-bold mb-2">You've logged out.</h1>
      <p className="text-gray-500 dark:text-gray-400 text-lg max-w-md mb-6">
        But you’ll be back. That’s totally fine. No need to rush.
      </p>

      <div className="flex flex-col sm:flex-row gap-3">
        <Link href="/login">
          <Button className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto">
            <LogIn className="mr-2 w-4 h-4" />
            {"Let’s Chat Again"}
          </Button>
        </Link>
        <Link href="/">
          <Button variant="primary-outline" className="w-full sm:w-auto">
            <Home className="mr-2 w-4 h-4" />
            Go to Home
          </Button>
        </Link>
        <Link href="/landing-enterprise">
          <Button variant="ghost" className="w-full sm:w-auto">
            <Users className="mr-2 w-4 h-4" />
            Start Your Team
          </Button>
        </Link>
      </div>
    </div>
  )
}