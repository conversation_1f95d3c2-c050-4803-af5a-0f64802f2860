import { buildCssVarsShadcn, buildVariables } from "./css-builder";

export function generateAppearanceCss(appearance: any): string {
  const light = {
    ...buildCssVarsShadcn(false, appearance.advanced.light, appearance),
    ...buildVariables(false, appearance),
  };

  const dark = {
    ...buildCssVarsShadcn(true, appearance.advanced.dark, appearance),
    ...buildVariables(true, appearance),
  };

  const stringify = (vars: Record<string, string | number | undefined>) =>
    Object.entries(vars)
      .map(([key, value]) => `${key}: ${value};`)
      .join("\n");

  return `:root {\n${stringify(light)}\n}\n.dark {\n${stringify(dark)}\n}`;
}
