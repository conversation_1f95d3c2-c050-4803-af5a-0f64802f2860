import { AppearanceColorAdvanced, AppearanceSetting } from "@/model/appSetting";

function isReference(text: string) {
  return text.includes("--");
}

function resolvePath(obj: any, path: string[]): any {
  return path.reduce(
    (acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined),
    obj
  );
}

function getReferenceValue(
  text: string,
  appearance: AppearanceSetting
): string | undefined {
  if (!isReference(text)) return text;

  const clear = text.replace(/^--/, "");
  const paths = clear.split(".");
  let result = resolvePath(appearance, paths);

  while (typeof result === "string" && isReference(result)) {
    const refClear = result.replace(/^--/, "");
    const refPaths = refClear.split(".");
    result = resolvePath(appearance, refPaths);
  }

  return result;
}

export const buildCssVarsShadcn = (
  isDark: boolean,
  colors: AppearanceColorAdvanced,
  appearance: AppearanceSetting
): Record<string, string | undefined> => {
  const convert = (hex?: string) => (hex ? convertHexToHSL(hex) : undefined);

  function buildHSL(value?: string) {
    const valueString = value || "";
    const valueThemeReplaced = valueString.replace(
      "[theme]",
      isDark ? "dark" : "light"
    );
    const resolved = getReferenceValue(valueThemeReplaced, appearance);
    return resolved ? convert(resolved) : undefined;
  }

  function buildRaw(value?: string) {
    const resolved = getReferenceValue(value || "", appearance);
    return resolved;
  }

  const colorsVariables = Object.fromEntries(
    Object.entries(colors).map(([key, value]) => [`--${key}`, buildHSL(value)])
  );

  return {
    // BASE VARIABLE
    "--radius": "0.5rem",

    // SECONDARY PALETTE
    ...colorsVariables,
  };
};

export function buildVariables(
  isDark: boolean,
  themeConfig: AppearanceSetting
): Record<string, string | number | undefined> {
  const unit = "px";
  const color = isDark
    ? themeConfig.colorPalette.dark
    : themeConfig.colorPalette.light;

  function buildRaw(value: string | number | undefined, log?: string) {
    const valueString = value?.toString() || "";
    const valueThemeReplaced = valueString.replace(
      "[theme]",
      isDark ? "dark" : "light"
    );
    const resolved = getReferenceValue(valueThemeReplaced, themeConfig);
    if (log) {
      console.log(
        "Build Raw Variables LOG:",
        log,
        "VALUE:",
        value,
        valueThemeReplaced,
        "RESOLVED:",
        resolved
      );
    }
    return resolved ?? undefined;
  }

  const tempVariablesDynamic = Object.fromEntries(
    Object.entries(themeConfig.component.temp_dynamic).map(([key, value]) => [
      `--${key}`,
      buildRaw(value),
    ])
  );

  return {
    // BASE VARIABLE
    "--color-primary": color.primary,
    "--color-secondary": color.secondary,
    "--color-tertiary": color.tertiary,
    "--color-quaternary": color.quaternary,
    "--font-family": themeConfig.typography.fontFamily,
    "--base-font-size": themeConfig.typography.baseFontSize,
    "--font-weight-normal": themeConfig.typography.fontWeight.normal,
    "--font-weight-bold": themeConfig.typography.fontWeight.bold,

    // THIS IS COMPONENT PALETTE
    "--button-rounded-top-left": buildRaw(
      themeConfig.button.borderRadius.topLeft + unit
    ),
    "--button-rounded-top-right": buildRaw(
      themeConfig.button.borderRadius.topRight + unit
    ),
    "--button-rounded-bottom-left": buildRaw(
      themeConfig.button.borderRadius.bottomLeft + unit
    ),
    "--button-rounded-bottom-right": buildRaw(
      themeConfig.button.borderRadius.bottomRight + unit
    ),
    "--button-padding": buildRaw(themeConfig.button.padding),
    "--button-font-family": buildRaw(themeConfig.button.textStyle.fontFamily),
    "--button-font-weight": buildRaw(themeConfig.button.textStyle.fontWeight),
    "--button-bg-color": buildRaw(
      themeConfig.button.hoverStyle.backgroundColor
    ),
    "--button-text-color": buildRaw(themeConfig.button.hoverStyle.textColor),
    "--card-rounded-top-left": buildRaw(themeConfig.card.borderRadius.topLeft),
    "--card-rounded-top-right": buildRaw(
      themeConfig.card.borderRadius.topRight
    ),
    "--card-rounded-bottom-left": buildRaw(
      themeConfig.card.borderRadius.bottomLeft
    ),
    "--card-rounded-bottom-right": buildRaw(
      themeConfig.card.borderRadius.bottomRight
    ),
    "--card-shadow": buildRaw(themeConfig.card.shadowStyle.boxShadow),
    "--card-bg-color": buildRaw(themeConfig.card.hoverStyle.backgroundColor),
    "--card-text-color": buildRaw(themeConfig.card.hoverStyle.textColor),
    "--logo-url": buildRaw(themeConfig.logo.url),
    "--logo-height": buildRaw(themeConfig.logo.height),

    "--chatsection-background": buildRaw(
      themeConfig.component.chatSection.background
    ),
    "--chatinput-background": buildRaw(
      themeConfig.component.chatInput.background
    ),
    "--sidebar-background": buildRaw(themeConfig.component.sidebar.background),
    ...tempVariablesDynamic,
  };
}

function convertHexToHSL(hex: string): string {
  hex = hex.replace(/^#/, "");
  let r = 0,
    g = 0,
    b = 0;
  if (hex.length === 3) {
    r = parseInt(hex[0] + hex[0], 16);
    g = parseInt(hex[1] + hex[1], 16);
    b = parseInt(hex[2] + hex[2], 16);
  } else if (hex.length === 6) {
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  }
  r /= 255;
  g /= 255;
  b /= 255;
  const max = Math.max(r, g, b),
    min = Math.min(r, g, b);
  let h = 0,
    s = 0,
    l = (max + min) / 2;
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }
  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(
    l * 100
  )}%`;
}
