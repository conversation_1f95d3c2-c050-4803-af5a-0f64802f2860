import i18next from "i18next";
import Backend from "i18next-fs-backend";
import path from "path";

const i18nServerInstance = i18next.createInstance();

let initialized = false;

export async function serverTranslation(lang: string, ns: string | string[]) {
  if (!initialized) {
    await i18nServerInstance.use(Backend).init({
      lng: lang,
      fallbackLng: "en",
      ns: Array.isArray(ns) ? ns : [ns],
      defaultNS: Array.isArray(ns) ? ns[0] : ns,
      backend: {
        loadPath: path.resolve("./public/locales/{{lng}}/{{ns}}.json"),
      },
      interpolation: {
        escapeValue: false,
      },
      initImmediate: false, // ⚠️ Important for synchronous SSR
    });
    initialized = true;
  } else {
    await i18nServerInstance.loadLanguages(lang);
  }

  return {
    t: i18nServerInstance.getFixedT(lang),
  };
}
