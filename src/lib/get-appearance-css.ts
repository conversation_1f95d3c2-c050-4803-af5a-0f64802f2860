type UploadTarget = "vercel" | "local" | "cloudflare";

const UPLOAD_TARGET = (process.env.UPLOAD_TARGET as UploadTarget) || "local";
const CDN_BASE_URL = process.env.CDN_BASE_URL || "";

export function getAppearanceCssUrl(appearanceId: string): string {
  switch (UPLOAD_TARGET) {
    case "vercel":
    case "cloudflare":
      return `${CDN_BASE_URL}/appearance/${appearanceId}.css`;
    case "local":
    default:
      return `/appearance/${appearanceId}.css`; // Served from public/
  }
}
