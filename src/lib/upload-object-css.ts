type UploadTarget = "vercel" | "local" | "cloudflare";

const UPLOAD_TARGET: UploadTarget =
  (process.env.UPLOAD_TARGET as UploadTarget) || "local";

export async function uploadAppearanceCss(
  id: string,
  css: string
): Promise<string> {
  switch (UPLOAD_TARGET) {
    case "vercel":
      return uploadToVercel(id, css);
    case "local":
      return uploadToLocal(id, css);
    case "cloudflare":
      return uploadToCloudflare(id, css);
    default:
      throw new Error("Unsupported upload target");
  }
}

// 1. Upload to Vercel Blob (Edge Config + Blob store)
async function uploadToVercel(id: string, css: string): Promise<string> {
  const { put } = await import("@vercel/blob");
  const blob = await put(
    `appearance/${id}.css`,
    new Blob([css], { type: "text/css" }),
    {
      access: "public",
    }
  );
  return blob.url;
}

// 2. Local fallback (e.g. during dev)
import fs from "fs/promises";
import path from "path";

async function uploadToLocal(id: string, css: string): Promise<string> {
  const filePath = path.join(
    process.cwd(),
    "public",
    "appearance",
    `${id}.css`
  );
  await fs.mkdir(path.dirname(filePath), { recursive: true });
  await fs.writeFile(filePath, css);
  return `/appearance/${id}.css`; // served by Next.js from public
}

// 3. Cloudflare R2 or other
async function uploadToCloudflare(id: string, css: string): Promise<string> {
  // implement with @cloudflare/workers-types or SDK
  throw new Error("Cloudflare R2 upload not implemented");
}
