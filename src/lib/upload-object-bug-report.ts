import fs from "fs/promises";
import path from "path";
import { v4 as uuidv4 } from "uuid";

type UploadTarget = "vercel" | "local" | "cloudflare";

const UPLOAD_TARGET: UploadTarget =
  (process.env.UPLOAD_TARGET as UploadTarget) || "local";

export interface BugReportPayload {
  markdown: string;
  clientInfo: any;
  screenshot: {
    buffer: Buffer; // screenshot content as a buffer
    originalFilename: string;
    mimetype: string;
  };
}

export interface UploadedBugReport {
  bugId: string;
  urls: {
    markdown: string;
    clientInfo: string;
    screenshot: string;
  };
}

export async function uploadBugReport(
  payload: BugReportPayload
): Promise<UploadedBugReport> {
  switch (UPLOAD_TARGET) {
    case "vercel":
      return uploadToVercel(payload);
    case "local":
      return uploadToLocal(payload);
    case "cloudflare":
      return uploadToCloudflare(payload);
    default:
      throw new Error("Unsupported upload target");
  }
}

// 1. Vercel Blob
async function uploadToVercel({
  markdown,
  clientInfo,
  screenshot,
}: BugReportPayload): Promise<UploadedBugReport> {
  const { put } = await import("@vercel/blob");
  const bugId = uuidv4();

  const [markdownBlob, clientBlob, screenshotBlob] = await Promise.all([
    put(
      `bugs/${bugId}/input.md`,
      new Blob([markdown], { type: "text/markdown" }),
      {
        access: "public",
      }
    ),
    put(
      `bugs/${bugId}/client.json`,
      new Blob([JSON.stringify(clientInfo, null, 2)], {
        type: "application/json",
      }),
      { access: "public" }
    ),
    put(
      `bugs/${bugId}/screenshot.png`,
      new Blob([screenshot.buffer], { type: screenshot.mimetype }),
      { access: "public" }
    ),
  ]);

  return {
    bugId,
    urls: {
      markdown: markdownBlob.url,
      clientInfo: clientBlob.url,
      screenshot: screenshotBlob.url,
    },
  };
}

// 2. Local filesystem
async function uploadToLocal({
  markdown,
  clientInfo,
  screenshot,
}: BugReportPayload): Promise<UploadedBugReport> {
  const bugId = uuidv4();
  const bugDir = path.join(process.cwd(), "public", "reports", bugId);

  await fs.mkdir(bugDir, { recursive: true });

  const markdownPath = path.join(bugDir, "input.md");
  const clientInfoPath = path.join(bugDir, "client.json");
  const screenshotPath = path.join(bugDir, "screenshot.png");

  // Ensure that screenshot.buffer is a Buffer, then convert to Uint8Array
  const screenshotBuffer =
    screenshot.buffer instanceof Buffer
      ? new Uint8Array(screenshot.buffer)
      : new Uint8Array(Buffer.from(screenshot.buffer));

  await Promise.all([
    fs.writeFile(markdownPath, markdown, "utf-8"),
    fs.writeFile(clientInfoPath, JSON.stringify(clientInfo, null, 2)),
    fs.writeFile(screenshotPath, screenshotBuffer), // Use the correct Uint8Array type
  ]);

  return {
    bugId,
    urls: {
      markdown: `/reports/${bugId}/input.md`,
      clientInfo: `/reports/${bugId}/client.json`,
      screenshot: `/reports/${bugId}/screenshot.png`,
    },
  };
}
// 3. Cloudflare R2 (stub)
async function uploadToCloudflare(
  _: BugReportPayload
): Promise<UploadedBugReport> {
  throw new Error("Cloudflare R2 upload not implemented");
}
