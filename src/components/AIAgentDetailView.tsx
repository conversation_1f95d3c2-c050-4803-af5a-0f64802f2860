import { AIAgent } from "@/model/ai_agent";
import { ArrowLeft } from "lucide-react";

interface AIAgentDetailViewProps {
  agent: AIAgent;
  onSelectQuery: (query: string) => void;
  onBack: () => void;
}

const AIAgentDetailView: React.FC<AIAgentDetailViewProps> = ({
  agent,
  onSelectQuery,
  onBack,
}) => {
  return (
    <div className="p-16 w-full flex flex-col">
      <div className="flex items-center space-x-4 mb-4">
        <button
          onClick={onBack}
          className="flex items-center text-[var(--text-custom-purple-600)] hover:text-[var(--text-custom-purple-800)]"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          <span>Back</span>
        </button>
      </div>

      <div className="flex items-center space-x-4">
        <img
          src={agent.icon}
          alt={agent.title}
          className="w-16 h-16 rounded-full object-cover"
        />
        <div>
          <h2 className="text-2xl font-bold text-[var(--text-custom-gray-800)]">
            {agent.title}
          </h2>
          <p className="text-[var(--text-custom-gray-600)] mt-2">
            {agent.description}
          </p>
        </div>
      </div>

      <div className="mt-6 flex flex-col">
        <h3 className="text-xl font-semibold text-[var(--text-custom-gray-800)]">
          Sample Queries
        </h3>
        <div className="flex flex-wrap w-full mt-2">
          {agent.queries.map((query, index) => (
            <div
              key={`query-sample-${index}`}
              className="mr-4 mb-2 px-4 py-2 bg-[var(--bg-custom-gray-200)] text-white rounded-md whitespace-nowrap hover:bg-[var(--bg-custom-purple-300)] hover:cursor-pointer"
              onClick={() => onSelectQuery(query)}
            >
              {query}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AIAgentDetailView;
