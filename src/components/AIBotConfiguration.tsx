import React from "react";
import { But<PERSON> } from "./Elements/Button";
import Dropdown from "./Dropdown";
import { usePopup } from "@/context/usePopUp";
import useJoinFeature from "@/app/hooks/useJoinFeature";
import { Analytics } from "@vercel/analytics/react";
import { getUserEmailOrPopUp } from "@/services/getUserEmailOrPopUp";
import { GeneralPopUp } from "./Popup";

type AIBotConfig = {
  value: string;
  label: string;
  status?: "incoming" | "premium";
  onClick: (this: AIBotConfig) => void;
};

const AIBotConfiguration: React.FC = () => {
  const { openPopup } = usePopup();
  const { joinFeature } = useJoinFeature();

  async function openPopupAiModel(this: AIBotConfig) {
    if (this.status) {
      openPopup(
        new GeneralPopUp({
          context: "Select AI Model",
          title: "🫡 Aye. Understood captain. Let's sail the sea of AI models",
          content: `Even though ${this.label} is an incoming feature, we want you to be the first tester. Would you like?`,
          buttons: [
            {
              isPrimary: true,
              onClick: () => {},
              title: "Sure. For free!",
            },
            {
              isPrimary: false,
              onClick: () => {},
              title: "Nope. Thanks",
            },
          ],
        })
      );
    } else {
      console.log("Select AIBotConfig", this.value);
    }
  }

  async function didSelectPersona(this: AIBotConfig) {
    openPopup(
      new GeneralPopUp({
        context: "Select AI Persona",
        title: `Good job. TomioAI will assist you as a ${this.label}`,
        content: `There are a lot of work to do as a ${this.label} right? You can give a lot of feedback to make your work easier.`,
        buttons: [
          {
            isPrimary: true,
            onClick: () => {},
            title: "🙌 I will give feedback!",
          },
          {
            isPrimary: false,
            onClick: () => {},
            title: "We will see!",
          },
        ],
      })
    );
  }

  const aiModels: AIBotConfig[] = [
    { value: "gpt-3", label: "GPT-3", onClick: openPopupAiModel },
    {
      value: "gpt-4",
      label: "GPT-4",
      status: "incoming",
      onClick: openPopupAiModel,
    },
    {
      value: "llma2",
      label: "Llama 2",
      status: "incoming",
      onClick: openPopupAiModel,
    },
    {
      value: "bard",
      label: "Bard",
      status: "incoming",
      onClick: openPopupAiModel,
    },
    {
      value: "anthropic",
      label: "Anthropic",
      status: "incoming",
      onClick: openPopupAiModel,
    },
  ];

  const personas: AIBotConfig[] = [
    {
      value: "student",
      label: "Student 🚀",
      onClick: () => {
        openPopup(
          new GeneralPopUp({
            context: "Select Student AI Persona",
            title: "TomioAI is super happy to have you",
            content: `We will help you with math, biology, physics, astronomy -- you want to go to the Moon? You can give a lot of feedback to make your studying fun!`,
            buttons: [
              {
                isPrimary: true,
                onClick: () => {},
                title: "🙌 I will give feedback!",
              },
              {
                isPrimary: false,
                onClick: () => {},
                title: "We will see!",
              },
            ],
          })
        );
      },
    },
    { value: "programmer", label: "Programmer", onClick: didSelectPersona },
    {
      value: "content-writer",
      label: "Content Writer",
      onClick: didSelectPersona,
    },
    {
      value: "seo-researcher",
      label: "SEO Researcher",
      onClick: didSelectPersona,
    },
  ];

  async function onUserSubmitKey() {
    openPopup(
      new GeneralPopUp({
        context: "Submit API Key",
        title:
          "You are the best! You will learn a lot from your own analytics.",
        content:
          "But we want to allow you to use this preview version for free. You can use our API key for now. We cover the cost. 💸",
        buttons: [
          {
            isPrimary: true,
            onClick: async () => {
              try {
                const email = getUserEmailOrPopUp(openPopup);
                if (!email) {
                  return;
                }
                const referal = await joinFeature(email, "analytics-dashboard");
                localStorage.setItem("referal_join", referal);
                openPopup(
                  new GeneralPopUp({
                    title: "Awesome. You have joined! Thanks.",
                    content:
                      "Analytics allow you to understand how you use the application, enabling you to maximize its potential for your own benefit. Why not take advantage of it? Meanwhile please enjoy TomioAI!",
                    context: "join-analytics-dashboard",
                    buttons: [
                      {
                        title: "OK",
                        onClick: () => {},
                        isPrimary: false,
                      },
                    ],
                  })
                );
              } catch (e) {}
            },
            title: "I prefer Analytics 📊.",
          },
          {
            isPrimary: false,
            onClick: async () => {
              setTimeout(() => {
                openPopup(
                  new GeneralPopUp({
                    title: "Okay! I like to talk with you.",
                    content:
                      "You can also give me a feedback. As for now, let's tak with email",
                    context: "join-analytics-dashboard-talk",
                    buttons: [
                      {
                        title: "Email",
                        onClick: () => {
                          const email = "<EMAIL>";
                          const mailtoLink = `mailto:${email}`;
                          window.location.href = mailtoLink;
                        },
                        isPrimary: false,
                      },
                    ],
                  })
                );
              }, 1000);
            },
            title: "Hmm... 🤔. Let's talk",
          },
          {
            isPrimary: false,
            onClick: () => {},
            title: "Awesome!",
          },
        ],
      })
    );
  }

  return (
    <div className="API w-[85%] flex flex-col gap-3 items-center">
      <div className="flex gap-3">
        <Dropdown label="Select AI Model" options={aiModels} />
        <Dropdown label="Select Persona" options={personas} />
      </div>
      <div className="flex gap-3 w-full">
        <input
          type="text"
          className="flex-grow h-10 border border-[#362167] outline-none rounded-lg text-xs font-medium px-2"
          placeholder="Using your own API key allows you to have access analytics. Want to try? Try input something here."
        />
        <Button className="bg-[#362168] px-5" onClick={onUserSubmitKey}>
          Submit
        </Button>
      </div>
    </div>
  );
};

export default AIBotConfiguration;
