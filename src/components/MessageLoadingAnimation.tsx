export const MessageLoadingAnimation = () => (
  <div className="flex min-w-full animate-pulse px-4 py-5 sm:px-6">
    <div className="flex flex-grow space-x-3">
      <div className="min-w-0 flex-1">
        <p className="font-large text-xxl text-[var(--text-custom-gray-900)]">
          <a href="#" className="hover:underline">
            AI
          </a>
        </p>
        <div className="space-y-4 pt-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-2 h-2 rounded bg-[var(--bg-custom-zinc-500)]"></div>
            <div className="col-span-1 h-2 rounded bg-[var(--bg-custom-zinc-500)]"></div>
          </div>
          <div className="h-2 rounded bg-[var(--bg-custom-zinc-500)]"></div>
        </div>
      </div>
    </div>
  </div>
);

export default MessageLoadingAnimation;
