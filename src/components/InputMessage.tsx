import React, {
  KeyboardEvent,
  ChangeEvent,
  useState,
  useEffect,
  useRef,
} from "react";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { Mi<PERSON>, Paperclip, Lightbulb, Globe } from "lucide-react";
import {
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePopup } from "@/context/usePopUp";
import { ImportChatMessagePopup } from "./ImportChatMessagePopup";
import { CustomPopUp } from "./Popup";

interface InputMessageProps {
  isWriting: boolean;
  sendMessage: (message: string) => void;
  importMessage: (message: {
    role: "user" | "assistant";
    content: string;
    source?: string;
  }) => void;
  stop: () => void;
}

const InputMessage: React.FC<InputMessageProps> = ({
  isWriting,
  sendMessage,
  importMessage,
  stop,
}) => {
  const [input, setInput] = useState("");
  const [isReasoningMode, setIsReasoningMode] = useState(false);
  const [isWebSearchEnabled, setIsWebSearchEnabled] = useState(false);
  const [importChatMessage, setImportChatMessage] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { openPopup, closePopup } = usePopup();

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [input]);

  function sendMessageIfIsWritingFalse(text: string) {
    if (text.length === 0) return;

    sendMessage(text);
    setInput("");
  }

  const handleImportChatMessage = (text: string) => {
    openPopup(
      new CustomPopUp({
        context: "importChatMessage",
        view: (
          <ImportChatMessagePopup
            onImport={(message) => {
              importMessage(message)
              closePopup()
            }}
            onCancel={() => closePopup()}
          />
        ),
      })
    );
  };

  const handleVoiceInput = () => {
    console.log("Voice input activated");
  };

  const handleImageAttach = () => {
    console.log("Image attach functionality");
  };

  const toggleWebSearch = () => {
    setIsWebSearchEnabled((prev) => !prev);
    console.log(`Web search ${isWebSearchEnabled ? "disabled" : "enabled"}`);
  };

  return (
    <div className="w-full flex flex-col gap-1 rounded-lg p-1 border border-[var(--border-custom-purple-300)]/50 bg-[var(--chatinput-background)]">
      {/* Textarea and associated prompt */}
      <div className="flex flex-col w-full">
        {/* Textarea input */}
        <textarea
          ref={textareaRef}
          aria-label="chat input"
          placeholder="Write your message here..."
          required
          className={`w-full rounded-md bg-[var(--chatinput-background)] px-3 py-2 text-sm placeholder:text-[var(--text-custom-zinc-400)] focus:border-[var(--border-custom-teal-500)] focus:outline-none focus:ring-4 focus:ring-teal-500/10 resize-none max-h-60 overflow-auto shadow-sm`}
          value={input}
          onKeyDown={(e: KeyboardEvent<HTMLTextAreaElement>) => {
            if (e.key === "Enter" && e.shiftKey) return;
            if (e.key === "Enter") {
              e.preventDefault();
              sendMessageIfIsWritingFalse(input);
            }
          }}
          onChange={(e: ChangeEvent<HTMLTextAreaElement>) => {
            setInput(e.target.value);
          }}
        />
      </div>

      {/* Button Group (Voice, Image, Reasoning, Web Search) */}
      <div className="flex justify-between w-full items-center">
        <div className="flex space-x-2 items-center">
          {/* Voice Input Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="w-7 h-7 bg-[var(--bg-custom-blue-500)]"
                  onClick={handleVoiceInput}
                >
                  <Mic size={20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Voice input</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Attach Image Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="w-7 h-7 bg-[var(--bg-custom-green-500)]"
                  onClick={handleImageAttach}
                >
                  <Paperclip size={20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Attach image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Reasoning Mode Toggle Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className={`w-7 h-7 ${
                    isReasoningMode ? "bg-[var(--bg-custom-yellow-500)]" : "bg-[var(--bg-custom-gray-300)]"
                  }`}
                  onClick={() => setIsReasoningMode(!isReasoningMode)}
                >
                  <Lightbulb size={20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle reasoning mode</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Web Search Toggle Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className={`w-7 h-7 ${
                    isWebSearchEnabled ? "bg-[var(--bg-custom-blue-500)]" : "bg-[var(--bg-custom-gray-300)]"
                  }`}
                  onClick={toggleWebSearch}
                >
                  <Globe size={20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {isWebSearchEnabled
                    ? "Web search enabled"
                    : "Enable web search"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div className="hidden md:block">
            <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant="primary-outline"
                  onClick={() => handleImportChatMessage(input)}
                >
                  Import Message
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Import Chat Message From Other LLM</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          </div>
        </div>

        {/* Conditional Send/Stop Button */}
        <div>
          {isWriting ? (
            <button
              className="bg-[var(--bg-custom-red-500)] p-2 text-white rounded-lg"
              onClick={stop}
            >
              STOP
            </button>
          ) : (
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="primary"
                onClick={() => sendMessageIfIsWritingFalse(input)}
              >
                Send
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InputMessage;