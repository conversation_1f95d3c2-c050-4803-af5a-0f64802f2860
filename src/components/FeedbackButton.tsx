import { useFeedback } from "@/context/useFeedback";
import React from "react";
import { Button } from "./ui/button";

const FeedbackButton = () => {
  const { setShowFeedbackPopup } = useFeedback();
  const handleClick = () => {
    setShowFeedbackPopup(true);
  };

  return (
    <Button variant="primary" onClick={handleClick} className="w-full">
      Leave Feedback
    </Button>
  );
};

export default FeedbackButton;
