import { DisplayedMessage } from "@/model/displayedMessage";
import { ChatRoom } from "@/model/chatroom";
import { Icon } from "../Icon";
import { useEffect, useMemo, useState } from "react";
import { SelectableModelList } from "./SelectableModelList";
import { SelectablePluginList } from "./SelectablePluginList";
import { usePopup } from "@/context/usePopUp";
import { useChatRoom } from "@/context/useChatRoom";
import { useModel } from "@/context/useModel";
import { CustomPopUp } from "../Popup";
import { AnalyticsDashboard } from "../AnalyticsDashboard";

interface ChatNavBarProps {
  leftBar: boolean;
  setLeftBar: (value: boolean) => void;
  rightBar: boolean;
  setRightBar: (value: boolean) => void;
  openExportImport: () => void;
  openRoom?: ChatRoom | null;
  messages: DisplayedMessage[];
}

export function ChatNavBar(props: ChatNavBarProps) {
  const { openRoom, messages } = props;
  return (
    <div className="flex flex-col">
      <nav className="bg-white w-full h-11 md:rounded-t-md items-center px-3 flex justify-between">
        <LeftItems {...props} />
        <span className="text-center leading-tight w-1/3 text-xs md:text-base">
          <h1 className="font-bold ">{openRoom?.name || "New Chat"}</h1>
          <p className="text-slate-400 text-xs md:text-sm">
            {`${messages.length} messages` || "Start a new chat"}
          </p>
        </span>
        <RightItems {...props} />
      </nav>
      <ChatNavBarBanners />
    </div>
  );
}

function LeftItems(props: ChatNavBarProps) {
  const [displayedDropDown, setIsDisplayed] = useState<string>("");
  const { openRoom } = useChatRoom();
  const { models, defaultModel } = useModel();

  const { popUp } = usePopup();

  useEffect(() => {
    if (popUp) {
      setIsDisplayed("");
    }
  }, [popUp]);

  const selectedModel = useMemo(() => {
    if (!openRoom) {
      return null;
    }
    const model = models.findLast((e) => e.id === openRoom.model);
    return model;
  }, [models, openRoom]);

  return (
    <div className="flex space-x-10 w-1/3">
      <button onClick={() => props.setLeftBar(!props.leftBar)}>
        <Icon iconName="SidebarLeft" />
      </button>
      <ItemWithDropDownList
        isDisplayed={displayedDropDown === "model"}
        setIsDisplayed={(isDisplay) => setIsDisplayed(isDisplay ? "model" : "")}
        tool={
          <div className="flex space-x-2 items-center w-full h-full">
            <label className="font-bold cursor-pointer">
              {selectedModel?.name || defaultModel.name}
            </label>
            <Icon iconName="ArrowDown" />
          </div>
        }
      >
        <SelectableModelList onClose={() => setIsDisplayed("")} />
      </ItemWithDropDownList>
      <ItemWithDropDownList
        isDisplayed={displayedDropDown === "plugin"}
        setIsDisplayed={(isDisplay) =>
          setIsDisplayed(isDisplay ? "plugin" : "")
        }
        tool={
          <div className="flex space-x-2 items-center w-full">
            {openRoom && (
              <div className="rounded-full bg-green-600 text-white w-7 h-7 cursor-pointer flex items-center justify-center">
                <span className="text-center">{openRoom.plugins.length}</span>
              </div>
            )}
            <Icon iconName="Plugin" />
          </div>
        }
      >
        <SelectablePluginList onClose={() => setIsDisplayed("")} />
      </ItemWithDropDownList>
    </div>
  );
}

function ItemWithDropDownList({
  tool,
  children,
  isDisplayed,
  setIsDisplayed,
}: {
  tool: React.ReactNode;
  children: React.ReactNode;
  isDisplayed: boolean;
  setIsDisplayed: (display: boolean) => void;
}) {
  return (
    <div className="hidden md:flex flex-col relative">
      <button
        className="flex space-x-2"
        onClick={() => setIsDisplayed(!isDisplayed)}
      >
        {tool}
      </button>
      <div className="absolute w-full top-full">
        <div className="w-full relative">
          {isDisplayed && (
            <div className="absolute left-0 top-5 z-20">{children}</div>
          )}
        </div>
      </div>
    </div>
  );
}

function RightItems(props: ChatNavBarProps) {
  const { openPopup } = usePopup();

  function openAnalytics() {
    openPopup(
      new CustomPopUp({
        context: "analytics",
        view: <AnalyticsDashboard />,
      })
    );
  }

  return (
    <div className="flex space-x-10 flex-grow justify-end">
      <button className="hidden md:block" onClick={openAnalytics}>
        <Icon iconName="Chart" />
      </button>
      <button onClick={props.openExportImport}>
        <Icon iconName="Cloud" />
      </button>
      <button onClick={() => props.setRightBar(!props.rightBar)}>
        <Icon iconName="SidebarRight" />
      </button>
    </div>
  );
}

function ChatNavBarBanners() {
  return (
    <div className="bg-gray-200 p-2 rounded-md">
      Welcome to TomioAI.com, enjoy your free preview access. We are constantly
      updating the feature. Please leave a feedback.
    </div>
  );
}
