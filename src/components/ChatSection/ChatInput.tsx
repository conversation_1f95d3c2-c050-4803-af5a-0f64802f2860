import { ArrowDown, Mic, Image, Lightbulb, Globe, X } from "lucide-react"; // Added Search icon
import { Button } from "../Elements/Button";
import InputHelper from "../InputHelper";
import InputMessage from "../InputMessage";
import { useState, useEffect } from "react";
import {
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePromptLibrary } from "@/context/usePromptLibrary";
import { twMerge } from "tailwind-merge";
import { WIDTH_CHAT_INPUT } from "../ChatUI/TomioChat/ChatView";
import { useSidebarContext } from "@/context/useSidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

interface ChatInputProps {
  isWriting: boolean;
  regenerate: () => void;
  resetMessages: () => void;
  clearContext: () => void;
  shareChat: () => void;
  isShowButtonFollow: boolean;
  autoScrollToBottom: (animate: boolean) => void;
  setIsShowButtonFollow: (value: boolean) => void;
  stop: () => void;
  sendMessage: (message: string) => void;
  importMessage: (message: {
    role: "user" | "assistant";
    content: string;
    source?: string;
  }) => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  isWriting,
  regenerate,
  resetMessages,
  shareChat,
  clearContext,
  isShowButtonFollow,
  autoScrollToBottom,
  setIsShowButtonFollow,
  stop,
  sendMessage,
  importMessage,
}) => {
  const [isReasoningMode, setIsReasoningMode] = useState(false);
  const [isWebSearchEnabled, setIsWebSearchEnabled] = useState(false); // Track web search state
  const [isMounted, setIsMounted] = useState(false); // Track whether component is mounted

  const { usedPrompt, setUsePrompt } = usePromptLibrary();

  const handleVoiceInput = () => {
    console.log("Voice input activated");
  };

  const handleImageAttach = () => {
    console.log("Image attach functionality");
  };

  const toggleWebSearch = () => {
    setIsWebSearchEnabled((prev) => !prev); // Toggle web search on/off
    console.log(`Web search ${isWebSearchEnabled ? "disabled" : "enabled"}`);
  };

  // Ensure that tooltips only render on the client-side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const { isOpenOne: isSidebarOpen } = useSidebarContext();
  const isMobile = useIsMobile();

  if (!isMounted) return null; // Prevent rendering tooltips until after mount

  return (
    <div className={cn(
      "relative w-full shadow-lg bg-background transition-transform duration-500 ease-in-out",
      isMobile && isSidebarOpen ? "translate-y-72" : "translate-y-0"
    )}>
      <div className="absolute w-full flex flex-col md:px-20 px-2 py-3 space-y-4 items-center bottom-0">
        <div className={twMerge(WIDTH_CHAT_INPUT)}>
          <div className="flex h-12 space-x-2 w-full justify-center items-center">
            {!isWriting && (
              <InputHelper
                className="p-x-2"
                regenerate={regenerate}
                resetMessages={resetMessages}
                clearContext={clearContext}
                share={shareChat}
              />
            )}
            {isShowButtonFollow && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      className="animate-slide-up-down w-10 h-10 bg-[var(--bg-custom-indigo-400)]"
                      onClick={() => {
                        autoScrollToBottom(true);
                        setIsShowButtonFollow(false);
                      }}
                    >
                      <ArrowDown size={20} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Scroll to bottom</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          <div className="w-full flex flex-col space-y-1 items-start">
            {/* Display prompt if available */}
            {usedPrompt && (
              <div className="flex items-center justify-start bg-[var(--bg-custom-indigo-600)] text-white rounded-md px-3 py-2 mb-2 text-sm">
                <div className="flex items-center gap-2">
                  <strong>Prompt:</strong>
                  <span>{usedPrompt.promptName}</span>
                </div>
                <button
                  onClick={() => setUsePrompt(null)}
                  className="hover:text-[var(--text-custom-red-300)]"
                >
                  <X size={16} />
                </button>
              </div>
            )}
            <div className="w-full flex flex-col space-y-2">
              <InputMessage
                isWriting={isWriting}
                sendMessage={sendMessage}
                importMessage={importMessage}
                stop={stop}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
