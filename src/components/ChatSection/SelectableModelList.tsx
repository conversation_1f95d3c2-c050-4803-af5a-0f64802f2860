import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Icon, IconName } from '../Icon';
import { usePopup } from '@/context/usePopUp';
import { CustomPopUp } from '../Popup';
import { ModelSettings } from '../ModelSettings';
import { twMerge } from 'tailwind-merge';
import { Model } from '@/model/ai_model';
import { useModel } from '@/context/useModel';
import { useChatRoom } from '@/context/useChatRoom';

export function SelectableModelList({ onClose }: { onClose: () => void }) {
  const { models, defaultModel } = useModel();
  const { openPopup } = usePopup();
  const ref = useRef<HTMLDivElement>(null);
  const { openRoom, openChatRoom } = useChatRoom();
  const roomId = openRoom?.id || '';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const currentModel = useMemo(() => {
    if (!openRoom) {
      return null;
    }
    return models.findLast((e) => e.id === openRoom.model) || defaultModel;
  }, [models, openRoom, defaultModel]);

  const filteredModels = useMemo(
    () => models.filter((e) => e.id !== currentModel?.id),
    [currentModel, models]
  );

  return (
    <div
      ref={ref}
      className='
      bg-white shadow-lg
      min-w-[280px] max-w-md h-[60vh] max-h-[28rem]
      flex flex-col rounded-xl
      sm:min-w-[300px] sm:h-[70vh] sm:max-h-[32rem]
      sm:w-96
    '
    >
      <div className='flex flex-col overflow-y-auto w-full h-full'>
        {currentModel && <ModelItem model={currentModel} onClick={onClose} />}
        <div className='h-1 w-full bg-[var(--bg-custom-slate-300)]' />
        {filteredModels.map((model) => (
          <ModelItem
            key={model.id}
            model={model}
            onClick={() => {
              if (openRoom) {
                openChatRoom({ ...openRoom, model: model.id });
              }
              onClose();
            }}
          />
        ))}
      </div>
      <div className='flex justify-between shadow p-1 items-center'>
        <button
          className='p-2 hover:bg-[var(--bg-custom-gray-400)] w-full flex items-center space-x-2 sm:space-x-3 rounded text-sm sm:text-base'
          onClick={() => {
            onClose();
            openPopup(
              new CustomPopUp({
                context: 'plugin-settings',
                view: <ModelSettings />,
              })
            );
          }}
        >
          <Icon iconName='AI' />
          <label>Model Setting for Current Chat</label>
        </button>
      </div>
    </div>
  );
}

interface ModelItemProps {
  model: Model;
  onClick: () => void;
}

export function ModelItem({ model, onClick }: ModelItemProps) {
  return (
    <div
      className='flex justify-between shadow p-3 sm:p-5 items-center cursor-pointer hover:bg-[var(--bg-custom-slate-300)]'
      onClick={onClick}
    >
      <div className='flex space-x-2 items-center'>
        <Icon iconName='AI' />
        <label className='font-bold text-sm sm:text-base'>{model.name}</label>
      </div>
      <div className='flex space-x-1 sm:space-x-2 items-center'>
        {model.features.plugin && (
          <Tooltip text='Plugin enabled'>
            <Icon iconName='Plugin' />
          </Tooltip>
        )}
        {model.features.vision && (
          <Tooltip text='Vision enabled'>
            <Icon iconName='Vision' />
          </Tooltip>
        )}
        {model.config.contextLimit > 0 && (
          <div className='flex space-x-1 sm:space-x-2'>
            <Tooltip
              text={`${model.config.contextLimit} context enabled`}
              className='flex items-center space-x-1 sm:space-x-2'
            >
              <Icon iconName='AI' />
              <label className='text-sm sm:text-base'>
                {model.config.contextLimit} M
              </label>
            </Tooltip>
          </div>
        )}
      </div>
    </div>
  );
}

interface TooltipProps {
  children: React.ReactNode;
  text: string;
  className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({ children, text, className }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const childRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateTooltipPosition = () => {
      if (childRef.current && isVisible) {
        const rect = childRef.current.getBoundingClientRect();
        setTooltipPosition({
          top: rect.top + window.scrollY - 10, // 10px above the child
          left: rect.left + window.scrollX + rect.width / 2,
        });
      }
    };

    updateTooltipPosition(); // Update position initially
    window.addEventListener('resize', updateTooltipPosition); // Update on resize
    return () => {
      window.removeEventListener('resize', updateTooltipPosition);
    };
  }, [isVisible]);

  const handleMouseEnter = () => {
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  const tooltipStyle = {
    position: 'fixed' as 'fixed',
    top: tooltipPosition ? tooltipPosition.top : 0,
    left: tooltipPosition ? tooltipPosition.left : 0,
    transform: 'translate(-50%, -100%)',
    backgroundColor: '#4a4a4a',
    color: '#fff',
    padding: '8px',
    borderRadius: '4px',
    whiteSpace: 'nowrap',
    pointerEvents: 'none' as 'none',
    zIndex: 9999,
  };

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={childRef}
      className={className}
    >
      {children}
      {isVisible && tooltipPosition && <div style={tooltipStyle}>{text}</div>}
    </div>
  );
};
