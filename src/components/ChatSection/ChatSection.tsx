import Welcome from "../Welcome";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useCookies } from "react-cookie";
import { DisplayedMessage } from "@/model/displayedMessage";
import ChatView from "../ChatUI/TomioChat/ChatView";

import Analytics from "@/services/analytics";
import ChatRoomViewModel from "@/viewModel/chatRoomViewModel";
import { usePromptLibrary } from "@/context/usePromptLibrary";
import LocalRepo from "@/repo/chatMessages/local";
import { useChatRoom } from "@/context/useChatRoom";
import { ChatInput } from "./ChatInput";
import { ChatNavBar } from "./ChatNavBar";
import { Dialog } from "@/components/Dialog";
import ChatImportExportDialog from "../Dialog/ChatImportExport";
import { useModel } from "@/context/useModel";
import { usePlugin } from "@/context/usePlugin";
import { usePopup } from "@/context/usePopUp";
import { CustomPopUp, GeneralPopUp } from "../Popup";
import { CustomError, REQUIRED_KEY_NOT_FOUND } from "@/model/custom_error";
import { ModelSettings } from "../ModelSettings";
import { useAIAgent } from "@/context/useAIAgents";
import { useChatRoomAIAgent } from "@/context/useChatRoomAgent";
import AIAgentDetailView from "../AIAgentDetailView";
import FeatureList from "../FeatureList";
import { features } from "@/app/const/features";
import { AIAgentViews } from "../AIAgentView";

const COOKIE_NAME = "tomioai-chat-session";

interface ChatSectionProps {
  setLeftBar: (status: boolean) => void;
  setRightBar: (status: boolean) => void;
  rightBar: boolean;
  leftBar: boolean;
}

const ChatSection: React.FC<ChatSectionProps> = ({
  setLeftBar,
  setRightBar,
  rightBar,
  leftBar,
}) => {
  const { usedPrompt } = usePromptLibrary();
  const vmRef = useRef<ChatRoomViewModel | null>(null);
  const [exportImport, setExportImport] = useState(false);
  const { models, defaultModel } = useModel();
  const { openRoom, openChatRoom, viewModel: chatRoomVM } = useChatRoom();
  const { plugins, pluginsByIdMap } = usePlugin();
  const { agents } = useAIAgent();
  const { selectedAgent, setSelectedAgent, setChatRoom } = useChatRoomAIAgent();
  // MARK: Autoscroll

  const scrollWrapperRef = useRef<HTMLDivElement>(null!);
  const [isShowButtonFollow, setIsShowButtonFollow] = useState(false);

  const scrollListener = () => {
    if (scrollWrapperRef.current) {
      const viewport = scrollWrapperRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (viewport) {
        const isAtBottom =
          viewport.scrollHeight - viewport.scrollTop - viewport.clientHeight <
          100;
        setIsShowButtonFollow(!isAtBottom);
      }
    }
  };

  function getDiff(): number {
    if (scrollWrapperRef.current) {
      const totalOffsetTopThreshold =
        scrollWrapperRef.current.scrollTop +
        scrollWrapperRef.current.offsetHeight;
      const diff =
        scrollWrapperRef.current.scrollHeight - totalOffsetTopThreshold;
      return diff;
    }
    return 0;
  }
  function shouldScrollBecauseReachBottom(): boolean {
    if (scrollWrapperRef.current) {
      const shouldScrollBecauseReachBottom = getDiff() < 50;
      return shouldScrollBecauseReachBottom;
    } else {
      return false;
    }
  }

  const autoScrollToBottom = (shouldScroll: boolean) => {
    if (scrollWrapperRef.current && shouldScroll) {
      const viewport = scrollWrapperRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (viewport) {
        viewport.scrollTo({
          top: viewport.scrollHeight,
          behavior: "smooth",
        });
      }
    }
  };
  // Reset Message
  const resetMessages = () => {
    vmRef.current?.resetMessages();
    Analytics.getInstance().track("Reset Chat", {});
  };

  const shareChat = async () => {
    const messagesToShare = vmRef.current?.prepareMessageForSharing();

    if (!messagesToShare || messagesToShare.length === 0) {
      alert("No messages to share.");
      return;
    }

    try {
      const response = await fetch("/api/v1/generate-share-chats-url", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ messages: messagesToShare }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate share link");
      }

      const { id } = await response.json();

      Analytics.getInstance().track("Share Chat", {
        messageCount: messagesToShare.length,
      });

      const url = `/shared/chat/${id}`;
      window.open(url, "_blank");
    } catch (error) {
      Analytics.getInstance().track("Failed Share Chat", {
        messageCount: messagesToShare.length,
        error,
      });
      console.error("Error sharing chat", error);
      alert("Error sharing chat");
    }
  };

  //Clear Context
  const clearContext = () => {
    vmRef.current?.clearContext();
    Analytics.getInstance().track("Clear Context", {});
  };

  // Delete message
  const deleteMessage = (id: string) => {
    vmRef.current?.deleteMessage(id);
    Analytics.getInstance().track("Delete Message", {});
  };

  const onReportRendering = async (
    markdown: string,
    screenshootBase64Png: string
  ) => {
    const formData = new FormData();

    console.log("SCREENHSOOTBASE", screenshootBase64Png);
    const blob = await fetch(screenshootBase64Png).then((res) => res.blob());
    const file = new File([blob], "screenshot.png", { type: "image/png" });
    console.log("FILE", file, blob);

    // Append fields to FormData
    formData.append("markdown", markdown);
    formData.append(
      "clientInfo",
      JSON.stringify({ userAgent: navigator.userAgent })
    );

    // Append screenshot
    formData.append("screenshot", file);

    try {
      const response = await fetch("/api/v1/bug-report", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        console.log("Bug report uploaded successfully:", result);
        Analytics.getInstance().track("Report Rendering", {});
        return result;
      } else {
        console.error("Failed to upload bug report:", result);
        return null;
      }
    } catch (error) {
      console.error("Error submitting bug report:", error);
      return null;
    }
  };

  // Regenerate Content
  const regenerate = () => {
    vmRef.current?.regenerateMessages();
    Analytics.getInstance().track("Regenerate Answer", {});
  };

  // MARK: Hit API

  const [messages, setMessages] = useState<DisplayedMessage[]>([]);
  const [cookie, setCookie] = useCookies([COOKIE_NAME]);
  const [loading, setLoading] = useState(false);
  const [isWriting, setWriting] = useState(false);
  const [chatMessagesUi, setChatMessagesUi] = useState<"chatgpt" | "tomiochat">(
    "tomiochat"
  );
  const { openPopup } = usePopup();

  function setInitialMessages(vm: ChatRoomViewModel) {
    const messages = vm.getDisplayedMessages();
    setMessages([...messages]);
  }

  // MARK: ViewModel

  // ENDMARK: ViewModel

  useEffect(() => {
    if (!cookie[COOKIE_NAME]) {
      const randomId = Math.random().toString(36).substring(7);
      setCookie(COOKIE_NAME, randomId);
    }

    if (scrollWrapperRef.current) {
      scrollWrapperRef.current.addEventListener("scroll", scrollListener);
    }

    return () => {
      if (scrollWrapperRef.current) {
        scrollWrapperRef.current.removeEventListener("scroll", scrollListener);
      }
    };
  }, [
    cookie,
    setCookie,
    scrollWrapperRef,
    isShowButtonFollow,
    setIsShowButtonFollow,
  ]);

  const stopConversationRef = useRef<boolean>(false);

  async function newEmptyChatRoom() {
    const newChatRoom = await chatRoomVM.addChatRoom("", {});
    openChatRoom(newChatRoom);
  }

  const usedModel = useMemo(() => {
    if (openRoom === null) {
      return defaultModel;
    }
    return models.findLast((e) => e.id === openRoom.model) || defaultModel;
  }, [models, openRoom]);

  useEffect(() => {
    const user = cookie[COOKIE_NAME];
    if (openRoom === null) {
      newEmptyChatRoom();
      return;
    }

    const vm = new ChatRoomViewModel(user, new LocalRepo(openRoom), usedModel);
    vm.setLoading = setLoading;
    vm.setWriting = setWriting;
    vm.setShowFollowButton = setIsShowButtonFollow;
    vm.onMessagesUpdated = () => {
      setInitialMessages(vm);
      autoScrollToBottom(shouldScrollBecauseReachBottom());
    };
    vm.onDidSendUserMessage = () => {
      if (shouldScrollBecauseReachBottom()) {
        setTimeout(() => {
          autoScrollToBottom(true);
          setIsShowButtonFollow(true);
        }, 300);
      }
    };
    vm.loadMessages();
    vmRef.current = vm;

    return () => {
      if (openRoom.messages.length === 0 && openRoom.name === "") {
        chatRoomVM.deleteChatRoom(openRoom.id);
      }
    };
  }, [openRoom?.id]);

  useEffect(() => {
    if (openRoom && vmRef.current) {
      const model =
        models.findLast((e) => e.id === openRoom.model) || defaultModel;
      const tools = plugins.filter((e) => openRoom.plugins.includes(e.id));
      vmRef.current.updateHandler(model, tools);
      chatRoomVM.updateRoomModel(openRoom.id, openRoom.model);
      chatRoomVM.updateRoomPlugins(openRoom.id, openRoom.plugins);
    }
  }, [openRoom, plugins, models, vmRef]);

  useEffect(() => {}, [usedModel]);

  useEffect(() => {
    if (openRoom) {
      setChatRoom(openRoom);
    }
  }, [openRoom?.id]);

  function stop() {
    vmRef.current?.stop();
  }

  function importMessage(mesage: {
    role: "user" | "assistant";
    content: string;
    source?: string;
  }) {
    vmRef.current?.importMessage(mesage);
  }

  async function sendMessage(msg: string) {
    try {
      await vmRef.current?.sendMessage(
        msg,
        usedPrompt,
        plugins,
        selectedAgent || undefined
      );
      if (openRoom && openRoom?.name === "") {
        const room = {
          ...openRoom,
          name: msg.slice(0, 20),
        };
        chatRoomVM.updateRoomName(openRoom.id, room.name);
        openChatRoom(room);
      }
      Analytics.getInstance().track("Send Message", {
        length: msg.length,
        has_prompt: usedPrompt !== null,
        prompt: usedPrompt?.promptName,
        model: usedModel.name,
        plugins: openRoom?.plugins.map((e) => pluginsByIdMap[e].name),
      });
    } catch (error) {
      if (
        error instanceof CustomError &&
        error.code === REQUIRED_KEY_NOT_FOUND
      ) {
        openPopup(
          new GeneralPopUp({
            context: "Setup AI Model Key",
            title:
              "AI Model API Key not set. Please setup in the Model Configuration",
            content: ``,
            buttons: [
              {
                isPrimary: true,
                onClick: () => {
                  openPopup(
                    new CustomPopUp({
                      context: "plugin-settings",
                      view: <ModelSettings model={usedModel} />,
                    }),
                    300
                  );
                },
                title: "Open Model Configuration",
              },
              {
                isPrimary: false,
                onClick: () => {},
                title: "Cancel",
              },
            ],
          })
        );
      } else {
        console.error("Unexpected error:", error);
      }
    }
  }

  async function openExportImport() {
    setExportImport(!exportImport);
  }

  const [mobileDropdownOpen, setMobileDropdownOpen] = useState(false);

  return (
    <>
      <div className="bg-[var(--chatsection-background)] p-2 flex flex-col w-full max-w-full overflow-x-hidden ">
        <ChatNavBar
          leftBar={leftBar}
          rightBar={rightBar}
          setLeftBar={setLeftBar}
          setRightBar={setRightBar}
          openExportImport={openExportImport}
          openRoom={openRoom}
          messages={messages}
        />
        <ChatView
          ui={chatMessagesUi}
          className="flex-grow"
          loading={loading}
          onDelete={deleteMessage}
          onReportRendering={onReportRendering}
          messages={messages}
          scrollRef={scrollWrapperRef}
        >
          <Welcome key={openRoom?.id} setChatMessagesUi={setChatMessagesUi} />
          {messages.length == 0 && (
            <div className="flex flex-col items-center space-y-24 w-full">
              <FeatureList features={features} />
              {!selectedAgent && agents.length > 0 && (
                <AIAgentViews agents={agents} />
              )}
              {selectedAgent && (
                <AIAgentDetailView
                  agent={selectedAgent}
                  onBack={() => setSelectedAgent(null)}
                  onSelectQuery={sendMessage}
                />
              )}
            </div>
          )}
        </ChatView>
        <ChatInput
          key={openRoom?.id}
          isWriting={isWriting}
          regenerate={regenerate}
          resetMessages={resetMessages}
          shareChat={shareChat}
          clearContext={clearContext}
          isShowButtonFollow={isShowButtonFollow}
          autoScrollToBottom={autoScrollToBottom}
          setIsShowButtonFollow={setIsShowButtonFollow}
          stop={stop}
          sendMessage={sendMessage}
          importMessage={importMessage}
        />
      </div>
      {exportImport && (
        <Dialog>
          <ChatImportExportDialog
            roomName={openRoom?.name}
            messages={messages}
            onClose={() => setExportImport(false)}
            onImport={(mode, messages) => {
              switch (mode) {
                case "append":
                  vmRef.current?.appendMessages(messages);
                  break;
                case "overwrite":
                  vmRef.current?.replaceMessages(messages);
                  break;
              }
            }}
          />
        </Dialog>
      )}
    </>
  );
};

export default ChatSection;
