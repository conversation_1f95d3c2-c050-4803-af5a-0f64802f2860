import React, { useEffect, useRef } from "react";
import { Icon } from "../Icon";
import { usePopup } from "@/context/usePopUp";
import { CustomPopUp } from "../Popup";
import { PluginSettings } from "../PluginSettings";
import { Plugin } from "@/model/plugin";
import { usePlugin } from "@/context/usePlugin";
import { useChatRoom } from "@/context/useChatRoom";

export function SelectablePluginList({ onClose }: { onClose: () => void }) {
  const { plugins } = usePlugin();
  const { openRoom, openChatRoom } = useChatRoom();
  const ref = useRef<HTMLDivElement>(null);
  const { openPopup } = usePopup();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  const handleTogglePlugin = (pluginId: string) => {
    if (!openRoom) return;

    const updatedPlugins = openRoom.plugins.includes(pluginId)
      ? openRoom.plugins.filter((id) => id !== pluginId)
      : [...openRoom.plugins, pluginId];

    openChatRoom({ ...openRoom, plugins: updatedPlugins });
  };

  return (
    <div
      ref={ref}
      className="bg-white shadow-lg
      min-w-[300px] max-w-md h-[70vh] max-h-[32rem]
      flex flex-col rounded-xl
      sm:w-96 sm:h-96"
    >
      <div className="flex flex-col overflow-y-auto w-full h-full">
        {plugins.map((plugin) => (
          <PluginItem
            key={plugin.name}
            plugin={plugin}
            isEnabled={openRoom?.plugins.includes(plugin.id) || false}
            onToggle={() => handleTogglePlugin(plugin.id)}
          />
        ))}
      </div>
      <div className="flex justify-between shadow p-1 items-center">
        <button
          className="p-2 hover:bg-[var(--bg-custom-gray-400)] w-full flex items-center space-x-3 rounded"
          onClick={() => {
            onClose();
            openPopup(
              new CustomPopUp({
                context: "plugin-settings",
                view: <PluginSettings />,
              })
            );
          }}
        >
          <Icon iconName="Plugin" />
          <label>Plugin Settings for Current Chat</label>
        </button>
      </div>
    </div>
  );
}

interface PluginItemProps {
  plugin: Plugin;
  isEnabled: boolean;
  onToggle: () => void;
}

export function PluginItem({ plugin, isEnabled, onToggle }: PluginItemProps) {
  return (
    <div className="flex justify-between shadow p-5 items-center hover:bg-[var(--bg-custom-purple-300)]">
      <div className="flex space-x-2 items-center">
        <label className="font-medium">
          {plugin.icon} {plugin.name}
        </label>
      </div>
      <div className="flex space-x-2 items-center">
        <button
          data-element-id="plugins-switch-enabled"
          className={`${
            isEnabled
              ? "bg-[var(--bg-custom-blue-600)]"
              : "bg-[var(--bg-custom-gray-300)]"
          } h-6 w-11 cursor-pointer relative inline-flex flex-shrink-0 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2`}
          role="switch"
          aria-checked={isEnabled}
          onClick={onToggle}
        >
          <span
            aria-hidden="true"
            className={`${
              isEnabled ? "translate-x-5" : "translate-x-0"
            } h-5 w-5 pointer-events-none inline-block transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
          ></span>
        </button>
      </div>
    </div>
  );
}
