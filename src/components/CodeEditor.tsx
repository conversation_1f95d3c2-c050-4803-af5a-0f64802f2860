import React from "react";
import { Editor } from "@monaco-editor/react";

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const CodeEditor: React.FC<CodeEditorProps> = ({ value, onChange }) => {
  return (
    <Editor
      height="200px"
      language="javascript"
      theme="xcode"
      value={value}
      options={{
        minimap: {
          enabled: false,
          showSlider: "mouseover",
        },
      }}
      onChange={(value) => {
        if (value !== undefined) {
          onChange(value);
        }
      }}
    />
  );
};

export default CodeEditor;
