import React from "react";
import { twMerge } from "tailwind-merge";

interface TextInfoProps {
  children: string;
  className?: string;
}

const TextInfo: React.FC<TextInfoProps> = ({ children, className }) => {
  return (
    <div
      className={twMerge(
        "text-black px-1 text-center rounded bg-gradient-to-r from-blue-600 via-green-500 to-indigo-400 rounded-br-none font-bold text-xs",
        className
      )}
    >
      {children}
    </div>
  );
};

export default TextInfo;
