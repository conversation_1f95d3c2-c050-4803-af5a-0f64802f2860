import React from "react";
import <PERSON> from "next/link";
import { Button } from "./Elements/Button";
import AIBotConfiguration from "./AIBotConfiguration";
import Dropdown from "./Dropdown";

const links = [
  { href: "/dashboard", label: "Dashboard" },
  { href: "/privacy", label: "Privacy" },
  { href: "/terms", label: "Terms" },
  { href: "/faqs", label: "FAQs" },
  { href: "https://discord.gg/hhUAbE2EbV", label: "Discord" },
  { href: "https://docs.tomioai.com", label: "Docs" },
  { href: "https://blog.tomioai.com", label: "Blog" },
  { href: "/contact", label: "Contact" },
];

const Welcome: React.FC<{
  setChatMessagesUi: (arg: "chatgpt" | "tomiochat") => void;
}> = ({ setChatMessagesUi }) => {
  return (
    <div className="w-full flex flex-col items-center gap-3 leading-none mt-5 mb-5 max-w-2xl mx-auto px-2 sm:px-4">
      <div className="circle w-10 h-10 bg-[#150B31] rounded-full"></div>
      <div className="font-bold text-xl sm:text-2xl text-center">
        TomioAI always here to help you!
      </div>
      <div className="font-medium text-center text-sm sm:text-base">
        Get instant answer, helpful recommendation, quick summary, and engaging
        conversation with our AI-powered App
      </div>
      <div className="flex flex-wrap justify-center font-medium text-xs gap-x-1 gap-y-1 text-center">
        <span>TomioAI.com © 2023 |</span>
        {links.map((link, idx) => (
          <React.Fragment key={link.href}>
            <Link
              href={link.href}
              rel="noopener noreferrer"
              target="_blank"
              className="hover:underline hover:text-secondary transition-colors px-1 "
            >
              {link.label}
            </Link>
            {idx !== links.length - 1 && <span>|</span>}
          </React.Fragment>
        ))}
      </div>
      {/* <AIBotConfiguration /> */}
      <div className="hidden md:block">
        <Dropdown
        label="Select Chat UI"
        options={[
          {
            value: "tomiochat",
            label: "TomioChat",
            onClick: () => setChatMessagesUi("tomiochat"),
          },
          {
            value: "chatgpt",
            label: "ChatGPT",
            onClick: () => setChatMessagesUi("chatgpt"),
          },
          {
            value: "indonesia",
            label: "Indonesia",
            status: "incoming",
            onClick: () => {},
          },
        ]}
      />
      </div>
    </div>
  );
};

export default Welcome;
