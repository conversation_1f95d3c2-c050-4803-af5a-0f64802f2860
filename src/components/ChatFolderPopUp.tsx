import { useChatFolder } from "@/context/useChatFolder";
import { ChatFolder } from "@/model/chatFolder";
import { Button } from "./ui/button";
import ChatFolderListViewModel from "@/viewModel/chatFolderViewModel";
import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";

interface ChatFolderPopupProps {}

const ChatFolderPopup: React.FC<ChatFolderPopupProps> = ({}) => {
  const { folder, closeChatFolder, viewModel } = useChatFolder();
  const [editableChatFolder, setEditableChatFolder] =
    useState<ChatFolder | null>(null);

  useEffect(() => {
    setEditableChatFolder(folder);
  }, [folder?.id]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!editableChatFolder) return;
    const { name, value } = e.target;
    setEditableChatFolder({ ...editableChatFolder, [name]: value });
  };

  const handleSave = async () => {
    if (!editableChatFolder) return;
    if (editableChatFolder.id) {
      viewModel.updateFolder(editableChatFolder);
    } else {
      viewModel.saveNewFolder(editableChatFolder);
    }
    closeChatFolder();
    setEditableChatFolder(null);
  };

  if (!editableChatFolder) return null;

  return createPortal(
    <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center bg-gray-500 bg-opacity-50 z-[9999]">
      <div className="bg-white p-4 w-[90vw] max-w-md rounded shadow-md bg-background ">
        <h2 className="font-semibold mb-4">Configure Folder</h2>
        <div className="mb-4">
          <label
            className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1"
            htmlFor="folderName"
          >
            Folder Name
          </label>
          <input
            type="text"
            id="folderName"
            name="folderName"
            value={editableChatFolder.folderName}
            onChange={handleInputChange}
            className="w-full border rounded px-3 py-2 outline-none"
          />
        </div>
        <div className="mb-4">
          <label
            className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1"
            htmlFor="folderDescription"
          >
            Folder Description
          </label>
          <textarea
            id="folderDescription"
            name="folderDescription"
            value={editableChatFolder.folderDescription}
            onChange={handleInputChange}
            className="w-full border rounded px-3 py-2 outline-none resize-none"
            rows={4}
          />
        </div>
        <div className="flex justify-end space-x-3">
          <Button
            variant="secondary"
            onClick={() => {
              closeChatFolder();
              setEditableChatFolder(null);
            }}
          >
            Cancel
          </Button>
          <Button
            className="justify-center rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[var(--bg-custom-indigo-500)]"
            onClick={handleSave}
          >
            Save
          </Button>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ChatFolderPopup;
