"use client";

import React, { useEffect, useState } from "react";
import { usePromptLibrary } from "@/context/usePromptLibrary";
import { PromptData } from "@/model/promptData";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import clsx from "clsx";

interface PromptLibraryPopupProps {}

const PromptLibraryPopup: React.FC<PromptLibraryPopupProps> = () => {
  const { prompt, closePromptLibrary, viewModel } = usePromptLibrary();
  const [editablePromptData, setEditablePromptData] =
    useState<PromptData | null>(null);
  const [touched, setTouched] = useState({ name: false, instruction: false });

  useEffect(() => {
    setEditablePromptData(prompt);
    setTouched({ name: false, instruction: false });
  }, [prompt?.id]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!editablePromptData) return;
    const { name, value } = e.target;
    setEditablePromptData({ ...editablePromptData, [name]: value });
    setTouched((prev) => ({
      ...prev,
      [name === "promptName" ? "name" : "instruction"]: true,
    }));
  };

  const handleSelectChange = (value: string) => {
    if (!editablePromptData) return;
    setEditablePromptData({ ...editablePromptData, promptType: value });
  };

  const handleSave = async () => {
    if (!editablePromptData) return;
    if (editablePromptData.id === "") {
      await viewModel.addNewPrompt(editablePromptData);
    } else {
      await viewModel.updatePrompt(editablePromptData);
    }
    closePromptLibrary();
    setEditablePromptData(null);
  };

  const handleDelete = async () => {
    if (!editablePromptData) return;
    await viewModel.deletePrompt(editablePromptData);
    closePromptLibrary();
    setEditablePromptData(null);
  };

  if (!editablePromptData) return null;

  const isNameEmpty = !editablePromptData.promptName?.trim();
  const isInstructionEmpty = !editablePromptData.promptInstruction?.trim();
  const disableSave = isNameEmpty || isInstructionEmpty;

  return (
    <Dialog
      open={!!editablePromptData}
      onOpenChange={(open) => {
        if (!open) {
          closePromptLibrary();
          setEditablePromptData(null);
        }
      }}
    >
      <DialogContent className="max-w-xl w-[90vw] sm:w-full z-[9999] rounded-lg ">
        <DialogHeader>
          <DialogTitle>Configure Your Prompt</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="promptName">Prompt Name</Label>
            <Input
              id="promptName"
              name="promptName"
              value={editablePromptData.promptName}
              onChange={handleInputChange}
              className={clsx({
                "border-[var(--border-custom-red-500)] focus-visible:ring-red-500":
                  touched.name && isNameEmpty,
              })}
            />
            {touched.name && isNameEmpty && (
              <p className="text-sm text-[var(--text-custom-red-500)]">
                Prompt name is required.
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="promptDescription">Description</Label>
            <Textarea
              id="promptDescription"
              name="promptDescription"
              value={editablePromptData.promptDescription}
              onChange={handleInputChange}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="promptType">Prompt Type</Label>
            <Select
              value={editablePromptData.promptType || "prefix"}
              onValueChange={handleSelectChange}
            >
              <SelectTrigger id="promptType">
                <SelectValue placeholder="Select prompt type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="prefix">Prefix</SelectItem>
                <SelectItem value="suffix">Suffix</SelectItem>
                <SelectItem value="template">Template</SelectItem>
              </SelectContent>
            </Select>

            {editablePromptData.promptType === "prefix" && (
              <p className="text-xs text-muted-foreground mt-1">
                <strong>Prefix:</strong> This prompt will be inserted{" "}
                <em>before</em> the user input.
              </p>
            )}
            {editablePromptData.promptType === "suffix" && (
              <p className="text-xs text-muted-foreground mt-1">
                <strong>Suffix:</strong> This prompt will be inserted{" "}
                <em>after</em> the user input.
              </p>
            )}
            {editablePromptData.promptType === "template" && (
              <p className="text-xs text-muted-foreground mt-1">
                <strong>Template:</strong> Use{" "}
                <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono">
                  {"{{input}}"}
                </code>{" "}
                to insert user input in your instruction.
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="promptInstruction">Instruction</Label>
            <Textarea
              id="promptInstruction"
              name="promptInstruction"
              value={editablePromptData.promptInstruction}
              onChange={handleInputChange}
              rows={4}
              className={clsx({
                "border-[var(--border-custom-red-500)] focus-visible:ring-red-500":
                  touched.instruction && isInstructionEmpty,
              })}
            />
            {touched.instruction && isInstructionEmpty && (
              <p className="text-sm text-[var(--text-custom-red-500)]">
                Prompt instruction is required.
              </p>
            )}
          </div>

          <div className="flex justify-between items-center pt-4 w-full">
            <div>
              {editablePromptData.id && (
                <Button variant="destructive" onClick={handleDelete}>
                  Delete
                </Button>
              )}
            </div>
            <div className="flex gap-3">
              <Button
                variant="primary-outline"
                onClick={() => {
                  closePromptLibrary();
                  setEditablePromptData(null);
                }}
              >
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={disableSave}>
                Save
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PromptLibraryPopup;
