import { useState, useEffect, ReactNode } from "react";

interface ButtonTimerProps {
  onClick: () => void;
  secondaryElement: ReactNode;
  timer: number;
  children: (onClick: () => void) => ReactNode; // Pass onClick to children
}

const ButtonTimer: React.FC<ButtonTimerProps> = ({
  onClick,
  secondaryElement,
  timer,
  children,
}) => {
  const [showSecondary, setShowSecondary] = useState(false);

  const handleClick = () => {
    setShowSecondary(true);
    setInterval(() => {
      setShowSecondary(false);
    }, timer);
    onClick();
  };

  return <>{showSecondary ? secondaryElement : children(handleClick)}</>;
};

export default ButtonTimer;
