import React, { useState } from "react";
import { Icon, IconName, IconsUsedInApplication } from "../Icon";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

const iconProviders = ["flat", "icon8", "iconsax", "other"];

interface AppearanceIconProps {}

export const AppearanceIcon: React.FC<AppearanceIconProps> = () => {
  const [selectedProvider, setSelectedProvider] = useState(iconProviders[0]);
  const [selectedIcon, setSelectedIcon] = useState("");

  const handleProviderChange = (value: string) => {
    setSelectedProvider(value);
    setSelectedIcon("");
  };

  const handleIconChange = (icon: string) => {
    setSelectedIcon(icon);
  };

  const icons = IconsUsedInApplication;

  return (
    <div className="space-y-6 w-full h-full p-4">
      <h2 className="text-lg font-semibold">Icon Settings</h2>

      <div className="space-y-2">
        <Label>Select Icon Provider</Label>
        <Select
          onValueChange={handleProviderChange}
          defaultValue={selectedProvider}
        >
          <SelectTrigger>
            <SelectValue placeholder="Choose provider" />
          </SelectTrigger>
          <SelectContent>
            {iconProviders.map((provider) => (
              <SelectItem key={provider} value={provider}>
                {provider.charAt(0).toUpperCase() + provider.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Select Icon</Label>
        <div className="grid grid-cols-4 gap-4">
          {icons.map((icon) => (
            <Card
              key={icon}
              onClick={() => handleIconChange(icon)}
              className={`flex flex-col items-center p-2 cursor-pointer transition hover:shadow-md ${
                selectedIcon === icon
                  ? "border-2 border-[var(--border-custom-blue-500)]"
                  : ""
              }`}
            >
              <Icon iconName={icon as IconName} size={36} />
              <span className="text-xs text-muted-foreground mt-2 text-center">
                {icon}
              </span>
            </Card>
          ))}
        </div>
      </div>

      {selectedIcon && (
        <div className="mt-6 space-y-2">
          <h3 className="font-semibold">Selected Icon:</h3>
          <div className="flex items-center space-x-4">
            <Icon iconName={selectedIcon as IconName} size={40} />
            <span className="text-[var(--text-custom-gray-700)]">
              {selectedIcon}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppearanceIcon;
