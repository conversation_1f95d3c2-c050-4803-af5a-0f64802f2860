import React, { useState, useEffect } from "react";
import { useAppSettings } from "@/context/useAppSetting";
import { Button } from "../Elements/Button";
import { SettingToggle } from "./SettingToggle";

interface AIGeneralProps {}

export const AIGeneral: React.FC<AIGeneralProps> = () => {
  const { settings, setSettings } = useAppSettings();

  const [streamAIResponse, setStreamAIResponse] = useState(
    settings.ai.streamAIResponse || false
  );
  const [autoGenerateChatTitle, setAutoGenerateChatTitle] = useState(
    settings.ai.autoGenerateChatTitle || false
  );
  const [autoContinueResponse, setAutoContinueResponse] = useState(
    settings.ai.autoContinueResponse || false
  );
  const [autoSaveChat, setAutoSaveChat] = useState(
    settings.ai.autoSaveChat || false
  );
  const [submitWithEnter, setSubmitWithEnter] = useState(
    settings.ai.submitWithEnter ?? true
  );
  const [autoFocusInput, setAutoFocusInput] = useState(
    settings.ai.autoFocusInput ?? true
  );
  const [defaultTone, setDefaultTone] = useState(
    settings.ai.defaultTone || "neutral"
  );
  const [defaultSystemPrompt, setDefaultSystemPrompt] = useState(
    settings.ai.defaultSystemPrompt || ""
  );
  const [defaultModel, setDefaultModel] = useState(
    settings.ai.defaultModel || "gpt-4"
  );
  const [enableWebAccess, setEnableWebAccess] = useState(
    settings.ai.enableWebAccess || false
  );
  const [enablePlugins, setEnablePlugins] = useState(
    settings.ai.enablePlugins || false
  );
  const [smartRetry, setSmartRetry] = useState(settings.ai.smartRetry || false);
  const [showTokenUsage, setShowTokenUsage] = useState(
    settings.ai.showTokenUsage || false
  );

  useEffect(() => {
    setSettings({
      ...settings,
      ai: {
        ...settings.ai,
        streamAIResponse,
        autoGenerateChatTitle,
        autoContinueResponse,
        autoSaveChat,
        submitWithEnter,
        autoFocusInput,
        defaultTone,
        defaultSystemPrompt,
        defaultModel,
        enableWebAccess,
        enablePlugins,
        smartRetry,
        showTokenUsage,
      },
    });
  }, [
    streamAIResponse,
    autoGenerateChatTitle,
    autoContinueResponse,
    autoSaveChat,
    submitWithEnter,
    autoFocusInput,
    defaultTone,
    defaultSystemPrompt,
    defaultModel,
    enableWebAccess,
    enablePlugins,
    smartRetry,
    showTokenUsage,
  ]);

  return (
    <div className="w-full h-full p-4 space-y-6">
      <h2 className="text-xl font-bold">AI Settings</h2>

      {/* Behavior Settings */}
      <div>
        <h3 className="font-semibold mb-2">Behavior</h3>
        <SettingToggle
          label="Stream AI Response"
          value={streamAIResponse}
          onChange={setStreamAIResponse}
          description="Enable this to stream AI responses in real-time."
        />
        <SettingToggle
          label="Autogenerate Title for New Chat"
          value={autoGenerateChatTitle}
          onChange={setAutoGenerateChatTitle}
        />
        <SettingToggle
          label="Auto-continue on Token Limit"
          value={autoContinueResponse}
          onChange={setAutoContinueResponse}
          description="Automatically continue generation when stopped due to length."
        />
        <SettingToggle
          label="Auto Save Chat"
          value={autoSaveChat}
          onChange={setAutoSaveChat}
        />
        <SettingToggle
          label="Smart Retry on Error"
          value={smartRetry}
          onChange={setSmartRetry}
        />
      </div>

      {/* Interaction Settings */}
      <div>
        <h3 className="font-semibold mb-2">Interaction</h3>
        <SettingToggle
          label="Submit with Enter"
          value={submitWithEnter}
          onChange={setSubmitWithEnter}
        />
        <SettingToggle
          label="Auto Focus Input"
          value={autoFocusInput}
          onChange={setAutoFocusInput}
        />
        <SettingDropdown
          label="Default Tone"
          value={defaultTone}
          onChange={(e) => setDefaultTone(e as any)}
          options={["neutral", "friendly", "professional", "sarcastic"]}
        />
        <div className="mt-2">
          <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
            Default System Prompt
          </label>
          <textarea
            value={defaultSystemPrompt}
            onChange={(e) => setDefaultSystemPrompt(e.target.value)}
            className="w-full p-2 border rounded"
            rows={3}
          />
        </div>
      </div>

      {/* Model Configuration */}
      <div>
        <h3 className="font-semibold mb-2">Model</h3>
        <SettingDropdown
          label="Default Model"
          value={defaultModel}
          onChange={setDefaultModel}
          options={["gpt-3.5", "gpt-4", "claude", "mistral", "gemini"]}
        />
        <div className="mt-2">
          <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
            Advanced Model Settings
          </label>
          <Button onClick={() => alert("Open model config modal")}>
            Configure per model
          </Button>
        </div>
      </div>

      {/* Integration Settings */}
      <div>
        <h3 className="font-semibold mb-2">Integration</h3>
        <SettingToggle
          label="Enable Web Access"
          value={enableWebAccess}
          onChange={setEnableWebAccess}
        />
        <SettingToggle
          label="Enable Plugins"
          value={enablePlugins}
          onChange={setEnablePlugins}
        />
      </div>

      {/* Developer Settings */}
      <div>
        <h3 className="font-semibold mb-2">Developer</h3>
        <SettingToggle
          label="Show Token Usage"
          value={showTokenUsage}
          onChange={setShowTokenUsage}
        />
      </div>
    </div>
  );
};

const SettingDropdown = ({
  label,
  value,
  onChange,
  options,
}: {
  label: string;
  value: string;
  onChange: (val: string) => void;
  options: string[];
}) => (
  <div className="mb-2">
    <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
      {label}
    </label>
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full p-2 border rounded"
    >
      {options.map((opt) => (
        <option key={opt} value={opt}>
          {opt.charAt(0).toUpperCase() + opt.slice(1)}
        </option>
      ))}
    </select>
  </div>
);

export default AIGeneral;
