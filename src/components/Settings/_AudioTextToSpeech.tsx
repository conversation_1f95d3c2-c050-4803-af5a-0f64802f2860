"use client";

import { useAppSettings } from "@/context/useAppSetting";
import React, { useState, useEffect, useMemo } from "react";
import { ExternalProviderSettings } from "../ExternalProviderSetting";
import { ExternalProvider } from "@/model/externalProvider";

import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const LOCAL_STORAGE_KEY = "custom_providers_text_to_speech";

export const AudioTextToSpeech: React.FC = () => {
  const { settings, setSettings } = useAppSettings();

  const currentSetting = useMemo(() => settings.audio.textToSpeech, [settings]);

  const [enabled, setEnabled] = useState(currentSetting.enabled);
  const [playButtonForMessage, setPlayButtonForMessage] = useState(
    currentSetting.playButtonForMessage
  );
  const [provider, setProvider] = useState({
    isCustom: currentSetting.isCustom,
    provider: currentSetting.provider,
  });
  const [autoPlay, setAutoPlay] = useState(currentSetting.autoPlay);
  const [testText, setTestText] = useState("");

  const [savedCustomProviders, setSavedCustomProviders] = useState<
    ExternalProvider[]
  >([]);

  useEffect(() => {
    setSettings({
      ...settings,
      audio: {
        ...settings.audio,
        textToSpeech: {
          enabled,
          provider: provider.provider,
          isCustom: provider.isCustom,
          playButtonForMessage,
          autoPlay,
        },
      },
    });
  }, [enabled, playButtonForMessage, autoPlay, provider]);

  function updateLocalStorage(savedCustomProviders: ExternalProvider[]) {
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify(savedCustomProviders)
    );
  }

  useEffect(() => {
    const data = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (data) {
      const savedCustomProviders = JSON.parse(data) as ExternalProvider[];
      setSavedCustomProviders(savedCustomProviders);
    }
  }, []);

  const handleSaveCustomProvider = (provider: ExternalProvider) => {
    const newData = [
      ...savedCustomProviders.filter((e) => e.id !== provider.id),
      provider,
    ];
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
  };

  const handleRemoveCustomProvider = (id: string) => {
    const newData = savedCustomProviders.filter(
      (provider) => provider.id !== id
    );
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
    setProvider({ isCustom: true, provider: undefined });
  };

  const handleTestTextToSpeech = () => {
    console.log("Testing text-to-speech with:", testText);
  };

  return (
    <div className="space-y-6 w-full h-full p-4">
      <h2 className="text-xl font-semibold">Text-to-Speech Settings</h2>

      <Card>
        <CardContent className="space-y-4 pt-6">
          <div className="flex items-center justify-between">
            <Label htmlFor="enabled">Enable Text-to-Speech</Label>
            <Switch
              id="enabled"
              checked={enabled}
              onCheckedChange={setEnabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="play-button">
              Show Play Button for Each Message
            </Label>
            <Switch
              id="play-button"
              checked={playButtonForMessage}
              onCheckedChange={setPlayButtonForMessage}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="autoplay">Autoplay When Message Finished</Label>
            <Switch
              id="autoplay"
              checked={autoPlay}
              onCheckedChange={setAutoPlay}
            />
          </div>
        </CardContent>
      </Card>

      <ExternalProviderSettings
        currentProvider={
          currentSetting.provider && {
            isCustom: currentSetting.isCustom,
            provider: currentSetting.provider,
          }
        }
        presetProviders={[
          {
            id: "ElevenLabs",
            name: "ElevenLabs",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
          {
            id: "OpenAIWhisper",
            name: "OpenAI Whisper",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
        ]}
        savedCustomProviders={savedCustomProviders}
        onSave={handleSaveCustomProvider}
        onRemove={handleRemoveCustomProvider}
        onProviderChange={(isCustom, provider) => {
          setProvider({ isCustom, provider });
        }}
      />

      <Card>
        <CardContent className="pt-6 space-y-2">
          <Label htmlFor="test-text">Test Text-to-Speech</Label>
          <div className="flex space-x-2">
            <Input
              id="test-text"
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="Enter text to test"
            />
            <Button onClick={handleTestTextToSpeech}>Test</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
