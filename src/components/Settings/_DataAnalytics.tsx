import React, { useState, useEffect, useMemo } from "react";
import { ExternalProviderSettings } from "../ExternalProviderSetting";
import { ExternalProvider } from "@/model/externalProvider";
import { useAppSettings } from "@/context/useAppSetting";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

const LOCAL_STORAGE_KEY = "custom_providers_analytics";

export const DataAnalytics: React.FC = () => {
  const { settings, setSettings } = useAppSettings();

  const currentSetting = useMemo(() => settings.data.analytics, [settings]);

  const [enabled, setEnabled] = useState(currentSetting.enabled);
  const [provider, setProvider] = useState({
    isCustom: currentSetting.isCustom,
    provider: currentSetting.provider,
  });
  const [savedCustomProviders, setSavedCustomProviders] = useState<
    ExternalProvider[]
  >([]);

  useEffect(() => {
    setSettings({
      ...settings,
      data: {
        ...settings.data,
        analytics: {
          enabled,
          provider: provider.provider,
          isCustom: provider.isCustom,
        },
      },
    });
  }, [enabled, provider]);

  function updateLocalStorage(savedCustomProviders: ExternalProvider[]) {
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify(savedCustomProviders)
    );
  }

  useEffect(() => {
    const data = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (data) {
      const savedProviders = JSON.parse(data) as ExternalProvider[];
      setSavedCustomProviders(savedProviders);
    }
  }, []);

  const handleSaveCustomProvider = (provider: ExternalProvider) => {
    const newData = [
      ...savedCustomProviders.filter((e) => e.id !== provider.id),
      provider,
    ];
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
  };

  const handleRemoveCustomProvider = (id: string) => {
    const newData = savedCustomProviders.filter(
      (provider) => provider.id !== id
    );
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
    setProvider({ isCustom: true, provider: undefined });
  };

  return (
    <Card className="w-full h-full p-4 space-y-6">
      <h2 className="text-xl font-semibold">Analytics Settings</h2>

      <div className="flex items-center justify-between">
        <Label htmlFor="enable-analytics">Enable Analytics</Label>
        <Switch
          id="enable-analytics"
          checked={enabled}
          onCheckedChange={setEnabled}
        />
      </div>

      <Separator />

      <ExternalProviderSettings
        currentProvider={
          currentSetting.provider && {
            isCustom: currentSetting.isCustom,
            provider: currentSetting.provider,
          }
        }
        presetProviders={[
          {
            id: "GoogleAnalytics",
            name: "Google Analytics",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
          {
            id: "Mixpanel",
            name: "Mixpanel",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
        ]}
        savedCustomProviders={savedCustomProviders}
        onSave={handleSaveCustomProvider}
        onRemove={(id) => handleRemoveCustomProvider(id)}
        onProviderChange={(isCustom, provider) => {
          setProvider({ isCustom, provider });
        }}
      />
    </Card>
  );
};
