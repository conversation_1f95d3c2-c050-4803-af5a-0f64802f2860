import React, { useState, useEffect, useMemo } from "react";
import { ExternalProviderSettings } from "../ExternalProviderSetting";
import { ExternalProvider } from "@/model/externalProvider";
import { useAppSettings } from "@/context/useAppSetting";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

const LOCAL_STORAGE_KEY = "custom_providers_storage";

export const DataStorage: React.FC = () => {
  const { settings, setSettings } = useAppSettings();

  const currentSetting = useMemo(() => settings.data.storage, [settings]);

  const [enabled, setEnabled] = useState(currentSetting.enabled);
  const [provider, setProvider] = useState({
    isCustom: currentSetting.isCustom,
    provider: currentSetting.provider,
  });
  const [savedCustomProviders, setSavedCustomProviders] = useState<
    ExternalProvider[]
  >([]);

  useEffect(() => {
    setSettings({
      ...settings,
      data: {
        ...settings.data,
        storage: {
          enabled,
          provider: provider.provider,
          isCustom: provider.isCustom,
        },
      },
    });
  }, [enabled, provider]);

  function updateLocalStorage(savedCustomProviders: ExternalProvider[]) {
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify(savedCustomProviders)
    );
  }

  useEffect(() => {
    const data = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (data) {
      const savedProviders = JSON.parse(data) as ExternalProvider[];
      setSavedCustomProviders(savedProviders);
    }
  }, []);

  const handleSaveCustomProvider = (provider: ExternalProvider) => {
    const newData = [
      ...savedCustomProviders.filter((e) => e.id !== provider.id),
      provider,
    ];
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
  };

  const handleRemoveCustomProvider = (id: string) => {
    const newData = savedCustomProviders.filter(
      (provider) => provider.id !== id
    );
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
    setProvider({ isCustom: true, provider: undefined });
  };

  return (
    <Card className="w-full h-full p-4 space-y-6">
      <h2 className="text-xl font-semibold">Storage Settings</h2>

      <div className="flex items-center justify-between">
        <Label htmlFor="enable-storage">Enable Storage</Label>
        <Switch
          id="enable-storage"
          checked={enabled}
          onCheckedChange={setEnabled}
        />
      </div>

      <Separator />

      <ExternalProviderSettings
        currentProvider={
          currentSetting.provider && {
            isCustom: currentSetting.isCustom,
            provider: currentSetting.provider,
          }
        }
        presetProviders={[
          {
            id: "AWS",
            name: "Amazon Web Services (AWS)",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
          {
            id: "Supabase",
            name: "Supabase",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
          {
            id: "Azure",
            name: "Microsoft Azure",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
          {
            id: "Firebase",
            name: "Google Firebase",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
        ]}
        savedCustomProviders={savedCustomProviders}
        onSave={handleSaveCustomProvider}
        onRemove={handleRemoveCustomProvider}
        onProviderChange={(isCustom, provider) =>
          setProvider({ isCustom, provider })
        }
      />
    </Card>
  );
};
