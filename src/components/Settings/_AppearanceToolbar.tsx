"use client";

import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface Feature {
  id: string;
  name: string;
  description: string;
  icon: string;
  title: string;
}

interface AppearanceToolbarProps {
  features: Feature[];
}

export const AppearanceToolbar: React.FC<AppearanceToolbarProps> = ({
  features,
}) => {
  const [visibleFeatures, setVisibleFeatures] = useState<string[]>([]);
  const [orderedFeatures, setOrderedFeatures] = useState<Feature[]>([]);
  const [selectedFeatureId, setSelectedFeatureId] = useState<string | null>(
    null
  );

  useEffect(() => {
    setOrderedFeatures(features);
  }, [features]);

  const handleToggleFeature = (featureId: string) => {
    setVisibleFeatures((prev) =>
      prev.includes(featureId)
        ? prev.filter((id) => id !== featureId)
        : [...prev, featureId]
    );
  };

  const handleSelectFeature = (featureId: string) => {
    setSelectedFeatureId((prev) => (prev === featureId ? null : featureId));
  };

  const moveFeature = (direction: "up" | "down") => {
    if (selectedFeatureId === null) return;

    const index = orderedFeatures.findIndex(
      (feature) => feature.id === selectedFeatureId
    );

    if (index === -1) return;

    const newOrder = [...orderedFeatures];
    const [movedFeature] = newOrder.splice(index, 1);

    if (direction === "up" && index > 0) {
      newOrder.splice(index - 1, 0, movedFeature);
    }

    if (direction === "down" && index < newOrder.length - 1) {
      newOrder.splice(index + 1, 0, movedFeature);
    }

    setOrderedFeatures(newOrder);
  };

  return (
    <div className="space-y-6 w-full h-full p-4">
      <h2 className="text-lg font-semibold">Toolbar Configuration</h2>

      <div className="flex justify-between items-center">
        <Label className="text-md">Available Features:</Label>
        <div className="flex space-x-2">
          <Button
            onClick={() => moveFeature("up")}
            disabled={
              selectedFeatureId === null ||
              orderedFeatures.findIndex((f) => f.id === selectedFeatureId) === 0
            }
            variant="secondary"
          >
            Move Up
          </Button>
          <Button
            onClick={() => moveFeature("down")}
            disabled={
              selectedFeatureId === null ||
              orderedFeatures.findIndex((f) => f.id === selectedFeatureId) ===
                orderedFeatures.length - 1
            }
            variant="secondary"
          >
            Move Down
          </Button>
        </div>
      </div>

      <div className="space-y-3">
        {orderedFeatures.map((feature) => (
          <Card
            key={feature.id}
            className={`cursor-pointer transition-shadow ${
              selectedFeatureId === feature.id
                ? "border-[var(--border-custom-red-500)] shadow-md"
                : ""
            }`}
            onClick={() => handleSelectFeature(feature.id)}
          >
            <CardContent className="flex items-center justify-between py-4 px-2 sm:px-4">
              <div className="flex items-center space-x-4">
                <img
                  src={feature.icon}
                  alt={feature.title}
                  className="w-6 h-6"
                />
                <div>
                  <div className="font-semibold">{feature.title}</div>
                  <div className="text-sm text-muted-foreground">
                    {feature.description}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor={`feature-${feature.id}`}>Visible</Label>
                <Switch
                  id={`feature-${feature.id}`}
                  checked={visibleFeatures.includes(feature.id)}
                  onCheckedChange={() => handleToggleFeature(feature.id)}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AppearanceToolbar;
