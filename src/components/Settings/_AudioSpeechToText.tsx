"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useAppSettings } from "@/context/useAppSetting";
import { ExternalProviderSettings } from "../ExternalProviderSetting";
import { ExternalProvider } from "@/model/externalProvider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Mic } from "lucide-react";

const LOCAL_STORAGE_KEY = "custom_providers_speech_to_text";

export const AudioSpeechToText: React.FC = () => {
  const { settings, setSettings } = useAppSettings();
  const currentSetting = useMemo(() => settings.audio.speechToText, [settings]);

  const [enabled, setEnabled] = useState(currentSetting.enabled);
  const [language, setLanguage] = useState(currentSetting.language || "auto");
  const [languages, setLanguages] = useState<{ key: string; value: string }[]>(
    []
  );
  const [provider, setProvider] = useState({
    isCustom: currentSetting.isCustom,
    provider: currentSetting.provider,
  });
  const [autoRecord, setAutoRecord] = useState(currentSetting.autoRecord);
  const [continueRecordAfterAI, setContinueRecordAfterAI] = useState(
    currentSetting.continueRecordAfterAI
  );
  const [autoSend, setAutoSend] = useState(currentSetting.autoSend);
  const [testText, setTestText] = useState("");

  const [savedCustomProviders, setSavedCustomProviders] = useState<
    ExternalProvider[]
  >([]);

  useEffect(() => {
    setSettings({
      ...settings,
      audio: {
        ...settings.audio,
        speechToText: {
          enabled,
          provider: provider.provider,
          isCustom: provider.isCustom,
          autoRecord,
          continueRecordAfterAI,
          autoSend,
          language,
        },
      },
    });
  }, [
    enabled,
    autoRecord,
    continueRecordAfterAI,
    autoSend,
    language,
    provider,
  ]);

  useEffect(() => {
    const data = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (data) {
      const savedCustomProviders = JSON.parse(data) as ExternalProvider[];
      setSavedCustomProviders(savedCustomProviders);
    }
  }, []);

  const updateLocalStorage = (newData: ExternalProvider[]) => {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newData));
  };

  const handleSaveCustomProvider = (provider: ExternalProvider) => {
    const newData = [
      ...savedCustomProviders.filter((e) => e.id !== provider.id),
      provider,
    ];
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
  };

  const handleRemoveCustomProvider = (id: string) => {
    const newData = savedCustomProviders.filter((p) => p.id !== id);
    setSavedCustomProviders(newData);
    updateLocalStorage(newData);
    setProvider({ isCustom: true, provider: undefined });
  };

  const handleTestSpeechToText = () => {
    console.log("Testing speech-to-text with:", testText);
  };

  return (
    <div className="space-y-6 w-full h-full p-4">
      <h2 className="text-xl font-semibold flex items-center gap-2">
        <Mic className="w-5 h-5" /> Speech-to-Text Settings
      </h2>

      <div className="flex items-center justify-between">
        <Label>Enable Speech-to-Text</Label>
        <Switch checked={enabled} onCheckedChange={setEnabled} />
      </div>

      <ExternalProviderSettings
        currentProvider={
          currentSetting.provider && {
            isCustom: currentSetting.isCustom,
            provider: currentSetting.provider,
          }
        }
        presetProviders={[
          {
            id: "GoogleSpeechAPI",
            name: "Google Speech API",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
          {
            id: "IBMWatson",
            name: "IBM Watson",
            endpoint: "",
            apiKey: "",
            payloadScript: "",
          },
        ]}
        savedCustomProviders={savedCustomProviders}
        onSave={handleSaveCustomProvider}
        onRemove={handleRemoveCustomProvider}
        onProviderChange={(isCustom, provider) =>
          setProvider({ isCustom, provider })
        }
      />

      <div className="space-y-2">
        <Label>Language</Label>
        <Select value={language} onValueChange={setLanguage}>
          <SelectTrigger>
            <SelectValue placeholder="Select Language" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="auto">Auto</SelectItem>
            {languages.map((lang) => (
              <SelectItem key={lang.key} value={lang.key}>
                {lang.value}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center justify-between">
        <Label>Auto Record When Open</Label>
        <Switch checked={autoRecord} onCheckedChange={setAutoRecord} />
      </div>

      <div className="flex items-center justify-between">
        <Label>Continue Recording After AI Finishes</Label>
        <Switch
          checked={continueRecordAfterAI}
          onCheckedChange={setContinueRecordAfterAI}
        />
      </div>

      <div className="flex items-center justify-between">
        <Label>Auto Send After Recording</Label>
        <Switch checked={autoSend} onCheckedChange={setAutoSend} />
      </div>

      <div className="space-y-2">
        <Label>Test Speech-to-Text</Label>
        <div className="flex gap-4">
          <Input
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            placeholder="Try saying something..."
          />
          <Button onClick={handleTestSpeechToText}>Test</Button>
        </div>
      </div>
    </div>
  );
};
