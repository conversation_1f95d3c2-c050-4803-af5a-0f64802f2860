import { BorderRadius } from "@/model/appSetting";

export interface RoundnessStyleProps {
  value: BorderRadius;
  onChange: (newValue: BorderRadius) => void;
}

const toBorderRadiusStyle = (radius: BorderRadius): string => {
  const { topLeft, topRight, bottomLeft, bottomRight } = radius;
  return `${topLeft} ${topRight} ${bottomLeft} ${bottomRight}`;
};

export const RoundnessStyle = ({ value, onChange }: RoundnessStyleProps) => (
  <div className="space-y-4">
    <label className="block text-sm font-semibold">Border Radius</label>
    <div className="grid grid-cols-2 gap-4">
      <div>
        <label className="block text-xs">Top Left (px)</label>
        <input
          type="number"
          value={parseInt(value.topLeft)} // Ensure value is parsed to a number
          onChange={
            (e) => onChange({ ...value, topLeft: `${e.target.value}px` }) // Store value as string with 'px'
          }
          className="w-full p-2 border rounded"
          placeholder="Top Left"
        />
      </div>
      <div>
        <label className="block text-xs">Top Right (px)</label>
        <input
          type="number"
          value={parseInt(value.topRight)} // Ensure value is parsed to a number
          onChange={
            (e) => onChange({ ...value, topRight: `${e.target.value}px` }) // Store value as string with 'px'
          }
          className="w-full p-2 border rounded"
          placeholder="Top Right"
        />
      </div>
      <div>
        <label className="block text-xs">Bottom Left (px)</label>
        <input
          type="number"
          value={parseInt(value.bottomLeft)} // Ensure value is parsed to a number
          onChange={
            (e) => onChange({ ...value, bottomLeft: `${e.target.value}px` }) // Store value as string with 'px'
          }
          className="w-full p-2 border rounded"
          placeholder="Bottom Left"
        />
      </div>
      <div>
        <label className="block text-xs">Bottom Right (px)</label>
        <input
          type="number"
          value={parseInt(value.bottomRight)} // Ensure value is parsed to a number
          onChange={
            (e) => onChange({ ...value, bottomRight: `${e.target.value}px` }) // Store value as string with 'px'
          }
          className="w-full p-2 border rounded"
          placeholder="Bottom Right"
        />
      </div>
    </div>
  </div>
);
