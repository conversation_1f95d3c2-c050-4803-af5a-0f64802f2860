import { BorderRadius } from "@/model/appSetting";
import { twMerge } from "tailwind-merge";
import { HoverStyle } from "./HoverStyle";
import { RoundnessStyle } from "./RoundnessStyle";
import { ShadowStyle } from "./ShadowStyle";

export interface CardStyleSettingsProps {
  cardStyle: {
    borderRadius: BorderRadius;
    hoverStyle: {
      backgroundColor: string;
      textColor: string;
    };
    shadowStyle: {
      boxShadow: string;
    };
  };
  setCardStyle: (newCardStyle: any) => void;
}

export const CardStyleSettings = ({
  cardStyle,
  setCardStyle,
}: CardStyleSettingsProps) => {
  const cssVars = {
    "--card-rounded-top-left": cardStyle.borderRadius.topLeft,
    "--card-rounded-top-right": cardStyle.borderRadius.topRight,
    "--card-rounded-bottom-left": cardStyle.borderRadius.bottomLeft,
    "--card-rounded-bottom-right": cardStyle.borderRadius.bottomRight,
    "--card-bg-color": cardStyle.hoverStyle.backgroundColor,
    "--card-text-color": cardStyle.hoverStyle.textColor,
    "--card-box-shadow": cardStyle.shadowStyle.boxShadow,
  } as React.CSSProperties;

  return (
    <section style={cssVars}>
      <h2 className="text-lg font-semibold">Card Style</h2>
      <div className="flex space-x-4">
        <div className="flex flex-col space-y-2 w-1/2">
          <RoundnessStyle
            value={cardStyle.borderRadius}
            onChange={(value) =>
              setCardStyle({ ...cardStyle, borderRadius: value })
            }
          />

          <HoverStyle
            hoverStyle={cardStyle.hoverStyle}
            setHoverStyle={(newHoverStyle) =>
              setCardStyle({ ...cardStyle, hoverStyle: newHoverStyle })
            }
          />

          <ShadowStyle
            shadowStyle={cardStyle.shadowStyle}
            setShadowStyle={(newShadowStyle) =>
              setCardStyle({ ...cardStyle, shadowStyle: newShadowStyle })
            }
          />
        </div>

        <div className="flex flex-col space-y-2 w-1/2">
          <h3 className="font-semibold">Card Preview</h3>
          <div
            className={twMerge(
              "p-5 text-base text-center",
              "rounded-tl-[var(--card-rounded-top-left)]",
              "rounded-tr-[var(--card-rounded-top-right)]",
              "rounded-bl-[var(--card-rounded-bottom-left)]",
              "rounded-br-[var(--card-rounded-bottom-right)]",
              "bg-[var(--card-bg-color)]",
              "text-[var(--card-text-color)]",
              "shadow-[var(--card-box-shadow)]"
            )}
          >
            Card
          </div>
        </div>
      </div>
    </section>
  );
};
