import {
  AppearanceColorAdvanced,
  AppearanceColorSetting,
} from "@/model/appSetting";
import React from "react";
import { ColorInput } from "./ColorInput";
import { ReferenceOrRawInput } from "./ReferenceOrRawInput";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

interface SecondaryAppearanceProps {
  lightColors: AppearanceColorAdvanced;
  darkColors: AppearanceColorAdvanced;
  handleColorChange: (
    tab: "light" | "dark",
    key: string,
    value: string
  ) => void;
}

const colorLabels: Record<string, string> = {
  background: "Background",
  foreground: "Foreground",
  card: "Card",
  cardForeground: "Card Foreground",
  popover: "Popover",
  popoverForeground: "Popover Foreground",
  primary: "Primary",
  primaryForeground: "Primary Foreground",
  secondary: "Secondary",
  secondaryForeground: "Secondary Foreground",
  muted: "Muted",
  mutedForeground: "Muted Foreground",
  accent: "Accent",
  accentForeground: "Accent Foreground",
  destructive: "Destructive",
  destructiveForeground: "Destructive Foreground",
  border: "Border",
  input: "Input",
  ring: "Ring",
  chart1: "Chart 1",
  chart2: "Chart 2",
  chart3: "Chart 3",
  chart4: "Chart 4",
  chart5: "Chart 5",
};

export const SecondaryAppearance = ({
  lightColors,
  darkColors,
  handleColorChange,
}: SecondaryAppearanceProps) => {
  const renderPalette = (
    colors: AppearanceColorAdvanced,
    tab: "light" | "dark"
  ) => (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
      {Object.entries(colors).map(([key, value]) => (
        <ReferenceOrRawInput
          key={key}
          label={key}
          value={value}
          listReferences={[
            {
              group: "base",
              selections: [{ label: "Primary", value: "--color-primary" }],
            },
            {
              group: "secondary",
              selections: [{ label: "Background", value: "--background" }],
            },
          ]}
          onForReferenceChange={(color, value) =>
            handleColorChange(tab, color, value)
          }
        >
          <ColorInput
            color={key}
            value={value}
            onChange={(color, value) => handleColorChange(tab, color, value)}
            label={colorLabels[key as keyof typeof colorLabels]}
          />
        </ReferenceOrRawInput>
      ))}
    </div>
  );

  return (
    <section>
      <h2 className="text-lg font-semibold mb-4">Color Palette</h2>

      <Tabs defaultValue="light" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="light">Light</TabsTrigger>
          <TabsTrigger value="dark">Dark</TabsTrigger>
        </TabsList>

        <TabsContent value="light">
          <h3 className="font-semibold text-xl mb-2">Light Theme</h3>
          {renderPalette(lightColors, "light")}
        </TabsContent>

        <TabsContent value="dark">
          <h3 className="font-semibold text-xl mb-2">Dark Theme</h3>
          {renderPalette(darkColors, "dark")}
        </TabsContent>
      </Tabs>
    </section>
  );
};
