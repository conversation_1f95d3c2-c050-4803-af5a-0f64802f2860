import { BorderRadius } from "@/model/appSetting";
import { twMerge } from "tailwind-merge";
import { HoverStyle } from "./HoverStyle";
import { RoundnessStyle } from "./RoundnessStyle";
import { TextStyle } from "./TextStyle";
import { ColorInput } from "./ColorInput";
import { ReferenceOrRawInput } from "./ReferenceOrRawInput";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

export interface ButtonStyleSettingsProps {
  buttonStyle: {
    borderRadius: BorderRadius;
    padding: string;
    textStyle: {
      fontSize: string;
      fontFamily: string;
      fontWeight: string;
    };
    hoverStyle: {
      backgroundColor: string;
      textColor: string;
    };
  };
  setButtonStyle: (newButtonStyle: any) => void;
  unit: string;
  setUnit: (unit: "rem" | "px") => void;
}

const colorLabels = {
  backgroundColor: "Background Color",
  textColor: "Text Color",
};

export const ButtonStyleSettings = ({
  buttonStyle,
  setButtonStyle,
  unit,
}: ButtonStyleSettingsProps) => {
  const cssVars = {
    "--button-rounded-top-left": buttonStyle.borderRadius.topLeft + unit,
    "--button-rounded-top-right": buttonStyle.borderRadius.topRight + unit,
    "--button-rounded-bottom-left": buttonStyle.borderRadius.bottomLeft + unit,
    "--button-rounded-bottom-right":
      buttonStyle.borderRadius.bottomRight + unit,
    "--button-padding": buttonStyle.padding + unit,
    "--button-font-size": buttonStyle.textStyle.fontSize + unit,
    "--button-font-family": buttonStyle.textStyle.fontFamily,
    "--button-font-weight": buttonStyle.textStyle.fontWeight,
    "--button-bg-color": buttonStyle.hoverStyle.backgroundColor,
    "--button-text-color": buttonStyle.hoverStyle.textColor,
  } as React.CSSProperties;

  const handleColorChange = (
    id: string,
    key: keyof typeof buttonStyle.hoverStyle,
    value: string
  ) => {
    setButtonStyle({
      ...buttonStyle,
      hoverStyle: {
        ...buttonStyle.hoverStyle,
        [key]: value,
      },
    });
  };

  return (
    <section className="space-y-4" style={cssVars}>
      <h2 className="text-lg font-semibold">Button Style</h2>

      <div className="flex">
        <div className="w-1/2">
          <Tabs defaultValue="roundness">
            <TabsList>
              <TabsTrigger value="roundness">Roundness</TabsTrigger>
              <TabsTrigger value="hover">Hover</TabsTrigger>
              <TabsTrigger value="color">Color</TabsTrigger>
              <TabsTrigger value="text">Text</TabsTrigger>
            </TabsList>

            <TabsContent value="roundness">
              <RoundnessStyle
                value={buttonStyle.borderRadius}
                onChange={(value) =>
                  setButtonStyle({ ...buttonStyle, borderRadius: value })
                }
              />
            </TabsContent>

            <TabsContent value="hover">
              <HoverStyle
                hoverStyle={buttonStyle.hoverStyle}
                setHoverStyle={(newHoverStyle) =>
                  setButtonStyle({ ...buttonStyle, hoverStyle: newHoverStyle })
                }
              />
            </TabsContent>

            <TabsContent value="color">
              <div className="flex flex-col items-center space-y-2">
                {(["backgroundColor", "textColor"] as const).map((key) => {
                  const value = buttonStyle.hoverStyle[key];
                  return (
                    <ReferenceOrRawInput
                      key={key}
                      label={key}
                      value={value}
                      listReferences={[
                        {
                          group: "base",
                          selections: [
                            { label: "Primary", value: "--color-primary" },
                          ],
                        },
                        {
                          group: "secondary",
                          selections: [
                            { label: "Background", value: "--background" },
                          ],
                        },
                      ]}
                      onForReferenceChange={(color, value) =>
                        handleColorChange("hoverStyle", key, value)
                      }
                    >
                      <ColorInput
                        key={key}
                        color={key}
                        value={value}
                        onChange={(color, value) =>
                          handleColorChange("hoverStyle", key, value)
                        }
                        label={colorLabels[key]}
                      />
                    </ReferenceOrRawInput>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="text">
              <TextStyle
                textStyle={buttonStyle.textStyle}
                setTextStyle={(newStyle) =>
                  setButtonStyle({ ...buttonStyle, textStyle: newStyle })
                }
              />
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/2">
          <div className="flex flex-col space-y-2 w-full">
            <h3 className="font-semibold">Button Preview</h3>
            <div
              className={twMerge(
                "flex items-center justify-center border",
                "rounded-tl-[var(--button-rounded-top-left)]",
                "rounded-tr-[var(--button-rounded-top-right)]",
                "rounded-bl-[var(--button-rounded-bottom-left)]",
                "rounded-br-[var(--button-rounded-bottom-right)]",
                "bg-[var(--button-bg-color)]",
                "text-[var(--button-text-color)]",
                "text-[length:var(--button-font-size)]",
                "font-[var(--button-font-weight)]",
                "[font-family:var(--button-font-family)]",
                "p-[var(--button-padding)]"
              )}
            >
              Button
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
