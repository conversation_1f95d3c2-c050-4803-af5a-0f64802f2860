export interface TextStyleProps {
  textStyle: {
    fontSize: string;
    fontFamily: string;
    fontWeight: string;
  };
  setTextStyle: (newStyle: {
    fontSize: string;
    fontFamily: string;
    fontWeight: string;
  }) => void;
}

export const TextStyle = ({ textStyle, setTextStyle }: TextStyleProps) => (
  <div>
    <label className="block text-sm">Font Size</label>
    <input
      type="text"
      value={textStyle.fontSize}
      onChange={(e) => setTextStyle({ ...textStyle, fontSize: e.target.value })}
      className="w-full p-2 border rounded"
      placeholder="Font size"
    />
    <label className="block text-sm">Font Family</label>
    <input
      type="text"
      value={textStyle.fontFamily}
      onChange={(e) =>
        setTextStyle({ ...textStyle, fontFamily: e.target.value })
      }
      className="w-full p-2 border rounded"
      placeholder="Font family"
    />
    <label className="block text-sm">Font Weight</label>
    <input
      type="text"
      value={textStyle.fontWeight}
      onChange={(e) =>
        setTextStyle({ ...textStyle, fontWeight: e.target.value })
      }
      className="w-full p-2 border rounded"
      placeholder="Font weight"
    />
  </div>
);
