import { Tabs, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { AppearanceColorSetting } from "@/model/appSetting";
import { ColorInput } from "./ColorInput";

export const colorLabels = {
  primary: "Primary",
  secondary: "Secondary",
  tertiary: "Tertiary",
  quaternary: "Quaternary",
};

export interface BaseAppearanceProps {
  lightColors: AppearanceColorSetting;
  darkColors: AppearanceColorSetting;
  handleColorChange: (
    tab: "light" | "dark",
    key: string,
    value: string
  ) => void;
}

export const BaseAppearance = ({
  lightColors,
  darkColors,
  handleColorChange,
}: BaseAppearanceProps) => {
  return (
    <section>
      <h2 className="text-lg font-semibold mb-4">Color Palette</h2>

      <Tabs defaultValue="light" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="light">Light</TabsTrigger>
          <TabsTrigger value="dark">Dark</TabsTrigger>
        </TabsList>

        <TabsContent value="light">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            {Object.entries(lightColors).map(([key, value]) => (
              <ColorInput
                key={key}
                color={key}
                value={value}
                onChange={(color, value) =>
                  handleColorChange("light", color, value)
                }
                label={colorLabels[key as keyof typeof colorLabels]}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="dark">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            {Object.entries(darkColors).map(([key, value]) => (
              <ColorInput
                key={key}
                color={key}
                value={value}
                onChange={(color, value) =>
                  handleColorChange("dark", color, value)
                }
                label={colorLabels[key as keyof typeof colorLabels]}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </section>
  );
};
