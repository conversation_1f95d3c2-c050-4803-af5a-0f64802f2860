export interface ShadowStyleProps {
  shadowStyle: {
    boxShadow: string;
  };
  setShadowStyle: (newShadowStyle: { boxShadow: string }) => void;
}

export const ShadowStyle = ({
  shadowStyle,
  setShadowStyle,
}: ShadowStyleProps) => (
  <div>
    <label className="block text-sm">Shadow</label>
    <input
      type="text"
      value={shadowStyle.boxShadow}
      onChange={(e) =>
        setShadowStyle({ ...shadowStyle, boxShadow: e.target.value })
      }
      className="w-full p-2 border rounded"
      placeholder="Shadow (e.g. 0px 4px 6px rgba(0, 0, 0, 0.1))"
    />
  </div>
);
