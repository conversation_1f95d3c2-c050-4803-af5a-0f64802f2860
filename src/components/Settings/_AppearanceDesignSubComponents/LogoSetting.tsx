export interface LogoSettingsProps {
  logo: {
    url: string;
    height?: string;
    alt?: string;
  };
  onChange: (key: string, value: any) => void;
}

export const LogoSettings: React.FC<LogoSettingsProps> = ({
  logo,
  onChange,
}) => {
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange("url", e.target.value);
  };

  const handleHeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange("height", e.target.value);
  };

  const handleAltChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange("alt", e.target.value);
  };

  return (
    <section>
      <h2 className="text-lg font-semibold">Logo Style</h2>
      <div className="flex space-x-4">
        <div className="flex flex-col space-y-2 w-1/2">
          <div>
            <label className="block text-sm">Logo URL</label>
            <input
              type="text"
              value={logo.url}
              onChange={handleUrlChange}
              className="w-full p-2 border rounded"
              placeholder="Logo URL"
            />
          </div>

          <div>
            <label className="block text-sm">Logo Height (optional)</label>
            <input
              type="text"
              value={logo.height || ""}
              onChange={handleHeightChange}
              className="w-full p-2 border rounded"
              placeholder="Logo Height"
            />
          </div>

          <div>
            <label className="block text-sm">Logo Alt Text (optional)</label>
            <input
              type="text"
              value={logo.alt || ""}
              onChange={handleAltChange}
              className="w-full p-2 border rounded"
              placeholder="Logo Alt Text"
            />
          </div>
        </div>
      </div>
    </section>
  );
};
