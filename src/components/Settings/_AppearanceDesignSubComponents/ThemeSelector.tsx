import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AppSettingTheme } from "@/model/appSetting";

export const ThemeSelector = ({
  theme,
  setTheme,
}: {
  theme: AppSettingTheme;
  setTheme: (v: AppSettingTheme) => void;
}) => (
  <section>
    <h2 className="text-lg font-semibold mb-2">Theme</h2>
    <Select value={theme} onValueChange={setTheme}>
      <SelectTrigger id="theme-select" className="w-[180px]">
        <SelectValue placeholder="Select Theme" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="system">System</SelectItem>
        <SelectItem value="light">Light</SelectItem>
        <SelectItem value="dark">Dark</SelectItem>
      </SelectContent>
    </Select>
  </section>
);
