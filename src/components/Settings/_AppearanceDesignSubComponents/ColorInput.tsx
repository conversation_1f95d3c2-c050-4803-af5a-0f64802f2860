export interface ColorInputProps {
  color: string;
  onChange: (color: string, value: string) => void;
  label: string;
  value: string;
}

export const ColorInput = ({
  color,
  onChange,
  label,
  value,
}: ColorInputProps) => (
  <div className="flex flex-col items-center space-y-2">
    <input
      type="color"
      value={value}
      onChange={(e) => onChange(color, e.target.value)}
      className="w-16 h-16 rounded shadow-md border"
    />
    <span className="text-sm text-[var(--text-custom-gray-600)]">{label}</span>
    <span className="text-xs text-[var(--text-custom-gray-500)]">{value}</span>
  </div>
);
