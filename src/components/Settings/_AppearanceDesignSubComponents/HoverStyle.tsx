export interface HoverStyleProps {
  hoverStyle: {
    backgroundColor: string;
    textColor: string;
  };
  setHoverStyle: (newHoverStyle: {
    backgroundColor: string;
    textColor: string;
  }) => void;
}

export const HoverStyle = ({ hoverStyle, setHoverStyle }: HoverStyleProps) => (
  <div>
    <label className="block text-sm">Hover Background Color</label>
    <input
      type="color"
      value={hoverStyle.backgroundColor}
      onChange={(e) =>
        setHoverStyle({ ...hoverStyle, backgroundColor: e.target.value })
      }
      className="w-full p-2 border rounded"
    />
    <label className="block text-sm">Hover Text Color</label>
    <input
      type="color"
      value={hoverStyle.textColor}
      onChange={(e) =>
        setHoverStyle({ ...hoverStyle, textColor: e.target.value })
      }
      className="w-full p-2 border rounded"
    />
  </div>
);
