export interface FontWeight {
  normal: number;
  bold: number;
}

interface Typography {
  fontFamily: string;
  baseFontSize: string; // e.g., "16px"
  fontWeight: FontWeight;
}

interface TypographySettingsProps {
  typography: Typography;
  setTypography: (newTypography: Typography) => void;
}

export const TypographySettings = ({
  typography,
  setTypography,
}: TypographySettingsProps) => (
  <section>
    <h2 className="text-lg font-semibold">Logo Style</h2>
    <div className="flex space-x-4">
      <div className="flex flex-col space-y-2 w-1/2">
        <div className="space-y-2">
          <input
            type="text"
            value={typography.fontFamily}
            onChange={(e) =>
              setTypography({ ...typography, fontFamily: e.target.value })
            }
            className="w-full p-2 border rounded"
            placeholder="Font family"
          />
          <input
            type="text"
            value={typography.baseFontSize}
            onChange={(e) =>
              setTypography({ ...typography, baseFontSize: e.target.value })
            }
            className="w-full p-2 border rounded"
            placeholder="Base font size (e.g. 16px)"
          />
          <input
            type="number"
            value={typography.fontWeight.normal}
            onChange={(e) =>
              setTypography({
                ...typography,
                fontWeight: {
                  ...typography.fontWeight,
                  normal: +e.target.value,
                },
              })
            }
            className="w-full p-2 border rounded"
            placeholder="Normal font weight"
          />
        </div>
      </div>
    </div>
  </section>
);
