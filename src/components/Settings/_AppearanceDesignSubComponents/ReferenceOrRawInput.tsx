import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Info, RotateCcw } from "lucide-react";
import { IconQuestionMark } from "@tabler/icons-react";
import Link from "next/link";

interface ReferenceOption {
  label: string;
  value: string;
}

interface ReferenceGroup {
  group: string;
  selections: ReferenceOption[];
}

interface ReferenceOrRawInputProps {
  value: string;
  label: string;
  listReferences: ReferenceGroup[];
  onForReferenceChange: (label: string, value: string) => void;
  children: React.ReactElement;
  docLink?: string; // optional documentation link
}

export const ReferenceOrRawInput = ({
  value,
  label,
  listReferences,
  onForReferenceChange,
  children,
  docLink,
}: ReferenceOrRawInputProps) => {
  const isReference = value.startsWith("var(");
  const [mode, setMode] = useState<"reference" | "raw">(
    isReference ? "reference" : "raw"
  );

  const handleToggleMode = () => {
    setMode((prev) => {
      const next = prev === "reference" ? "raw" : "reference";
      onForReferenceChange(label, ""); // Clear value when switching
      return next;
    });
  };

  const handleReferenceChange = (newValue: string) => {
    onForReferenceChange(label, newValue ? `var(${newValue})` : "");
  };

  const handleReset = () => {
    onForReferenceChange(label, "");
  };

  const handleInfoClick = () => {
    if (docLink) {
      window.open(docLink, "_blank");
    }
  };

  return (
    <div className="flex flex-col border rounded-md p-4 space-y-2 w-full h-full">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium">{label}</span>
        <Link
          className="w-6 h-6 text-muted-foreground"
          title={`See documentation for ${label}`}
          href={"#"}
        >
          <IconQuestionMark className="w-4 h-4" />
        </Link>
      </div>

      <div className="w-full h-full">
        {mode === "reference" ? (
          <Select
            value={value.replace(/^var\((.*)\)$/, "$1")}
            onValueChange={handleReferenceChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a reference" />
            </SelectTrigger>
            <SelectContent>
              {listReferences.map((group) => (
                <SelectGroup key={group.group}>
                  <SelectLabel>{group.group}</SelectLabel>
                  {group.selections.map((ref) => (
                    <SelectItem key={ref.value} value={ref.value}>
                      {ref.label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              ))}
            </SelectContent>
          </Select>
        ) : (
          children
        )}
      </div>

      <span className="text-xs text-muted-foreground break-all">
        Current: {value || "(none)"}
      </span>

      <div className="flex items-center justify-between">
        <Button
          variant="link"
          className="text-xs h-auto p-0"
          onClick={handleToggleMode}
        >
          Use {mode === "reference" ? "raw value" : "reference"}
        </Button>
        {docLink && (
          <Button
            variant="ghost"
            size="icon"
            className="w-6 h-6 text-muted-foreground"
            onClick={handleInfoClick}
            title="View documentation"
          >
            <Info className="w-4 h-4" />
          </Button>
        )}
        <Button
          variant="ghost"
          size="icon"
          className="w-6 h-6 text-muted-foreground"
          onClick={handleReset}
          title="Reset to default"
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};
