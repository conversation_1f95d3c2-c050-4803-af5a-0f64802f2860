import React, { useState, useEffect } from "react";
import { useAppSettings } from "@/context/useAppSetting";
import {
  AppearanceColorSetting,
  AppearanceSetting,
  AppSettingTheme,
  BorderRadius,
} from "@/model/appSetting";
import { saveAs } from "file-saver";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { twMerge } from "tailwind-merge";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { isEqual } from "lodash";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LogoSettings } from "./_AppearanceDesignSubComponents/LogoSetting";
import { ButtonStyleSettings } from "./_AppearanceDesignSubComponents/ButtonStyle";
import { CardStyleSettings } from "./_AppearanceDesignSubComponents/CardStyle";
import { ThemeSelector } from "./_AppearanceDesignSubComponents/ThemeSelector";
import { TypographySettings } from "./_AppearanceDesignSubComponents/Typography";
import {
  applyCssVariablesClient,
  applyShadcnVariablesClient,
  resetStyleLocal,
} from "../ThemeStyle";
import Cookies from "js-cookie";
import { BaseAppearance } from "./_AppearanceDesignSubComponents/BaseAppearance";
import { SecondaryAppearance } from "./_AppearanceDesignSubComponents/SecondaryAppearance";
import defaultAppearanceSetting from "@/consts/defaultAppearanceSetting.json";

export const AppearanceDesign: React.FC = () => {
  const { settings, setSettings } = useAppSettings();

  const [lightColors, setLightColors] = useState(
    settings.appearance.colorPalette.light
  );
  const [darkColors, setDarkColors] = useState(
    settings.appearance.colorPalette.dark
  );
  const [lightColorsAdvanced, setLightColorsAdvanced] = useState(
    settings.appearance.advanced.light
  );
  const [darkColorsAdvanced, setDarkColorsAdvanced] = useState(
    settings.appearance.advanced.dark
  );
  const [theme, setTheme] = useState<AppSettingTheme>(
    settings.appearance.theme || "system"
  );

  const [typography, setTypography] = useState(settings.appearance.typography);
  const [buttonStyle, setButtonStyle] = useState(settings.appearance.button);
  const [cardStyle, setCardStyle] = useState(settings.appearance.card);
  const [logo, setLogo] = useState(settings.appearance.logo);
  const [unit, setUnit] = useState<"px" | "rem">("px");
  const [isPreviewingApperance, setIsPreviewingAppearance] = useState(false);
  const [isAppearanceChanged, setIsAppearanceChanged] = useState(false);

  useEffect(() => {
    const old = localStorage.getItem("oldAppearance");
    setIsPreviewingAppearance(old !== null);
  }, []);

  useEffect(() => {
    const newAppearance: AppearanceSetting = {
      ...settings.appearance,
      colorPalette: {
        light: lightColors,
        dark: darkColors,
      },
      advanced: {
        light: lightColorsAdvanced,
        dark: darkColorsAdvanced,
      },
      theme,
      typography,
      button: buttonStyle,
      card: cardStyle,
      logo,
    };

    if (!isEqual(newAppearance, settings.appearance)) {
      localStorage.setItem(
        "oldAppearance",
        JSON.stringify(settings.appearance)
      );
      setIsAppearanceChanged(true);
    }
  }, [
    lightColors,
    darkColors,
    lightColorsAdvanced,
    darkColorsAdvanced,
    theme,
    typography,
    buttonStyle,
    cardStyle,
    logo,
  ]);

  async function preview() {
    const newAppearance: AppearanceSetting = {
      ...settings.appearance,
      colorPalette: {
        light: lightColors,
        dark: darkColors,
      },
      advanced: {
        light: lightColorsAdvanced,
        dark: darkColorsAdvanced,
      },
      theme,
      typography,
      button: buttonStyle,
      card: cardStyle,
      logo,
    };

    if (!isEqual(newAppearance, settings.appearance)) {
      localStorage.setItem(
        "oldAppearance",
        JSON.stringify(settings.appearance)
      );
      setIsPreviewingAppearance(true);
    }

    applyCssVariablesClient(
      theme,
      newAppearance.colorPalette.light,
      newAppearance.colorPalette.dark,
      newAppearance
    );
    applyShadcnVariablesClient(
      theme,
      newAppearance.advanced.light,
      newAppearance.advanced.dark,
      newAppearance
    );
  }

  async function apply() {
    try {
      const newAppearance: AppearanceSetting = {
        ...settings.appearance,
        colorPalette: {
          light: lightColors,
          dark: darkColors,
        },
        advanced: {
          light: lightColorsAdvanced,
          dark: darkColorsAdvanced,
        },
        theme,
        typography,
        button: buttonStyle,
        card: cardStyle,
        logo,
      };

      const res = await fetch("/api/v1/appearance/upload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          appearance: newAppearance,
          appearanceId: Cookies.get("appearance_id") || null,
          userId: null, // optional
          orgId: null, // optional
        }),
      });

      if (!res.ok) throw new Error("Failed to apply appearance");

      const { newAppearaceId } = await res.json();

      // Store the new appearance ID in cookie (for client-side fallback)
      Cookies.set("appearance_id", newAppearaceId, { expires: 365 });

      localStorage.removeItem("oldAppearance");
      setIsPreviewingAppearance(false);
    } catch (error) {
      console.error("Apply failed:", error);
      alert("Failed to apply appearance.");
    }
  }

  function revert() {
    const old = localStorage.getItem("oldAppearance");
    if (!old) return;

    try {
      const oldAppearance: AppearanceSetting = JSON.parse(old);
      setLightColors(oldAppearance.colorPalette.light);
      setDarkColors(oldAppearance.colorPalette.dark);
      setLightColorsAdvanced(oldAppearance.advanced.light);
      setDarkColorsAdvanced(oldAppearance.advanced.dark);
      setTheme(oldAppearance.theme || "system");
      setTypography(oldAppearance.typography);
      setButtonStyle(oldAppearance.button);
      setCardStyle(oldAppearance.card);
      setLogo(oldAppearance.logo);

      setIsPreviewingAppearance(false);
      localStorage.removeItem("oldAppearance");
    } catch (err) {
      console.error("Failed to parse oldAppearance", err);
    }
  }

  function reset() {
    try {
      resetStyleLocal();
      localStorage.removeItem("oldAppearance");
      setSettings((prev) => ({
        ...prev,
        appearance: defaultAppearanceSetting as unknown as AppearanceSetting,
      }));
    } catch (err) {
      console.error("Failed to parse oldAppearance", err);
    }
  }

  const handleColorChange = (
    theme: "light" | "dark",
    color: string,
    value: string
  ) => {
    if (theme === "light") {
      setLightColors((prev) => ({ ...prev, [color]: value }));
    } else {
      setDarkColors((prev) => ({ ...prev, [color]: value }));
    }
  };

  const handleAdvancedColorChange = (
    theme: "light" | "dark",
    color: string,
    value: string
  ) => {
    if (theme === "light") {
      setLightColorsAdvanced((prev) => ({ ...prev, [color]: value }));
    } else {
      setDarkColorsAdvanced((prev) => ({ ...prev, [color]: value }));
    }
  };

  const parseSize = (value: string) => {
    const number = parseFloat(value);
    return unit === "rem" ? `${number / 16}rem` : `${number}px`;
  };

  const exportJSON = () => {
    const json = JSON.stringify(settings.appearance, null, 2);
    const blob = new Blob([json], { type: "application/json" });
    saveAs(blob, "appearance-config.json");
  };

  const importJSON = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      try {
        const imported = JSON.parse(reader.result as string);
        if (imported) {
          setSettings({ ...settings, appearance: imported });
        }
      } catch (err) {
        alert("Invalid JSON file.");
      }
    };
    reader.readAsText(file);
  };

  const applyTheme = () => {
    setSettings({
      ...settings,
      appearance: {
        ...settings.appearance,
        theme,
      },
    });
    document.documentElement.className =
      theme === "system"
        ? window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light"
        : theme;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold">
          Appearance Settings{" "}
          {isPreviewingApperance && (
            <span className="text-[var(--text-custom-blue-500)]">
              ( Draft )
            </span>
          )}
        </h1>
      </div>

      <ThemeSelector theme={theme} setTheme={setTheme} />
      <div className="flex space-x-2 w-1/2">
        <Input type="file" accept="application/json" onChange={importJSON} />
        <Button onClick={exportJSON} variant="primary-outline">
          Export JSON
        </Button>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isAppearanceChanged && (
            <div className="flex space-x-2">
              <Button onClick={reset} variant="primary-outline" color="red">
                Reset
              </Button>
              <Button onClick={revert} variant="primary-outline" color="red">
                Revert
              </Button>
              {!isPreviewingApperance && (
                <Button
                  onClick={preview}
                  variant="primary-outline"
                  color="green"
                >
                  Preview
                </Button>
              )}
              <Button onClick={apply} variant="primary-outline" color="green">
                Apply
              </Button>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Label htmlFor="unit-select" className="text-sm">
            Unit:
          </Label>
          <Select
            value={unit}
            onValueChange={(value) => setUnit(value as "rem" | "px")}
          >
            <SelectTrigger id="unit-select" className="w-[100px]">
              <SelectValue placeholder="Select unit" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="px">px</SelectItem>
              <SelectItem value="rem">rem</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <Tabs defaultValue="baseAppearance" className="w-full">
        <TabsList>
          <TabsTrigger value="baseAppearance">Base Appearance</TabsTrigger>
          <TabsTrigger value="secondaryAppearance">
            Secondary Appearance
          </TabsTrigger>
          <TabsTrigger value="typography">Typography</TabsTrigger>
          <TabsTrigger value="button">Button Style</TabsTrigger>
          <TabsTrigger value="card">Card Style</TabsTrigger>
          <TabsTrigger value="logo">Logo</TabsTrigger>
        </TabsList>

        <TabsContent value="baseAppearance" className="p-4">
          <BaseAppearance
            lightColors={lightColors}
            darkColors={darkColors}
            handleColorChange={handleColorChange}
          />
        </TabsContent>

        <TabsContent value="secondaryAppearance" className="p-4">
          <SecondaryAppearance
            lightColors={lightColorsAdvanced}
            darkColors={darkColorsAdvanced}
            handleColorChange={handleAdvancedColorChange}
          />
        </TabsContent>

        <TabsContent value="typography" className="p-4">
          <TypographySettings
            typography={typography}
            setTypography={setTypography}
          />
        </TabsContent>

        <TabsContent value="button" className="p-4">
          <ButtonStyleSettings
            buttonStyle={buttonStyle}
            setButtonStyle={setButtonStyle}
            // parseSize={parseSize}
            unit={unit}
            setUnit={setUnit}
          />
        </TabsContent>

        <TabsContent value="card" className="p-4">
          <CardStyleSettings
            cardStyle={cardStyle}
            setCardStyle={setCardStyle}
          />
        </TabsContent>

        <TabsContent value="logo" className="p-4">
          <LogoSettings
            logo={logo}
            onChange={(key, value) =>
              setLogo((prev) => ({ ...prev, [key]: value }))
            }
          />
        </TabsContent>
      </Tabs>
      <Button
        onClick={applyTheme}
        className="mt-4 px-4 py-2 bg-primary text-white rounded"
      >
        Save
      </Button>
    </div>
  );
};
