import { Switch } from "@/components/ui/switch";

interface SettingToggleProps {
  label: string;
  value: boolean;
  onChange: (val: boolean) => void;
  description?: string;
}

export const SettingToggle: React.FC<SettingToggleProps> = ({
  label,
  value,
  onChange,
  description,
}) => (
  <div className="flex items-center justify-between py-2">
    <div className="space-y-1">
      <label className="text-sm font-medium leading-none">{label}</label>
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
    </div>
    <Switch checked={value} onCheckedChange={onChange} />
  </div>
);
