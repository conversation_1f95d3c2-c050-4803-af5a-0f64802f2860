// import { DisplayedMessage } from "@/model/displayedMessage";
// import { ChatMessages as ChatGPT } from "./ChatGPT/ChatMessages";
// import { ChatMessages as TomioChat } from "./TomioChat/ChatMessages";
// import { ChatMessagesProps } from "./ChatMessagesProps";

// export default function ChatMessages(props: ChatMessagesProps) {
//     switch (props.ui) {
//         case 'chatgpt':
//             return <ChatGPT {...props} />
//         case 'tomiochat':
//             return <TomioChat {...props} />
//     }
// }
