import React, { forwardRef } from 'react';
import { ChatMessage } from './ChatMessage';
import MessageLoadingAnimation from '../../MessageLoadingAnimation';
import { ChatMessagesProps } from '../ChatMessagesProps';
import { twMerge } from 'tailwind-merge';
import { usePromptLibrary } from '@/context/usePromptLibrary';
import { useSidebarContext } from '@/context/useSidebar';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Info, AlertTriangle, AlertCircle } from 'lucide-react';

interface ChatViewProps extends ChatMessagesProps {
  scrollRef?: React.RefObject<HTMLDivElement>;
}

export const WIDTH_CHAT_INPUT = 'w-full md:w-[750px]';
const WIDTH_WHEN_BOTH_SIDEBAR_DISPLAYED = 'w-[850px]';
export const WIDTH_WHEN_ONE_SIDEBAR_DISPLAYED = 'w-[1000px]';
const WIDTH_WHEN_NONE_SIDEBAR_DISPLAYED = '';

export const getContentWidthClass = (sidebar: {
  left: boolean;
  right: boolean;
}): string => {
  if (sidebar.left && sidebar.right) return WIDTH_WHEN_BOTH_SIDEBAR_DISPLAYED;
  if (sidebar.left || sidebar.right) return WIDTH_WHEN_ONE_SIDEBAR_DISPLAYED;
  return WIDTH_WHEN_NONE_SIDEBAR_DISPLAYED;
};

const ChatView: React.FC<ChatViewProps> = ({
  messages,
  loading,
  onDelete,
  onReportRendering,
  children,
  className,
  scrollRef,
}) => {
  const { usedPrompt } = usePromptLibrary();
  const { open } = useSidebarContext();

  const widthClass = twMerge(
    'w-full max-w-3xl mx-auto',
    open.left && open.right && 'xl:max-w-4xl',
    !open.left && !open.right && 'xl:max-w-5xl'
  );

  const getAlertProps = (type: string) => {
    switch (type) {
      case 'info':
        return {
          variant: 'default',
          icon: <Info className='h-4 w-4' />,
          title: 'Info',
        };
      case 'warning':
        return {
          variant: 'warning',
          icon: (
            <AlertTriangle className='h-4 w-4 text-[var(--text-custom-yellow-500)]' />
          ),
          title: 'Warning',
        };
      case 'error':
        return {
          variant: 'destructive',
          icon: (
            <AlertCircle className='h-4 w-4 text-[var(--text-custom-red-500)]' />
          ),
          title: 'Error',
        };
      default:
        return {
          variant: 'default',
          icon: <Info className='h-4 w-4' />,
          title: 'Note',
        };
    }
  };

  return (
    <ScrollArea className={twMerge('w-full h-full', className)} ref={scrollRef}>
      <div
        className={twMerge(
          'flex flex-col w-full pb-52 items-center',
          usedPrompt !== null && 'md:pb-52',
          messages.length == 0 && "md:pb-96"
        )}
      >
        {children && children}

        <div className='container mx-auto w-full'>
          {messages.map((message, index) => (
            <div
              id={`chat-message-${message.messageId}`}
              key={`chat-message-${index}`}
              className={twMerge(widthClass)}
            >
              {message.content && (
                <ChatMessage
                  onDelete={onDelete}
                  onReportRendering={onReportRendering}
                  messageId={message.messageId}
                  message={message}
                />
              )}
              {message.info && (
                <div className='mx-8 my-4'>
                  {/* Uncomment to use alerts */}
                  {/* <Alert {...getAlertProps(message.info.type)}>
                    <AlertTitle>
                      {getAlertProps(message.info.type).title}
                    </AlertTitle>
                    <AlertDescription>{message.info.content}</AlertDescription>
                  </Alert> */}
                </div>
              )}
            </div>
          ))}

          {loading && (
            <div className={twMerge(widthClass)}>
              <MessageLoadingAnimation />
            </div>
          )}
        </div>
      </div>
    </ScrollArea>
  );
};

export default ChatView;
