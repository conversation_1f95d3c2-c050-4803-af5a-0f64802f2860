"use client";

import React, { useState, useRef } from "react";
import ContentServiceButtons, {
  ContentServiceItem,
} from "../../ContentServiceButtons";
import RoleTag from "../../RoleTag";
import MessageMetaInfo from "../../MessageMetaInfo";
import MessageContent from "../../MessageContent";
import SearchEngineHelper from "../../SearchEngineHelper";
import { DisplayedMessage } from "@/model/displayedMessage";
import { usePromptLibrary } from "@/context/usePromptLibrary";
import domtoimage from "dom-to-image";
import { usePopup } from "@/context/usePopUp";
import { GeneralPopUp } from "@/components/Popup";

export type TomioAgent = "user" | "system" | "assistant";

interface ChatMessageProps {
  message: DisplayedMessage;
  onDelete?: (id: string) => void;
  onReportRendering?: (markdown: string, screenshot: string) => Promise<void>;
  messageId: string;
}

export function ChatMessage({
  message: {
    role = "assistant",
    content,
    timestamp,
    prompt,
    modelName,
    searchEngineQueries,
  },
  messageId,
  onReportRendering,
  onDelete,
}: ChatMessageProps) {
  const [copyMessage, setCopyMessage] = useState(false);
  const [copyAllMessage, setCopyAllMessage] = useState(false);
  const [rawDisplay, setRawDisplay] = useState(false);
  const [isSendingReport, setIsSendingReport] = useState(false);
  const { usedPrompt, openPromptLibrary } = usePromptLibrary();
  const { openPopup, closePopup } = usePopup();

  const messageRef = useRef<HTMLDivElement>(null);
  const isAssistant = role === "assistant";

  const contentService: ContentServiceItem[] = [
    ...(isAssistant && onReportRendering
      ? [
          {
            label: isSendingReport ? "IS REPORTING" : "REPORT RENDERING",
            onClick: async () => {
              const messageElement = messageRef.current;
              if (messageElement) {
                try {
                  setIsSendingReport(true);
                  const dataUrl = await domtoimage.toPng(messageElement);
                  await onReportRendering(content, dataUrl);
                  setIsSendingReport(false);
                  openPopup(
                    new GeneralPopUp({
                      context: "rendering-report",
                      title: "Thank you!",
                      content: "Your report has been sent",
                      buttons: [
                        { isPrimary: true, title: "OK", onClick: closePopup },
                      ],
                    })
                  );
                } catch (error) {
                  console.error("Failed to capture screenshot:", error);
                  setIsSendingReport(false);
                }
              }
            },
          },
        ]
      : []),

    ...(isAssistant
      ? [
          {
            label: "ASK OTHER MODEL",
            onClick: () =>
              openPopup(
                new GeneralPopUp({
                  context: "ask-other-model",
                  title: "Awesome! It's good to see how other model respond",
                  content:
                    "However, this is a PRO plan and is unavailable at the moment",
                  buttons: [
                    { isPrimary: true, title: "OK", onClick: closePopup },
                  ],
                })
              ),
          },
        ]
      : []),

    {
      label: rawDisplay ? "NORMAL" : "RAW",
      onClick: () => setRawDisplay(!rawDisplay),
    },

    ...(onDelete
      ? [
          {
            label: "DELETE",
            onClick: () => onDelete(messageId),
          },
        ]
      : []),

    {
      label: copyMessage ? "COPIED" : "COPY",
      onClick: () => {
        setCopyMessage(true);
        navigator.clipboard.writeText(content);
        setTimeout(() => setCopyMessage(false), 1000);
      },
    },
  ];

  if (prompt) {
    contentService.push({
      label: copyAllMessage ? "COPIED" : "COPY ALL",
      onClick: () => {
        setCopyAllMessage(true);
        navigator.clipboard.writeText(
          buildText(prompt.promptInstruction, content)
        );
        setTimeout(() => setCopyAllMessage(false), 1000);
      },
    });
  }

  return (
    <div
      ref={messageRef} // Add ref to the container to capture its screenshot
      className={`group/message px-5 mb-5 flex flex-col items-end w-full ${
        role !== "assistant" ? "clear-both" : ""
      }`}
    >
      <div
        className={`${isAssistant ? "bg-[#E2DCF1]" : "bg-white"} 
        ${
          role === "assistant"
            ? "duration-300 ease-in-out rounded-xl rounded-br-none"
            : "border-l-4 border-l-[#150B32]"
        } 
        ${role === "assistant" ? "group-hover/message:bg-[#E2DCF0]" : ""} 
        px-4 py-4 w-full flex flex-col gap-2 overflow-x-auto`}
      >
        <div className="flex items-center gap-x-2 w-full sticky left-0">
          <RoleTag role={role} modelName={modelName} />
          <MessageMetaInfo
            timestamp={timestamp}
            content={content}
            labels={[]}
          />
          {prompt && (
            <button
              onClick={() => openPromptLibrary(prompt)}
              className="bg-[var(--bg-custom-indigo-600)] rounded-sm px-2 text-white"
            >
              <span>{prompt.promptName}</span>
            </button>
          )}
        </div>
        {rawDisplay && <div className="font-bold">RAW CONTENT:</div>}
        <MessageContent
          content={content}
          rawDisplay={rawDisplay}
          isUserMessage={role === "user"}
        />
      </div>
      <ContentServiceButtons
        contentService={contentService}
        role={role}
        content={content}
        messageId={messageId}
      />
      <SearchEngineHelper
        moreContent={true}
        role={role}
        searchEngineQueries={searchEngineQueries}
      />
    </div>
  );
}

function buildText(promptInstruction: string, content: string) {
  return `${promptInstruction}

INPUT:
${content}`;
}