import useJoinFeature from "@/app/hooks/useJoinFeature";
import { usePopup } from "@/context/usePopUp";
import { copyToClipboard } from "@/services/copyToClipboard";
import { getUserEmailOrPopUp } from "@/services/getUserEmailOrPopUp";
import React, { useEffect, useState } from "react";
import { GeneralPopUp } from "./Popup";
import { Button } from "./ui/button";

const MobileAppBanner: React.FC = () => {
  const [email, setEmail] = useState<string>("");
  const [referal, setReferal] = useState<string>("");
  const { openPopup } = usePopup();
  const { buttonLoading, joinFeature } = useJoinFeature();

  async function copyReferalLink() {
    await copyToClipboard(`${window.location.href}?ref=${referal}`);
  }

  async function joinWaitlist() {
    try {
      const _email = email || getUserEmailOrPopUp(openPopup);
      if (!_email) {
        return;
      }

      const referal = await joinFeature(_email, "mobile-app");
      localStorage.setItem("referal_join", referal);
      setReferal(referal);

      openPopup(
        new GeneralPopUp({
          context: "Join Mobile App Waitlist",
          title: "Thanks! You have joined the waitlist",
          content: "While you are waiting it. Would you mind to share?",
          buttons: [
            {
              isPrimary: true,
              onClick: copyReferalLink,
              title: "Copy Share Link 🤩",
            },
            {
              isPrimary: false,
              onClick: () => {},
              title: "I will share myself 👍",
            },
          ],
        })
      );
    } catch (err) {}
  }

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  useEffect(() => {
    const referal = localStorage.getItem("referal_join");
    if (referal) {
      setReferal(referal);
    }
  }, []);

  return (
    <div className="flex flex-col items-center space-y-2 border-dashed border-primary-foreground border-2 text-primary-foreground p-2 rounded-md">
      {referal ? (
        <>
          <button
            onClick={copyReferalLink}
            className="w-full flex space-x-2 justify-center rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-[var(--bg-custom-indigo-500)]"
          >
            Copy Referal Link
          </button>
        </>
      ) : (
        <>
          <p className="text-sm text-center">
            Join the waitlist for the mobile app version
          </p>
          <Button variant="primary" onClick={joinWaitlist} className="w-full">
            {buttonLoading && (
              <svg
                width="16"
                height="16"
                preserveAspectRatio="xMidYMid meet"
                fill="currentColor"
                className="mr-2 animate-spin"
                viewBox="0 0 1792 1792"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M526 1394q0 53-37.5 90.5t-90.5 37.5q-52 0-90-38t-38-90q0-53 37.5-90.5t90.5-37.5 90.5 37.5 37.5 90.5zm498 206q0 53-37.5 90.5t-90.5 37.5-90.5-37.5-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm-704-704q0 53-37.5 90.5t-90.5 37.5-90.5-37.5-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm1202 498q0 52-38 90t-90 38q-53 0-90.5-37.5t-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm-964-996q0 66-47 113t-113 47-113-47-47-113 47-113 113-47 113 47 47 113zm1170 498q0 53-37.5 90.5t-90.5 37.5-90.5-37.5-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm-640-704q0 80-56 136t-136 56-136-56-56-136 56-136 136-56 136 56 56 136zm530 206q0 93-66 158.5t-158 65.5q-93 0-158.5-65.5t-65.5-158.5q0-92 65.5-158t158.5-66q92 0 158 66t66 158z"></path>
              </svg>
            )}
            Yes, I want to join
          </Button>
        </>
      )}
    </div>
  );
};

export default MobileAppBanner;
