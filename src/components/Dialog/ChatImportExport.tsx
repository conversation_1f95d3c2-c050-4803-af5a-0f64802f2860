import React, { useState } from "react";
import { DisplayedMessage } from "@/model/displayedMessage";
import { saveAs } from "file-saver";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ChatImportExportDialogProps {
  roomName: string | undefined;
  messages: DisplayedMessage[];
  onImport: (
    mode: "append" | "overwrite",
    messages: DisplayedMessage[]
  ) => void;
  onClose: () => void;
}

const ChatImportExportDialog: React.FC<ChatImportExportDialogProps> = ({
  roomName,
  messages,
  onImport,
  onClose,
}) => {
  const [exportName, setExportName] = useState<string>(
    roomName || "chat-export"
  );
  const [importMode, setImportMode] = useState<"append" | "overwrite">(
    "append"
  );
  const [importFile, setImportFile] = useState<File | null>(null);
  const [finishImport, setFinishImport] = useState<boolean>(false);

  const handleExport = () => {
    const filteredMessages = messages.map(({ isWriting, ...rest }) => rest);
    const messagesJson = JSON.stringify(filteredMessages, null, 2);
    const blob = new Blob([messagesJson], { type: "application/json" });
    saveAs(blob, `${exportName}-${new Date().toISOString()}.json`);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
    }
  };

  const handleImport = () => {
    if (importFile) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const contents = event.target?.result as string;
        const parsedMessages = JSON.parse(contents) as DisplayedMessage[];
        const updatedMessages = parsedMessages.map((message) => ({
          ...message,
          timestamp: new Date(message.timestamp),
        }));
        onImport(importMode, updatedMessages);
        setFinishImport(true);
        setTimeout(() => setFinishImport(false), 1200);
      };
      reader.readAsText(importFile);
    }
  };

  return (
    <div
      className="
      fixed top-1/2 left-1/2
      w-[90vw] max-w-2xl
      -translate-x-1/2 -translate-y-1/2
      rounded-2xl border bg-white p-6
      px-4
      shadow-xl animate-scale z-50
    "
    >
      <div className="text-2xl font-semibold text-[var(--text-custom-gray-800)] mb-4">
        Chat Import & Export
      </div>

      {/* Export Section */}
      <Card>
        <CardHeader>
          <CardTitle>Export Chat</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <Input
            placeholder="Export file name"
            value={exportName}
            onChange={(e) => setExportName(e.target.value)}
          />
          <Button onClick={handleExport}>Export</Button>
        </CardContent>
      </Card>

      {/* Import Section */}
      <Card>
        <CardHeader>
          <CardTitle>Import Chat</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <Input type="file" accept=".json" onChange={handleFileChange} />

          <div className="flex items-center gap-4">
            <Label htmlFor="import-mode">Import Mode</Label>
            <Select
              value={importMode}
              onValueChange={(value) =>
                setImportMode(value as "append" | "overwrite")
              }
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Select mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="append">Append</SelectItem>
                <SelectItem value="overwrite">Overwrite</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button onClick={handleImport} disabled={!importFile}>
            {finishImport ? "Imported ✅" : "Import"}
          </Button>
        </CardContent>
      </Card>

      <DialogFooter className="mt-4">
        <Button variant="secondary" onClick={onClose}>
          Close
        </Button>
      </DialogFooter>
    </div>
  );
};

export default ChatImportExportDialog;
