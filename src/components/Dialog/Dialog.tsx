import React from "react";
import { createPortal } from "react-dom";

interface DialogProps {
  children: React.ReactNode;
}

export const Dialog: React.FC<DialogProps> = ({ children }) => {
  return createPortal(
    <div className="fixed inset-0 flex items-center justify-center z-[9999]">
      <div className="fixed inset-0 bg-[var(--bg-custom-gray-100)] opacity-20 transition-opacity"></div>
      {children}
    </div>,
    document.body
  );
};
