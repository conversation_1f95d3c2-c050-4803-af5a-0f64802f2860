import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

export interface AdvancedParameter {
  key: string;
  label: string;
  value: string | number | string[] | undefined;
  type: "input" | "select" | "range";
  description: string;
  options?: { value: string | number; label: string }[];
  min?: number;
  max?: number;
  step?: number;
}

interface AdvancedParameterProps {
  param: AdvancedParameter;
  onChange: (key: string, value: string | number | undefined) => void;
}

const AdvancedParameterSetting: React.FC<AdvancedParameterProps> = ({
  param,
  onChange,
}) => {
  const handleChange = (value: string | number | undefined) => {
    onChange(param.key, value);
  };

  const renderInput = () => {
    switch (param.type) {
      case "input":
        return (
          <Input
            value={param.value?.toString() || ""}
            onChange={(e) => handleChange(e.target.value)}
            placeholder="Enter value..."
          />
        );
      case "select":
        return (
          <Select
            value={param.value?.toString() || ""}
            onValueChange={handleChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              {param.options?.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case "range":
        return (
          <div className="flex flex-col space-y-2">
            <Slider
              value={[Number(param.value) || 0]}
              min={param.min}
              max={param.max}
              step={param.step}
              onValueChange={(val) => handleChange(val[0])}
            />
            <div className="text-sm text-muted-foreground">
              Value: {param.value ?? "Default"}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="mb-4">
      <Label className="block text-sm font-medium mb-1">{param.label}</Label>
      {renderInput()}
      <div className="flex items-center justify-between mt-1">
        <Button
          size="sm"
          variant="ghost"
          className="text-xs px-2"
          onClick={() => handleChange(undefined)}
        >
          Reset to default
        </Button>
        <span className="text-xs text-muted-foreground">
          {param.description}
        </span>
      </div>
    </div>
  );
};

interface AdvancedSettingsProps {
  parameters: AdvancedParameter[];
  onChange: (key: string, value: string | number | undefined) => void;
}

const AdvancedModelSettings: React.FC<AdvancedSettingsProps> = ({
  parameters,
  onChange,
}) => {
  return (
    <div className="flex flex-col">
      <h3 className="text-lg font-semibold mb-2">Advanced Parameters</h3>
      {parameters.map((param) => (
        <AdvancedParameterSetting
          key={param.key}
          param={param}
          onChange={onChange}
        />
      ))}
    </div>
  );
};

export default AdvancedModelSettings;
