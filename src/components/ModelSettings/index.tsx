import React, { useState, useEffect } from 'react';
import { Model, ModelConfiguration } from '@/model/ai_model';
import { useModel } from '@/context/useModel';
import { LeftPane } from './LeftPane';
import { RightPane } from './RightPane';
import { PopupCloseButton } from '../Popup';

export const ModelSettings: React.FC<{ model?: Model }> = ({ model }) => {
  const { models, setModels } = useModel();
  const [selectedModel, setSelectedModel] = useState<Model | null>(
    model || null
  );

  const handleConfigurationChange = (
    key: keyof ModelConfiguration,
    value: any
  ) => {
    setSelectedModel((prev) => {
      if (!prev) return prev;

      const updatedModel = {
        ...prev,
        config: {
          ...prev.config,
          [key]: value,
        },
      };

      const updatedModels = models.map((model) =>
        model.id === updatedModel.id ? updatedModel : model
      );
      setModels(updatedModels);

      return updatedModel;
    });
  };

  return (
    <div
      id='model-setting'
      className='relative w-[95%] h-[90%] sm:w-5/6 sm:h-5/6 rounded-lg bg-white overflow-clip flex flex-col sm:flex-row'
    >
      <PopupCloseButton />
      <LeftPane
        models={models}
        selectedModel={selectedModel}
        onSelectModel={setSelectedModel}
      />
      <div className='w-full sm:w-3/4 pt-4 sm:pt-16 pb-5 px-4 sm:px-0'>
        <RightPane
          model={selectedModel}
          onConfigurationChange={handleConfigurationChange}
        />
      </div>
    </div>
  );
};
