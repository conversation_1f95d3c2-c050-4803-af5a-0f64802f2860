'use client';

import React, { useState, useEffect } from 'react';
import { Model, ModelConfiguration } from '@/model/ai_model';
import AdvancedModelSettings from './AdvancedModelSettings';
import { mapModelConfigurationToAdvancedParameters } from './mapModelConfigurationToAdvanceParameter';
import ChatComponent from './ChatComponent';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface RightPaneProps {
  model: Model | null;
  onConfigurationChange: (key: keyof ModelConfiguration, value: any) => void;
}

export const RightPane: React.FC<RightPaneProps> = ({
  model,
  onConfigurationChange,
}) => {
  const [inputValues, setInputValues] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!model) return;

    const initialValues: Record<string, string> = {};
    const keys = [...model.keepLocal.required, ...model.keepLocal.optional];

    keys.forEach((key) => {
      const storedValue = localStorage.getItem(key);
      if (storedValue) {
        initialValues[key] = storedValue;
      }
    });

    setInputValues(initialValues);
  }, [model]);

  const handleParameterChange = (
    key: string,
    value: string | number | undefined
  ) => {
    onConfigurationChange(key as keyof ModelConfiguration, value);
  };

  const handleInputChange = (key: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [key]: value }));
    localStorage.setItem(key, value);
  };

  if (!model)
    return (
      <div className='p-8 text-[var(--text-custom-gray-500)] text-center'>
        Select a model to view configuration.
      </div>
    );

  const parameters = mapModelConfigurationToAdvancedParameters(model.config);
  const { required, optional } = model.keepLocal;
  const allKeys = [...required, ...optional];

  return (
    <div className='flex flex-col lg:flex-row h-full' id={model.id}>
      <ScrollArea className='flex-1 p-4 sm:p-6 bg-white overflow-y-auto'>
        <Card className='mb-4 sm:mb-6'>
          <CardHeader>
            <CardTitle className='text-base sm:text-lg'>Model Keys</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3 sm:space-y-4'>
            {allKeys.map((key) => {
              const isRequired = required.includes(key);
              const value = inputValues[key] || '';
              const isEmpty = isRequired && !value;

              return (
                <div key={key}>
                  <Label htmlFor={key} className='text-sm'>
                    {key}{' '}
                    {isRequired && (
                      <span className='text-[var(--text-custom-red-500)]'>
                        *
                      </span>
                    )}
                  </Label>
                  <Input
                    id={key}
                    type='text'
                    value={value}
                    onChange={(e) => handleInputChange(key, e.target.value)}
                    className={
                      isEmpty
                        ? 'border-[var(--border-custom-red-500)] focus-visible:ring-red-500'
                        : ''
                    }
                  />
                </div>
              );
            })}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='text-base sm:text-lg'>
              Advanced Parameters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AdvancedModelSettings
              parameters={parameters}
              onChange={handleParameterChange}
            />
          </CardContent>
        </Card>
      </ScrollArea>

      <div className='w-full lg:w-1/2 lg:min-w-[400px] flex flex-col mt-4 lg:mt-0'>
        <ChatComponent model={model} />
      </div>
    </div>
  );
};

export default RightPane;
