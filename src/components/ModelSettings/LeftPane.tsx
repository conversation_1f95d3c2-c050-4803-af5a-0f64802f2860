'use client';

import React, { useState } from 'react';
import { Model } from '@/model/ai_model';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '../ui/scroll-area';

interface LeftPaneProps {
  models: Array<Model>;
  selectedModel: Model | null;
  onSelectModel: (model: Model) => void;
}

const groupBy = (models: Array<Model>, criteria: 'vendor' | 'apiKey') => {
  const groups: { [key: string]: Array<Model> } = {};
  models.forEach((model) => {
    let key: string;
    if (criteria === 'vendor') {
      key = model.vendor;
    } else {
      const hasApiKey =
        model.keepLocal?.optional?.length > 0 ||
        model.keepLocal?.required?.length > 0;
      key = hasApiKey ? 'With API Key' : 'Without API Key';
    }

    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(model);
  });
  return groups;
};

export const LeftPane: React.FC<LeftPaneProps> = ({
  models,
  selectedModel,
  onSelectModel,
}) => {
  const [grouping, setGrouping] = useState<'vendor' | 'apiKey'>('vendor');
  const groupedModels = groupBy(models, grouping);

  return (
    <div className='w-full sm:w-72 border-b sm:border-b-0 sm:border-r space-y-4 h-auto sm:h-full flex flex-col'>
      <div className='h-fit pt-4 px-4'>
        <label className='block text-sm font-medium mb-1'>Group By</label>
        <Select
          value={grouping}
          onValueChange={(value) => setGrouping(value as 'vendor' | 'apiKey')}
        >
          <SelectTrigger>
            <SelectValue placeholder='Select grouping' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='vendor'>Vendor</SelectItem>
            <SelectItem value='apiKey'>API Key</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <ScrollArea className='h-[200px] sm:h-full w-full'>
        <div className='h-full flex flex-col space-y-4 sm:space-y-6 p-4'>
          {Object.entries(groupedModels).map(([group, models]) => (
            <div key={group}>
              <h3 className='text-sm font-semibold text-muted-foreground mb-2'>
                {group}
              </h3>
              <div className='space-y-1'>
                {models.map((model) => (
                  <Button
                    key={model.id}
                    variant={
                      selectedModel?.id === model.id ? 'secondary' : 'ghost'
                    }
                    className='w-full justify-start text-sm sm:text-base'
                    onClick={() => onSelectModel(model)}
                  >
                    {model.name}
                  </Button>
                ))}
              </div>
              <Separator className='my-2 sm:my-3' />
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};
