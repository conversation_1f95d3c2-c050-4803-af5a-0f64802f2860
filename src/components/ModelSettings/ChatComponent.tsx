import React, { useState, useEffect, useRef } from "react";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import MultiLineTextInput from "../MultilineTextInput";
import { Model } from "@/model/ai_model";

interface Message {
  text: string;
  isUser: boolean;
}

const ChatComponent: React.FC<{ model: Model }> = ({ model }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  const handleSend = (text: string) => {
    const trimmed = text.trim();
    if (!trimmed) return;

    setMessages((prev) => [
      ...prev,
      { text: trimmed, isUser: true },
      { text: "AI response to: " + trimmed, isUser: false },
    ]);
  };

  useEffect(() => {
    setMessages([]);
  }, [model]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <Card className="flex flex-col h-full rounded-xl">
      <ScrollArea className="flex-1 p-4 space-y-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`max-w-[75%] px-4 py-2 rounded-2xl text-sm shadow ${
              message.isUser
                ? "bg-primary text-primary-foreground self-end"
                : "bg-muted text-muted-foreground self-start border"
            }`}
          >
            {message.text}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </ScrollArea>

      <Separator />

      <div className="p-4">
        <MultiLineTextInput
          maxLines={10}
          placeholder="Type your message..."
          onSend={handleSend}
        />
      </div>
    </Card>
  );
};

export default ChatComponent;
