import { ModelConfiguration } from "@/model/ai_model";
import { AdvancedParameter } from "./AdvancedModelSettings";

export const mapModelConfigurationToAdvancedParameters = (
  config: ModelConfiguration
): AdvancedParameter[] => {
  return [
    {
      key: "temperature",
      label: "Temperature",
      value: config.temperature,
      type: "range",
      description: "Sampling temperature, 0 to 1",
      min: 0,
      max: 1,
      step: 0.1,
    },
    {
      key: "top_p",
      label: "Top P",
      value: config.top_p,
      type: "range",
      description: "Nucleus sampling probability, 0 to 1",
      min: 0,
      max: 1,
      step: 0.1,
    },
    {
      key: "frequencyPenalty",
      label: "Frequency Penalty",
      value: config.frequencyPenalty,
      type: "range",
      description: "Penalty for repeating tokens",
      min: 0,
      max: 2,
      step: 0.1,
    },
    {
      key: "presencePenalty",
      label: "Presence Penalty",
      value: config.presencePenalty,
      type: "range",
      description: "Penalty for introducing new tokens",
      min: 0,
      max: 2,
      step: 0.1,
    },
    {
      key: "maxTokens",
      label: "Max Tokens",
      value: config.maxTokens,
      type: "input",
      description: "Maximum number of tokens in the response",
    },
    {
      key: "stopSequences",
      label: "Stop Sequences",
      value: config.stopSequences,
      type: "input",
      description: "Sequences where the model should stop generating",
    },
    {
      key: "logprobs",
      label: "Logprobs",
      value: config.logprobs,
      type: "input",
      description: "Number of log probabilities to include in the response",
    },
    {
      key: "n",
      label: "Number of Completions",
      value: config.n,
      type: "input",
      description: "Number of completions to generate",
    },
  ];
};
