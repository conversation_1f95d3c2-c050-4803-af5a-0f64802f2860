import { useChatRoomAIAgent } from "@/context/useChatRoomAgent";
import { AIAgent } from "@/model/ai_agent";
import React, { useState } from "react";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { List, LayoutGrid } from "lucide-react";
import {
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const AIAgentViewHStack: React.FC<{ agent: AIAgent; onClick: () => void }> = ({
  agent,
  onClick,
}) => (
  <div
    className="flex items-center space-x-4 border border-[var(--border-custom-purple-500)] p-4 rounded-xl hover:shadow-lg cursor-pointer"
    onClick={onClick}
  >
    <img
      src={agent.icon}
      alt={agent.title}
      className="w-16 h-16 rounded-full"
    />
    <div>
      <h3 className="font-semibold">{agent.title}</h3>
      <p className="text-[var(--text-custom-gray-500)]">{agent.description}</p>
    </div>
  </div>
);

const AIAgentViewVStack: React.FC<{ agent: AIAgent; onClick: () => void }> = ({
  agent,
  onClick,
}) => (
  <div
    className="flex flex-col items-center border border-[var(--border-custom-purple-500)] p-4 rounded-xl hover:shadow-lg cursor-pointer"
    onClick={onClick}
  >
    <img
      src={agent.icon}
      alt={agent.title}
      className="w-16 h-16 mb-2 rounded-full"
    />
    <div className="text-center">
      <h3 className="font-semibold">{agent.title}</h3>
      <p className="text-[var(--text-custom-gray-500)]">{agent.description}</p>
    </div>
  </div>
);

export const AIAgentViews: React.FC<{ agents: AIAgent[] }> = ({ agents }) => {
  const [layout, setLayout] = useState<"list" | "grid">("list");
  const { setSelectedAgent } = useChatRoomAIAgent();

  return (
    <div className="hidden md:flex flex-col items-end container mx-auto px-4">
      <div className="flex justify-between w-full  mb-4 items-center">
        <div className="flex space-x-4 items-center justify-between md:justify-start w-full">
          <div>
            <div className="font-bold">Your AI Agents</div>
            <button className="underline text-[var(--text-custom-purple-600)] flex md:hidden">
              See More
            </button>
          </div>
          <Tabs
            value={layout}
            onValueChange={(val) => setLayout(val as "list" | "grid")}
          >
            <TabsList className="bg-muted flex-row ml-auto gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger
                      value="list"
                      className={`
  flex items-center gap-2 px-4 py-2 rounded-md transition border border-transparent
  text-gray-500 bg-gray-200
  data-[state=active]:bg-[var(--bg-custom-purple-100)] !data-[state=active]:bg-[var(--bg-custom-purple-100)] 
  data-[state=active]:text-[var(--text-custom-purple-700)] !data-[state=active]:text-[var(--text-custom-purple-700)]
  data-[state=active]:border-[var(--border-custom-purple-500)] !data-[state=active]:border-[var(--border-custom-purple-500)]
  data-[state=active]:font-bold data-[state=active]:shadow
`}
                    >
                      <List className="w-4 h-4" />
                      <span className="hidden md:inline">List View</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent className="block md:hidden ">
                    List View
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger
                      value="grid"
                      className={`
    flex items-center gap-2 px-4 py-2 rounded-md transition border
    border-transparent
    text-gray-500
    bg-gray-300
    data-[state=active]:bg-[var(--bg-custom-purple-100)]
    data-[state=active]:text-[var(--text-custom-purple-700)]
    data-[state=active]:border-[var(--border-custom-purple-500)]
    data-[state=active]:font-bold
    data-[state=active]:shadow
  `}
                    >
                      <LayoutGrid className="w-4 h-4" />
                      <span className="hidden md:inline">Grid View</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent className="block md:hidden">
                    Grid View
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TabsList>
          </Tabs>
        </div>
        <button className="underline text-[var(--text-custom-purple-600)] hidden md:flex w-1/3 justify-end">
          See More
        </button>
      </div>

      <div
        className={`grid gap-4 w-full ${
          layout === "list"
            ? "grid-cols-1 sm:grid-cols-2"
            : "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4"
        }`}
      >
        {agents.map((agent, index) =>
          layout === "list" ? (
            <AIAgentViewHStack
              key={index}
              agent={agent}
              onClick={() => setSelectedAgent(agent)}
            />
          ) : (
            <AIAgentViewVStack
              key={index}
              agent={agent}
              onClick={() => setSelectedAgent(agent)}
            />
          )
        )}
      </div>
    </div>
  );
};
