import React, { useState } from "react";
import { Button } from "./ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "./ui/textarea";

interface ImportChatMessagePopupProps {
  onImport: (message: {
    role: "user" | "assistant";
    content: string;
    source?: string;
  }) => void;
  onCancel: () => void;
}

const LLM_SOURCES = ["ChatGPT-4o", "Claude Sonnet", "Deepseek v3", "Other"];

export const ImportChatMessagePopup: React.FC<ImportChatMessagePopupProps> = ({
  onImport,
  onCancel,
}) => {
  const [role, setRole] = useState<"user" | "assistant">("user");
  const [source, setSource] = useState<string>("");
  const [content, setContent] = useState<string>("");

  const handleImport = () => {
    if (!content.trim()) return;
    const message = { role, content, ...(role === "assistant" && { source }) };
    onImport(message);
  };

  return (
    <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center bg-[var(--bg-custom-gray-700)] bg-opacity-50 z-50">
      <div className="bg-white p-6 w-[500px] rounded shadow-md">
        <h2 className="font-semibold mb-4">Import Chat Message</h2>

        <div className="mb-4">
          <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
            Message Content
          </label>
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={4}
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
            Role
          </label>
          <Select value={role} onValueChange={(value) => setRole(value as any)}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="user">User</SelectItem>
              <SelectItem value="assistant">AI (Assistant)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {role === "assistant" && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
              AI Source
            </label>
            <Select value={source} onValueChange={(value) => setSource(value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select source" />
              </SelectTrigger>
              <SelectContent>
                {LLM_SOURCES.map((src) => (
                  <SelectItem key={src} value={src}>
                    {src}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <Button variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            disabled={!content.trim() || (role === "assistant" && !source)}
          >
            Import
          </Button>
        </div>
      </div>
    </div>
  );
};
