import React, { useState } from "react";

type DateRange = {
  startDate: Date | null;
  endDate: Date | null;
};

export const Calendar: React.FC = () => {
  const [selectedRange, setSelectedRange] = useState<DateRange>({
    startDate: null,
    endDate: null,
  });

  const handleDateClick = (date: Date) => {
    if (
      !selectedRange.startDate ||
      (selectedRange.startDate && selectedRange.endDate)
    ) {
      setSelectedRange({ startDate: date, endDate: null });
    } else if (selectedRange.startDate && !selectedRange.endDate) {
      if (date < selectedRange.startDate) {
        setSelectedRange({ startDate: date, endDate: selectedRange.startDate });
      } else {
        setSelectedRange({ ...selectedRange, endDate: date });
      }
    }
  };

  const isDateInRange = (date: Date) => {
    if (selectedRange.startDate && selectedRange.endDate) {
      return date >= selectedRange.startDate && date <= selectedRange.endDate;
    }
    return false;
  };

  const isSameDay = (date1: Date, date2: Date) =>
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate();

  return (
    <div className="max-w-sm w-full shadow-lg">
      {/* Calendar Header */}
      <div className="md:p-8 p-5 dark:bg-[var(--bg-custom-gray-800)] bg-white rounded-t">
        <div className="px-4 flex items-center justify-between">
          <span
            tabIndex={0}
            className="focus:outline-none text-base font-bold dark:text-[var(--text-custom-gray-100)] text-[var(--text-custom-gray-800)]"
          >
            October 2020
          </span>
          <div className="flex items-center">
            <button
              aria-label="calendar backward"
              className="focus:text-[var(--text-custom-gray-400)] hover:text-[var(--text-custom-gray-400)] text-[var(--text-custom-gray-800)] dark:text-[var(--text-custom-gray-100)]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="icon icon-tabler icon-tabler-chevron-left"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <polyline points="15 6 9 12 15 18" />
              </svg>
            </button>
            <button
              aria-label="calendar forward"
              className="focus:text-[var(--text-custom-gray-400)] hover:text-[var(--text-custom-gray-400)] ml-3 text-[var(--text-custom-gray-800)] dark:text-[var(--text-custom-gray-100)]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="icon icon-tabler icon-tabler-chevron-right"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <polyline points="9 6 15 12 9 18" />
              </svg>
            </button>
          </div>
        </div>

        {/* Calendar Days */}
        <div className="flex items-center justify-between pt-12 overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr>
                {["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"].map((day) => (
                  <th key={day}>
                    <div className="w-full flex justify-center">
                      <p className="text-base font-medium text-center text-[var(--text-custom-gray-800)] dark:text-[var(--text-custom-gray-100)]">
                        {day}
                      </p>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }, (_, week) => (
                <tr key={week}>
                  {Array.from({ length: 7 }, (_, day) => {
                    const date = new Date(2020, 9, week * 7 + day + 1); // Example date calculation
                    const inRange = isDateInRange(date);
                    const isStart =
                      selectedRange.startDate &&
                      isSameDay(date, selectedRange.startDate);
                    const isEnd =
                      selectedRange.endDate &&
                      isSameDay(date, selectedRange.endDate);

                    return (
                      <td key={day} className="pt-6">
                        <div
                          className={`px-2 py-2 cursor-pointer flex w-full justify-center ${
                            inRange ? "bg-[var(--bg-custom-indigo-100)]" : ""
                          } ${
                            isStart || isEnd
                              ? "bg-[var(--bg-custom-indigo-500)] text-white"
                              : ""
                          }`}
                          onClick={() => handleDateClick(date)}
                        >
                          <p
                            className={`text-base font-medium ${
                              inRange
                                ? "text-[var(--text-custom-indigo-600)]"
                                : "text-[var(--text-custom-gray-500)]"
                            } ${isStart || isEnd ? "font-bold" : ""}`}
                          >
                            {date.getDate()}
                          </p>
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
