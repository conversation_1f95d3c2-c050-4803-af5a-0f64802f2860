import { ChatRoom } from "@/model/chatroom";
import { useState, useEffect } from "react";
import Image from "next/image";
import iconRoom from "../../public/Profile.png";
import Analytics from "@/services/analytics";
import { useChatRoom } from "@/context/useChatRoom";
import { Icon } from "./Icon";

interface ChatRoomProps {
  room: ChatRoom;
  isActive: boolean;
  onDragStart: (event: React.DragEvent<HTMLDivElement>) => void;
  isBulkAction: boolean | null;
  onBulkActionToggle: (id: string) => void;
}

const ChatRoomView = ({
  room,
  isActive,
  onDragStart,
  isBulkAction,
  onBulkActionToggle,
}: ChatRoomProps) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const { openEditChatRoom, openChatRoom, closeChatRoom } = useChatRoom();
  const { viewModel } = useChatRoom();
  const vm = viewModel;

  useEffect(() => {
    const tooltipShown = localStorage.getItem("dragTooltipShown");
    if (!tooltipShown) {
      setShowTooltip(true);
    }
  }, []);

  const handleEdit = () => {
    openEditChatRoom(room);
    Analytics.getInstance().track("Edit Chat Room Name", {});
  };

  const handleDelete = () => {
    vm.deleteChatRoom(room.id);
    if (isActive) {
      closeChatRoom();
    }
  };

  const handleDragStart = (event: React.DragEvent<HTMLDivElement>) => {
    onDragStart(event);
    if (showTooltip) {
      setShowTooltip(false);
      localStorage.setItem("dragTooltipShown", "true");
    }
  };

  const chatRoomRender = (
    <div
      key={room.id}
      className={`group/room overflow-hidden w-full flex justify-between rounded-lg p-2 shadow-md mb-1 transition hover:bg-[var(--bg-custom-slate-700)] hover:bg-opacity-50 ${
        isActive ? "bg-opacity-50 bg-[var(--bg-custom-slate-500)]" : ""
      }`}
      onDragStart={handleDragStart}
      onClick={() => {
        openChatRoom(room);
      }}
      title={
        showTooltip ? "Drag the room icon to move to another folder" : undefined
      }
    >
      <div className="flex flex-grow gap-2 items-start justify-start cursor-pointer">
        <Image src={iconRoom} alt="iconRoom" className="w-10 h-10" />
        <div className="flex flex-col">
          <h3 className="text-primary-foreground flex-shrink select-none">
            {room.name}
          </h3>
          {/* <div className="text-primary-foreground text-sm text-ellipsis text-nowrap">foobar fdsalfjs;ajflsjdafljsad;fjsda;lfjk</div> */}
        </div>
      </div>
      <span className="ml-1 hidden group-hover/room:flex space-x-2 text-primary-foreground">
        <button onClick={handleEdit}>
          <Icon iconName="Edit" />
        </button>
        <button onClick={handleDelete}>
          <Icon iconName="Trash" />
        </button>
      </span>
    </div>
  );

  if (isBulkAction === null) {
    return chatRoomRender;
  }

  return (
    <div className="flex space-x-2 items-center">
      {isBulkAction !== null && (
        <input
          type="checkbox"
          className="h-5 w-5"
          value={`${isBulkAction || false}`}
          onChange={(e) => onBulkActionToggle(room.id)}
        ></input>
      )}
      {chatRoomRender}
    </div>
  );
};

export default ChatRoomView;
