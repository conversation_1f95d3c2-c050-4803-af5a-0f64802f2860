import { Role } from "@/model/sendMessage";
import React from "react";

interface RoleTagProps {
  role: Role;
  modelName: string;
}

const RoleTag: React.FC<RoleTagProps> = ({ role, modelName }) => {
  const isAssistant = role === "assistant";

  return (
    <div
      className={`font-medium bg-[#362167] text-white px-5 text-xs rounded-lg max-w-max ${
        isAssistant ? "" : ""
      }`}
    >
      <div className={isAssistant ? "" : ""}>
        {isAssistant ? modelName || `AI` : "You"}
      </div>
    </div>
  );
};

export default RoleTag;
