import {
  SidebarLeft,
  <PERSON>bar<PERSON><PERSON>,
  SidebarBottom,
  Trash,
  Menu,
  CloseSquare,
  Edit2,
  Add,
  Folder,
  FolderOpen,
  Setting2,
  Cpu,
  ArrowDown2,
  Box,
  Eye,
  Cloud,
  Chart1,
  Shop,
} from "iconsax-react";

export const IconsUsedInApplication = [
  "SidebarLeft",
  "SidebarRight",
  "SidebarBottom",
  "Trash",
  "Menu",
  "Close",
  "Edit",
  "Add",
  "ArrowDown",
  "ArrowUp",
  "Folder",
  "FolderOpen",
  "Setting",
  "AI",
  "Plugin",
  "Vision",
  "Cloud",
  "Chart",
  "Market",
] as const;
export type IconName = (typeof IconsUsedInApplication)[number];

export const Icon: React.FC<{ iconName: IconName; size?: number }> = ({
  iconName,
  size,
}) => {
  switch (iconName) {
    case "SidebarLeft":
      return <SidebarLeft size={size || 20} />;
    case "SidebarRight":
      return <SidebarRight size={size || 20} />;
    case "SidebarBottom":
      return <SidebarBottom size={size || 20} />;
    case "Trash":
      return <Trash size={size || 20} />;
    case "Menu":
      return <Menu size={size || 20} />;
    case "Close":
      return <CloseSquare size={size || 20} />;
    case "Edit":
      return <Edit2 size={size || 20} />;
    case "Add":
      return <Add size={size || 20} />;
    case "Folder":
      return <Folder size={size || 20} />;
    case "FolderOpen":
      return <FolderOpen size={size || 20} />;
    case "Setting":
      return <Setting2 size={size || 20} />;
    case "AI":
      return <Cpu size={size || 20} />;
    case "ArrowDown":
      return <ArrowDown2 size={size || 20} />;
    case "Plugin":
      return <Box size={size || 20} />;
    case "Plugin":
      return <Box size={size || 20} />;
    case "Vision":
      return <Eye size={size || 20} />;
    case "Cloud":
      return <Cloud size={size || 20} />;
    case "Chart":
      return <Chart1 size={size || 20} />;
    case "Market":
      return <Shop size={size || 20} />;
    default:
      return null;
  }
};
