import React, { useState } from "react";
import { ExtensionItem } from "@/model/extension";

interface LeftBarProps {
  items: ExtensionItem[];
  onItemSelected: (item: ExtensionItem) => void;
}

const LeftBar: React.FC<LeftBarProps> = ({ items, onItemSelected }) => {
  const [searchTerm, setSearchTerm] = useState<string>("");

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const filteredItems = items.filter((item) =>
    item.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-1/4 p-4 flex flex-col">
      <h2 className="text-xl font-bold mb-4">Search</h2>
      <input
        type="text"
        placeholder="Search items..."
        value={searchTerm}
        onChange={handleSearchChange}
        className="w-full p-2 border rounded mb-4"
      />
      <div className="flex flex-col overflow-y-auto w-full space-y-2">
        {filteredItems.map((item) => (
          <div
            key={item.id}
            className="w-full"
            onClick={() => onItemSelected(item)}
          >
            <ItemLayout item={item} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default LeftBar;

interface ItemLayoutProps {
  item: ExtensionItem;
}

const ItemLayout: React.FC<ItemLayoutProps> = ({ item }) => {
  return (
    <div className="flex border p-1 rounded items-center space-x-2 max-w-full">
      <img
        src={item.image}
        alt={item.title}
        className="w-10 h-10 object-cover rounded"
      />
      <div className="flex flex-col flex-grow h-full overflow-x-clip">
        <h3 className="text-sm font-bold text-nowrap text-ellipsis">
          {item.title}
        </h3>
        <p className="text-xs text-[var(--text-custom-gray-600)] text-nowrap text-ellipsis">
          {item.description}
        </p>
        <p className="text-xs text-[var(--text-custom-gray-600)]">
          🛠️ {item.creator}
        </p>
      </div>
      <div className="flex flex-col h-full">
        <div className="flex space-x-2">
          <span className="text-xs text-[var(--text-custom-gray-600)]">
            Downloads: {item.downloadTimes}
          </span>
          <span className="text-xs text-[var(--text-custom-gray-600)]">
            Rating: {item.starRating}
          </span>
        </div>
        <button className="bg-[var(--bg-custom-blue-500)] px-1 rounded">
          Install
        </button>
      </div>
    </div>
  );
};
