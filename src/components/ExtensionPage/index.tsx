import React, { useState } from "react";
import LeftBar from "./LeftBar";
import RightBar from "./RightBar";
import { PopupCloseButton } from "../Popup";
import { ExtensionItem } from "@/model/extension";

export const Extension: React.FC = () => {
  const [selectedExtensionItem, setSelectedExtensionItem] =
    useState<ExtensionItem | null>(null);
  const [items] = useState<ExtensionItem[]>([
    {
      id: 1,
      image: "https://via.placeholder.com/150",
      title: "ExtensionItem 1",
      description: "This is a short description for item 1",
      downloadTimes: 100,
      starRating: 4.5,
      creator: "<PERSON>",
      creatorUrl: "johndoe.org",
      installUrl: "#",
      lastReleased: new Date(),
      lastUpdated: new Date(),
      published: false,
      version: "2.0",
    },
  ]);

  return (
    <div
      id="plugin"
      className="relative w-5/6 h-5/6 rounded-lg bg-white overflow-clip flex"
    >
      <PopupCloseButton />
      <div className="flex w-full h-full">
        <LeftBar items={items} onItemSelected={setSelectedExtensionItem} />
        <RightBar selectedItem={selectedExtensionItem} />
      </div>
    </div>
  );
};
