import { ExtensionItem } from "@/model/extension";
import { DATE_FORMAT } from "@/utils/dateFormat";
import { dateFormat } from "@/utils/ext/date";
import React from "react";
import { Icon } from "../Icon";
import { TabView } from "../TabView";

interface RightBarProps {
  selectedItem: ExtensionItem | null;
}

const RightBar: React.FC<RightBarProps> = ({ selectedItem }) => {
  if (!selectedItem) {
    return <div className="w-3/4 p-4">Select an item to see the details</div>;
  }

  const tabs = [
    {
      id: "detail",
      title: "Detail",
      content: <DetailView item={selectedItem} />,
    },
    {
      id: "features",
      title: "Features",
      content: <FeaturesView />,
    },
  ];

  return (
    <div className="w-3/4 p-4 text-black bg-[var(--bg-custom-white-900)]">
      <div className="flex items-center mb-4">
        <img
          src={selectedItem.image}
          alt={selectedItem.title}
          className="w-20 h-20 object-cover rounded mr-4"
        />
        <div className="flex flex-col space-y-1">
          <div className="flex space-x-2">
            <h2 className="text-2xl font-bold">{selectedItem.title}</h2>
            <p className="p-1 bg-[var(--bg-custom-gray-400)] bg-opacity-5 rounded-md">
              {selectedItem.version}
            </p>
          </div>
          <div className="flex space-x-2">
            <a href={selectedItem.creatorUrl} className="text-black text-sm">
              {selectedItem.creator}
            </a>
            <div className="border-r border-white "></div>
            <div className="flex items-center text-sm">
              <span className="mr-2">Downloads:</span>
              <span>{selectedItem.downloadTimes}</span>
            </div>
            <div className="border-r border-white "></div>
            <div className="flex items-center text-sm">
              <span>Rating:</span>
              <span>{selectedItem.starRating} stars</span>
            </div>
          </div>
          <p className="text-sm text-black mb-4">{selectedItem.description}</p>
          <div className="flex space-x-2">
            <button className="bg-[var(--bg-custom-purple-500)] rounded px-1">
              Disable
            </button>
            <button className="bg-[var(--bg-custom-purple-500)] rounded px-1">
              Uninstall
            </button>
            <button className="bg-[var(--bg-custom-purple-500)] rounded px-1">
              Switch PreRelease
            </button>
            <div className="flex space-x-2">
              <input
                type="checkbox"
                className="h-5 w-5"
                value={"false"}
              ></input>
              <div>Auto Update</div>
            </div>
            <button className="flex space-x-2">
              <Icon iconName="Setting" />
            </button>
          </div>
        </div>
      </div>
      <TabView initialTab="detail" tabs={tabs} />
    </div>
  );
};

export default RightBar;

const DetailView: React.FC<{ item: ExtensionItem }> = ({ item }) => {
  return (
    <div>
      <div className="text-sm text-[var(--text-custom-gray-400)]">
        <p className="mb-1">
          <strong>Last Released:</strong>{" "}
          {dateFormat(item.lastReleased, DATE_FORMAT.YYYY_MM_DD_HH_mm_ss)}
        </p>
        <p className="mb-1">
          <strong>Last Updated:</strong>{" "}
          {dateFormat(item.lastUpdated, DATE_FORMAT.YYYY_MM_DD_HH_mm_ss)}
        </p>
        <p className="mb-1">
          <strong>Published:</strong> {item.published}
        </p>
      </div>
      <div className="mt-6">
        <a
          href={item.installUrl}
          className="text-[var(--text-custom-blue-400)] hover:underline"
        >
          Install from the Extension
        </a>
      </div>
    </div>
  );
};

const FeaturesView: React.FC = () => {
  return <div>Features</div>;
};
