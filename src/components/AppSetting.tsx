import { usePopup } from "@/context/usePopUp";
import React, { useState, useEffect } from "react";
import {
  AIGeneral,
  AppearanceDesign,
  AppearanceIcon,
  AppearanceToolbar,
  DataAnalytics,
  DataStorage,
  AudioTextToSpeech,
  AudioSpeechToText,
} from "./Settings";
import { useAppSettings } from "@/context/useAppSetting";
import { PopupCloseButton } from "./Popup";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

export const AppSetting: React.FC = () => {
  const { resetAllSettings } = useAppSettings();
  const [active, setActive] = useState<{ category: string; pane: string }>({
    category: "Appearance",
    pane: "Design",
  });
  const [expandAll, setExpandAll] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const features = [
    {
      id: "feature1",
      name: "Feature 1",
      description: "Description of Feature 1",
      icon: "path/to/icon1.png",
      title: "Feature 1",
    },
    {
      id: "feature2",
      name: "Feature 2",
      description: "Description of Feature 2",
      icon: "path/to/icon2.png",
      title: "Feature 2",
    },
    {
      id: "feature3",
      name: "Feature 3",
      description: "Description of Feature 3",
      icon: "path/to/icon3.png",
      title: "Feature 3",
    },
    {
      id: "feature4",
      name: "Feature 4",
      description: "Description of Feature 4",
      icon: "path/to/icon1.png",
      title: "Feature 4",
    },
    {
      id: "feature5",
      name: "Feature 5",
      description: "Description of Feature 5",
      icon: "path/to/icon2.png",
      title: "Feature 5",
    },
    {
      id: "feature6",
      name: "Feature 6",
      description: "Description of Feature 6",
      icon: "path/to/icon3.png",
      title: "Feature 6",
    },
  ];

  const settingsPanes = [
    {
      name: "Appearance",
      panes: [
        { name: "Design", view: <AppearanceDesign /> },
        { name: "Icon", view: <AppearanceIcon /> },
        { name: "Toolbar", view: <AppearanceToolbar features={features} /> },
      ],
    },
    {
      name: "AI",
      panes: [{ name: "General", view: <AIGeneral /> }],
    },
    {
      name: "Data",
      panes: [
        { name: "Analytics", view: <DataAnalytics /> },
        { name: "Storage", view: <DataStorage /> },
      ],
    },
    {
      name: "Audio",
      panes: [
        { name: "Text to Speech", view: <AudioTextToSpeech /> },
        { name: "Speech to Text", view: <AudioSpeechToText /> },
      ],
    },
  ];

  const handleCategoryClick = (category: string) => {
    if (!expandAll) {
      const categoryPanes = settingsPanes.find((p) => p.name === category);
      if (categoryPanes) {
        setActive({ category, pane: categoryPanes.panes[0].name });
      }
    }
  };

  const handlePaneClick = (pane: string, category: string) => {
    setActive({ category, pane });
    setSidebarOpen(false); // close on mobile
  };

  const toggleExpandAll = () => setExpandAll((prev) => !prev);

  const activeCategory = settingsPanes.find(
    (pane) => pane.name === active.category
  );
  const activePane =
    activeCategory?.panes.find((pane) => pane.name === active.pane)?.view ||
    null;

  return (
    <div
      id="app-setting"
      className="relative w-full h-full max-h-screen overflow-auto md:w-5/6 md:h-5/6 md:rounded-2xl rounded-none bg-background flex flex-col shadow-xl"
    >
      <PopupCloseButton />
      <div className="flex flex-col md:flex-row h-full">
        {/* Sidebar Toggle (Mobile Only) */}
        <div className="md:hidden p-4">
          <Button onClick={() => setSidebarOpen(!sidebarOpen)}>
            {sidebarOpen ? "Tutup Menu" : "Buka Menu"}
          </Button>
        </div>

        {/* Sidebar */}
        {(sidebarOpen ||
          (typeof window !== "undefined" && window.innerWidth >= 768)) && (
          <div className="md:w-1/4 w-full bg-[var(--bg-custom-gray-100)] flex flex-col justify-between md:block">
            <ScrollArea className="flex-1 space-y-2 pr-2">
              {settingsPanes.map((category) => (
                <div key={category.name}>
                  <Button
                    variant="ghost"
                    className={`w-full justify-start font-semibold ${
                      expandAll
                        ? "opacity-60 cursor-not-allowed"
                        : active.category === category.name
                        ? "bg-muted"
                        : ""
                    }`}
                    onClick={() => handleCategoryClick(category.name)}
                    disabled={expandAll}
                  >
                    {category.name}
                  </Button>
                  {(expandAll || active.category === category.name) && (
                    <div className="ml-4 mt- space-y-">
                      {category.panes.map((pane) => (
                        <Button
                          key={pane.name}
                          variant="ghost"
                          className={`w-full justify-start text-sm ${
                            active.pane === pane.name
                              ? "bg-muted font-semibold"
                              : ""
                          }`}
                          onClick={() =>
                            handlePaneClick(pane.name, category.name)
                          }
                        >
                          {pane.name}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </ScrollArea>
            <div className="flex flex-col gap-2 my-4 px-4">
              <Button onClick={toggleExpandAll}>
                {expandAll ? "Close" : "Open"}
              </Button>
              <Button variant="destructive" onClick={resetAllSettings}>
                Reset
              </Button>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="md:w-3/4 w-full pt-6 pb-5 px-4 md:pt-16 md:px-6">
          <ScrollArea className="h-full pr-2 pb-4">{activePane}</ScrollArea>
        </div>
      </div>
    </div>
  );
};

export default AppSetting;
