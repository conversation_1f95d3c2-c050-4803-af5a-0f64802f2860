"use client";

import React, { useState, useRef, useEffect } from "react";
import { Send, Paperclip, Smile, Mic } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface MultiLineTextInputProps {
  maxLines?: number;
  placeholder?: string;
  onSend?: (text: string) => void;
  className?: string;
}

export default function MultiLineTextInput({
  maxLines = 5,
  placeholder = "Type a message...",
  onSend = () => {},
  className = "",
}: MultiLineTextInputProps) {
  const [text, setText] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    textarea.style.height = "auto";
    const lineHeight =
      Number.parseInt(getComputedStyle(textarea).lineHeight) || 24;
    const maxHeight = lineHeight * maxLines;
    const newHeight = Math.min(textarea.scrollHeight, maxHeight);
    textarea.style.height = `${newHeight}px`;
  }, [text, maxLines]);

  const handleSend = () => {
    if (text.trim()) {
      onSend(text);
      setText("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className={cn("border rounded-xl bg-background", className)}>
      <textarea
        ref={textareaRef}
        value={text}
        onChange={(e) => setText(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="w-full px-4 py-3 resize-none overflow-y-auto focus:outline-none rounded-t-xl text-sm min-h-[80px] max-h-[calc(1.5em*5)]"
      />

      <div className="flex items-center justify-between px-3 py-2 border-t bg-muted rounded-b-xl">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" type="button">
            <Paperclip className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" type="button">
            <Smile className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" type="button">
            <Mic className="h-4 w-4" />
          </Button>
        </div>

        <Button onClick={handleSend} disabled={!text.trim()} size="sm">
          <Send className="h-4 w-4 mr-1" />
          Send
        </Button>
      </div>
    </div>
  );
}
