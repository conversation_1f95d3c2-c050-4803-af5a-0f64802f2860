import React, { useState, useEffect, useMemo } from "react";
import { ChatRoom } from "@/model/chatroom";
import ChatRoomView from "./ChatRoomView";
import { useChatRoom } from "@/context/useChatRoom";
import { useChatFolder } from "@/context/useChatFolder";
import { ChatFolder } from "@/model/chatFolder";
import { Icon } from "./Icon";

const ChatFolderComponent: React.FC<{
  folder: ChatFolder;
  onDragStart: (event: React.DragEvent<HTMLDivElement>, roomId: string) => void;
  isDragOver: boolean;
  isBulkAction: boolean | null;
  onBulkActionToggle: (id: string) => void;
}> = ({
  folder,
  onDragStart,
  isDragOver,
  isBulkAction,
  onBulkActionToggle,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(() => {
    const savedState = localStorage.getItem(`folder-${folder.id}-expanded`);
    return savedState === "true";
  });

  const { openRoom, closeChatRoom } = useChatRoom();
  const { viewModel: chatFolderVM, openChatFolder } = useChatFolder();
  const { viewModel: chatRoomVM, openEditChatRoom } = useChatRoom();

  const activeMenu = useMemo(
    () => folder.rooms.find((room) => room.id === openRoom?.id),
    [openRoom, folder.rooms]
  );

  const handleEditClick = () => {
    openChatFolder(folder);
  };

  const handleRemove = () => {
    chatFolderVM.deleteChatRoom(folder.id);
    if (openRoom && folder.rooms.map((e) => e.id).includes(openRoom.id)) {
      closeChatRoom();
    }
  };

  const addChatRoom = async () => {
    const room = await chatRoomVM.createChatRoom(
      "New Chat Room",
      [],
      folder.id
    );
    openEditChatRoom(room);
  };

  const toggleExpand = () => {
    setIsExpanded((prev) => {
      const newState = !prev;
      localStorage.setItem(`folder-${folder.id}-expanded`, newState.toString());
      return newState;
    });
  };

  return (
    <div
      className={`p-2 text-white text-left w-full flex flex-col group ${
        isDragOver ? "border border-[var(--border-custom-blue-300)]" : ""
      }`}
    >
      <div className="flex justify-between items-center mb-4">
        <div
          className="flex space-x-2 items-center cursor-pointer"
          onClick={toggleExpand}
        >
          <div className="h-full aspect-square">
            {isExpanded ? (
              <Icon iconName="FolderOpen" />
            ) : (
              <Icon iconName="Folder" />
            )}
          </div>
          <span>
            {folder.folderName} ({folder.rooms.length})
          </span>
        </div>
        <div className="ml-1 flex space-x-2">
          <button onClick={addChatRoom}>
            <Icon iconName="Add" />
          </button>
          <button onClick={handleEditClick}>
            <Icon iconName="Edit" />
          </button>
          <button onClick={handleRemove}>
            <Icon iconName="Trash" />
          </button>
        </div>
      </div>
      <div className="flex flex-col ml-1">
        {isExpanded &&
          folder.rooms.map((room: ChatRoom) => (
            <div key={room.id} className="border-l border-white p-2">
              <ChatRoomView
                room={room}
                isActive={room.id === activeMenu?.id}
                onDragStart={(event) => onDragStart(event, room.id)}
                isBulkAction={isBulkAction}
                onBulkActionToggle={onBulkActionToggle}
              />
            </div>
          ))}
      </div>
    </div>
  );
};

export default ChatFolderComponent;
