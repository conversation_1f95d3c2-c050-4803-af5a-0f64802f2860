import { useChatRoom } from "@/context/useChatRoom";
import { ChatRoom } from "@/model/chatroom";
import React, { useEffect, useState } from "react";

interface ChatRoomPopupProps {}

const ChatRoomPopup: React.FC<ChatRoomPopupProps> = ({}) => {
  const { editRoom, closeEditChatRoom, viewModel } = useChatRoom();
  const [editableChatRoom, setChatRoom] = useState<ChatRoom | null>(null);
  const [newTag, setNewTag] = useState(""); // State for the new tag input

  useEffect(() => {
    setChatRoom(editRoom);
  }, [editRoom?.id]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!editableChatRoom) return;
    const { name, value } = e.target;
    setChatRoom({
      ...editableChatRoom,
      [name]: value,
    });
  };

  const handleTagChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewTag(e.target.value);
  };

  const handleAddTag = () => {
    if (!editableChatRoom || !newTag.trim()) return;
    const updatedTags = [...(editableChatRoom.tags || []), newTag.trim()];
    setChatRoom({
      ...editableChatRoom,
      tags: updatedTags,
    });
    setNewTag("");
  };

  const handleDeleteTag = (tag: string) => {
    if (!editableChatRoom) return;
    const updatedTags = editableChatRoom.tags.filter((t) => t !== tag);
    setChatRoom({
      ...editableChatRoom,
      tags: updatedTags,
    });
  };

  const handleSave = async () => {
    if (!editableChatRoom) return;
    if (editableChatRoom.id) {
      viewModel.updateRoomName(editableChatRoom.id, editableChatRoom.name);
      viewModel.updateRoomTags(editableChatRoom.id, editableChatRoom.tags);
    } else {
      viewModel.addChatRoom(editableChatRoom.name, {
        folderId: editableChatRoom.folderId,
        tags: editableChatRoom.tags,
      });
    }
    closeEditChatRoom();
    setChatRoom(null);
  };

  if (!editableChatRoom) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center bg-[var(--bg-custom-gray-700)] bg-opacity-50 z-[9999]">
      <div className="bg-white p-4 w-[500px] rounded shadow-md">
        <h2 className="font-semibold mb-4">Configure Chat Room</h2>

        <div className="mb-4">
          <label
            className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1"
            htmlFor="roomName"
          >
            Name
          </label>
          <input
            type="text"
            id="roomName"
            name="name"
            value={editableChatRoom.name}
            onChange={handleInputChange}
            className="w-full border rounded px-3 py-2 outline-none"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-[var(--text-custom-gray-700)] mb-1">
            Tags
          </label>
          <div className="flex items-center space-x-2 mb-3">
            <input
              type="text"
              value={newTag}
              onChange={handleTagChange}
              className="w-full border rounded px-3 py-2 outline-none"
              placeholder="Enter tag and press Enter"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleAddTag();
                }
              }}
            />
            <button
              onClick={handleAddTag}
              className="bg-[var(--bg-custom-indigo-600)] text-white px-3 py-2 rounded"
            >
              Add
            </button>
          </div>

          <div className="flex flex-wrap gap-2 ">
            {(editableChatRoom.tags || []).map((tag) => (
              <span
                key={tag}
                className="bg-[var(--bg-custom-gray-200)] px-3 py-1 rounded-full text-sm text-[var(--text-custom-gray-700)] flex items-center space-x-2 text-white"
              >
                <span>{tag}</span>
                <button
                  onClick={() => handleDeleteTag(tag)}
                  className="text-[var(--text-custom-red-500)]"
                >
                  &times;
                </button>
              </span>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            className="mt-3 inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-[var(--text-custom-gray-900)] shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
            onClick={() => {
              closeEditChatRoom();
              setChatRoom(null);
            }}
          >
            Cancel
          </button>
          <button
            className="justify-center rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[var(--bg-custom-indigo-500)]"
            onClick={handleSave}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatRoomPopup;
