import React, { useState, useEffect, useRef } from "react";
import TextInfo from "./TextInfo";
import { usePopup } from "@/context/usePopUp";

interface Option {
  value: string;
  label: string;
  status?: string;
  onClick: () => void;
}

interface DropdownProps {
  label: string;
  options: Option[];
}

const Dropdown: React.FC<DropdownProps> = ({ label, options }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isDropdownOpen]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const selectOption = (option: Option) => {
    setSelectedOption(option);
    setIsDropdownOpen(false);
  };

  return (
    <div ref={dropdownRef} className="relative inline-block text-left">
      <button
        id="dropdownDefaultButton"
        data-dropdown-toggle="dropdown"
        onClick={toggleDropdown}
        className="text-white bg-[var(--bg-custom-blue-700)] hover:bg-[var(--bg-custom-blue-800)] focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-[var(--bg-custom-blue-600)] dark:hover:bg-[var(--bg-custom-blue-700)] dark:focus:ring-blue-800"
        type="button"
      >
        {selectedOption ? selectedOption.label : label}{" "}
        <svg
          className={`w-2.5 h-2.5 ml-2.5 transform ${
            isDropdownOpen ? "rotate-180" : ""
          }`}
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 10 6"
        >
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="m1 1 4 4 4-4"
          />
        </svg>
      </button>
      {/* Dropdown menu */}
      {isDropdownOpen && (
        <div
          id="dropdown"
          className="mt-2 absolute z-10 bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-[var(--bg-custom-gray-700)]"
        >
          <ul
            className="py-2 text-sm text-[var(--text-custom-gray-700)] dark:text-[var(--text-custom-gray-200)]"
            aria-labelledby="dropdownDefaultButton"
          >
            {options.map((option) => (
              <li
                key={option.value}
                className="px-2 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-600)] hover:text-white"
              >
                <button
                  onClick={() => {
                    if (!option.status) {
                      selectOption(option);
                    }
                    option.onClick();
                  }}
                  className="w-full p-2 flex items-center justify-between "
                >
                  <span>{option.label}</span>
                  {option.status && <TextInfo>{option.status}</TextInfo>}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
