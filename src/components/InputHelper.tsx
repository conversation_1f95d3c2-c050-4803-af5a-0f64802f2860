import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { usePopup } from "@/context/usePopUp";
import Analytics from "@/services/analytics";
import ButtonTimer from "./ButtonTimer";
import { GeneralPopUp } from "./Popup";
import { RefreshCcw, <PERSON>ser, Share2, Trash2, <PERSON><PERSON>ircle } from "lucide-react";
import {
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface InputHelperProps extends React.HTMLProps<HTMLDivElement> {
  className: string;
  regenerate: () => void;
  resetMessages: () => void;
  clearContext: () => void;
  share: () => void;
}

const InputHelper: React.FC<InputHelperProps> = ({
  className,
  regenerate,
  resetMessages,
  clearContext,
  share,
  ...rest
}) => {
  const { openPopup } = usePopup();

  return (
    <TooltipProvider>
      <div
        className={`${className} flex flex-wrap gap-2 animate-spreadFromCenter`}
        {...rest}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="secondary"
              className="bg-[var(--bg-custom-yellow-600)] hover:bg-[var(--bg-custom-yellow-500)] px-2 sm:px-4"
              onClick={regenerate}
            >
              <RefreshCcw className="h-5 w-5" />
              <span className="hidden sm:inline ml-2">Regenerate</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent className="block sm:hidden">
            Regenerate
          </TooltipContent>
        </Tooltip>

        <ButtonTimer
          onClick={clearContext}
          timer={2000}
          secondaryElement={
            <Tooltip>
              <TooltipTrigger asChild>
                <Button className="bg-[var(--bg-custom-green-600)] hover:bg-[var(--bg-custom-green-500)] px-2 sm:px-4">
                  <CheckCircle className="h-5 w-5" />
                  <span className="hidden sm:inline ml-2">Context Cleared</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent className="block sm:hidden">
                Context Cleared
              </TooltipContent>
            </Tooltip>
          }
        >
          {(triggerClear) => (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={triggerClear}
                  className="bg-[var(--bg-custom-green-600)] hover:bg-[var(--bg-custom-green-500)] px-2 sm:px-4"
                >
                  <Eraser className="h-5 w-5" />
                  <span className="hidden sm:inline ml-2">Clear Context</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent className="block sm:hidden">
                Clear Context
              </TooltipContent>
            </Tooltip>
          )}
        </ButtonTimer>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="secondary"
              className="px-2 sm:px-4"
              onClick={() => {
                openPopup(
                  new GeneralPopUp({
                    context: "Share Chat",
                    title: "Do you want to share this chat?",
                    content: `Sharing this chat will upload your chat history to the Tomio server.\n\nThe data will be stored securely for 30 days before being deleted automatically.`,
                    buttons: [
                      {
                        isPrimary: true,
                        onClick: () => {
                          Analytics.getInstance().track(
                            "Share Chat Confirm",
                            {}
                          );
                          share();
                        },
                        title: "Yes, share it",
                      },
                      {
                        isPrimary: false,
                        onClick: () => {
                          Analytics.getInstance().track(
                            "Share Chat Cancel",
                            {}
                          );
                        },
                        title: "Cancel",
                      },
                    ],
                  })
                );
                Analytics.getInstance().track("Share Chat Display", {});
              }}
            >
              <Share2 className="h-5 w-5" />
              <span className="hidden sm:inline ml-2">Share</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent className="block sm:hidden">Share</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="bg-[var(--bg-custom-orange-600)] hover:bg-[var(--bg-custom-orange-500)] px-2 sm:px-4"
              onClick={resetMessages}
            >
              <Trash2 className="h-5 w-5" />
              <span className="hidden sm:inline ml-2">Reset Chat</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent className="block sm:hidden">
            Reset Chat
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
};

export default InputHelper;
