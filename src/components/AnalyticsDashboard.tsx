import React, { useState, useEffect } from "react";
import { ChartView } from "./ChartView";
import { TabView } from "./TabView";
import { PopupCloseButton } from "./Popup";
import { TimeRangeSelector } from "./TimeRangeSelector";
import { IndexDBRepo } from "@/repo/IndexDb";

const mockData3 = {
  labels: ["Tool A", "Tool B", "Tool C", "Tool D"],
  datasets: [
    {
      label: "Usage",
      data: [30, 50, 70, 40],
      backgroundColor: "rgba(153, 102, 255, 0.5)",
    },
  ],
};

const mockData4 = {
  labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
  datasets: [
    {
      label: "Session Duration (mins)",
      data: [45, 50, 55, 40],
      borderColor: "rgba(255, 159, 64, 0.5)",
      fill: false,
    },
  ],
};

export const AnalyticsDashboard: React.FC = () => {
  const dbRepo = new IndexDBRepo();

  const [chartDataSendMessage, setChartDataSendMessage] = useState<any>({
    labels: [],
    datasets: [],
  });

  const [chartDataModelCount, setChartDataModelCount] = useState<any>({
    labels: [],
    datasets: [],
  });

  const [range, setRange] = useState<{ startDate: Date; endDate: Date }>({
    startDate: (() => {
      const date = new Date();
      date.setDate(date.getDate() - 3);
      return date;
    })(),
    endDate: new Date(),
  });

  const fetchSendMessageCount = async (range: {
    startDate: Date;
    endDate: Date;
  }) => {
    const startDate = range.startDate;
    const endDate = range.endDate;

    const timeDifference = endDate.getTime() - startDate.getTime();

    const numberOfDays = Math.ceil(timeDifference / (1000 * 3600 * 24));

    const eventCounts = await dbRepo.countEventsByDay(
      "Send Message",
      numberOfDays
    );

    const labels = Object.keys(eventCounts).map((dateStr) => {
      const date = new Date(dateStr);
      return date.toLocaleString("en-US", { weekday: "long" });
    });

    const data = Object.values(eventCounts).map((count) =>
      parseInt(count.toString())
    );

    const formattedData = {
      labels: labels,
      datasets: [
        {
          label: "Messages",
          data: data,
          backgroundColor: "rgba(75, 192, 192, 0.5)",
        },
      ],
    };

    setChartDataSendMessage(formattedData);
  };

  const fetchSendMessageModelCount = async (range: {
    startDate: Date;
    endDate: Date;
  }) => {
    const startDate = range.startDate;
    const endDate = range.endDate;

    const timeDifference = endDate.getTime() - startDate.getTime();
    const numberOfDays = Math.ceil(timeDifference / (1000 * 3600 * 24));

    const modelCounts = await dbRepo.countEventsByModel(
      "Send Message",
      numberOfDays
    );

    const labels = Object.keys(modelCounts);
    const data = Object.values(modelCounts).map((count) =>
      parseInt(count.toString())
    );

    const getRandomColor = () => {
      const r = Math.floor(Math.random() * 255);
      const g = Math.floor(Math.random() * 255);
      const b = Math.floor(Math.random() * 255);
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    };

    const backgroundColor = labels.map(() => getRandomColor());

    const formattedData = {
      labels: labels,
      datasets: [
        {
          label: "Model Usage",
          data: data,
          backgroundColor: backgroundColor,
        },
      ],
    };

    setChartDataModelCount(formattedData);
  };

  useEffect(() => {
    fetchSendMessageCount(range);
    fetchSendMessageModelCount(range);
  }, [range]);

  const handleRangeSelect = (selectedRange: {
    startDate: Date;
    endDate: Date;
  }) => {
    setRange(selectedRange);
  };

  return (
    <div
      id="app-setting"
      className="
      relative
      h-5/6
      w-[90vw]  max-w-7xl
      rounded-lg bg-white overflow-clip flex flex-col
      px-4 py-6 md:p-10
    "
    >
      <PopupCloseButton />
      <div className="flex flex-col space-y-2 w-full h-full">
        <h2 className="font-bold">Analytics Dashboard</h2>
        <div className="z-20">
          <TimeRangeSelector onSelectRange={handleRangeSelect} />
        </div>
        <TabView
          inactiveMode="remove"
          tabs={[
            {
              id: "sent-messages",
              title: "Sent Messages",
              content: (
                <ChartView
                  chartType="Bar"
                  data={chartDataSendMessage}
                  label="Messages Sent"
                />
              ),
            },
            {
              id: "model-usage",
              title: "Model Usage",
              content: (
                <ChartView
                  chartType="Pie"
                  data={chartDataModelCount}
                  label="Model Usage"
                />
              ),
            },
            {
              id: "tools-usage",
              title: "Tools Usage",
              content: (
                <ChartView
                  chartType="Bar"
                  data={mockData3}
                  label="Most Used Tools"
                />
              ),
            },
            {
              id: "session-duration",
              title: "Session Duration",
              content: (
                <ChartView
                  chartType="Line"
                  data={mockData4}
                  label="Average Session Duration"
                />
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};
