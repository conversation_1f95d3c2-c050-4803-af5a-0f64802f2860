import React from "react";
import MarkdownContent from "./MarkdownContent";

interface MessageContentProps {
  content: string;
  rawDisplay: boolean;
  isUserMessage: boolean;
}

const MessageContent: React.FC<MessageContentProps> = ({
  content,
  rawDisplay,
  isUserMessage,
}) => {
  if (rawDisplay) {
    return (
      <div className="w-full flex-grow p-4 bg-[var(--bg-custom-gray-400)] ">
        <pre className="break-words whitespace-pre-wrap w-full">{content}</pre>
      </div>
    );
  }

  if (isUserMessage) {
    return (
      <div className="w-full flex-grow break-words whitespace-pre-wrap">
        {content}
      </div>
    );
  }

  return (
    <div className="w-full flex-grow">
      <MarkdownContent content={content} />
    </div>
  );
};

export default MessageContent;
