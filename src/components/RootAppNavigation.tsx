"use client";

import React, { useEffect, useMemo } from "react";
import SideBar from "./Layouts/SideBar";
import RightBar from "./Fragments/RightBar";
import LeftBar from "./Fragments/LeftBar";
import ChatSection from "./ChatSection/ChatSection";
import { usePopup } from "@/context/usePopUp";
import { CustomPopUp } from "./Popup";
import WelcomeBanner from "./WelcomeBanner";
import { twMerge } from "tailwind-merge";
import { useAppSettings } from "@/context/useAppSetting";
import { ChatRoomAIAgentProvider } from "@/context/useChatRoomAgent";
import { useRoot } from "@/context/useRoot";
import { SIDEBAR_COOKIE, SIDEBAR_TYPE } from "./SidebarCookie";
import { useSidebarContext } from "@/context/useSidebar";

const RootAppNavigation: React.FC = () => {
  const root = useRoot();
  const { settings } = useAppSettings();
  const { openPopup } = usePopup();
  const {
    refs,
    setOpen: setSidebarOpen,
    open: sidebarOpen,
  } = useSidebarContext();

  const setCookie = (name: string, value: string, days: number) => {
    const expires = new Date(Date.now() + days * 864e5).toUTCString();
    document.cookie = `${name}=${value}; expires=${expires}; path=/`;
  };

  const _setBar = (type: SIDEBAR_TYPE, status: boolean) => {
    setCookie(SIDEBAR_COOKIE.getKey(type), SIDEBAR_COOKIE.setValue(status), 1);
    setSidebarOpen(type, status);
  };

  useEffect(() => {
    const isMobile = window.innerWidth <= 768;

    if (!isMobile) {
      openPopup(
        new CustomPopUp({
          context: "welcome-banner",
          view: (
            <WelcomeBanner
              title="Welcome To tomioAI"
              description="foobar"
              imageSrc="https://i.pinimg.com/736x/a7/b6/12/a7b612ca08cfc29711ea61d70831e12a.jpg"
            />
          ),
        })
      );
    }
  }, []);

  const themeConfig = settings.appearance;
  const unit = "px";

  const theme = useMemo(
    () => themeConfig.theme || "system",
    [themeConfig.theme]
  );

  return (
    <div
      id="root-app-navigation"
      className={twMerge("flex w-full h-full bg-[var(--sidebar-background)]")}
    >
      <SideBar ref={refs.left} type="left" isOpen={sidebarOpen.left}>
        <LeftBar />
      </SideBar>

      <ChatRoomAIAgentProvider>
          <ChatSection
            setLeftBar={(status) => _setBar("left", status)}
            setRightBar={(status) => _setBar("right", status)}
            rightBar={sidebarOpen.right}
            leftBar={sidebarOpen.left}
          />
      </ChatRoomAIAgentProvider>

      <SideBar ref={refs.right} type="right" isOpen={sidebarOpen.right}>
        <RightBar />
      </SideBar>
    </div>
  );
};

export default RootAppNavigation;
