import { useFeedback } from "@/context/useFeedback";
import Analytics from "@/services/analytics";
import React, { useState, useEffect } from "react";

interface FeedbackPopupProps {}

const COUNTDOWN_AUTOCLOSE = 5;

const FeedbackPopup: React.FC<FeedbackPopupProps> = ({}) => {
  const [feedback, setFeedback] = useState("");
  const [showThankYou, setShowThankYou] = useState(false);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [countdown, setCountdown] = useState(COUNTDOWN_AUTOCLOSE);
  const { showFeedbackPopup, setShowFeedbackPopup } = useFeedback();

  const handleSubmit = async () => {
    setButtonLoading(true);
    const response = await fetch("/api/feedback", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ feedback }),
    });

    if (!response.ok) {
      throw new Error("Failed to add to feedback");
    }

    const data = await response.json();
    setFeedback("");
    setButtonLoading(false);
    setShowThankYou(true);
    setCountdown(COUNTDOWN_AUTOCLOSE);
    Analytics.getInstance().track("Submit Feedback", {});
  };

  useEffect(() => {
    if (!showFeedbackPopup) {
      return;
    }
    Analytics.getInstance().track("Show Feedback PopupOverlay", {});
  }, [showFeedbackPopup]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (showThankYou) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [showThankYou]);

  useEffect(() => {
    if (countdown === 0) {
      setShowFeedbackPopup(false);
      setShowThankYou(false);
    }
  }, [countdown]);

  if (!showFeedbackPopup) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-500 bg-opacity-50 z-[9999]">
      <div className="bg-white p-6 rounded-lg shadow-md w-96">
        <h2 className=" font-semibold mb-4">Leave Feedback</h2>
        {showThankYou ? (
          <div className="flex flex-col">
            <p className="mb-4">Thank you for your valuable feedback</p>
            <p className="mb-4">You can always add more feedback later</p>
            <p className="mb-4">Again, very much thank you!</p>
          </div>
        ) : (
          <textarea
            placeholder="Type your feedback here..."
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            className="w-full p-2 border border-[var(--border-custom-gray-300)] rounded mb-4"
            rows={4}
          />
        )}
        <div className="flex space-x-3 justify-end">
          {!showThankYou && (
            <button
              onClick={() => {
                setShowFeedbackPopup(false);
                Analytics.getInstance().track("Cancel Feedback", {});
              }}
              className="justify-center rounded-md px-3 py-2 text-sm font-semibold text-black shadow-sm"
            >
              Cancel
            </button>
          )}
          {showThankYou ? (
            <button
              onClick={() => {
                setShowFeedbackPopup(false);
                setShowThankYou(false);
                setCountdown(0);
              }}
              className="justify-center rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[var(--bg-custom-indigo-500)]"
            >
              Close ({countdown})
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              className="justify-center flex rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[var(--bg-custom-indigo-500)]"
            >
              {buttonLoading && (
                <svg
                  width="16"
                  height="16"
                  preserveAspectRatio="xMidYMid meet"
                  fill="currentColor"
                  className="mr-2 animate-spin"
                  viewBox="0 0 1792 1792"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M526 1394q0 53-37.5 90.5t-90.5 37.5q-52 0-90-38t-38-90q0-53 37.5-90.5t90.5-37.5 90.5 37.5 37.5 90.5zm498 206q0 53-37.5 90.5t-90.5 37.5-90.5-37.5-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm-704-704q0 53-37.5 90.5t-90.5 37.5-90.5-37.5-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm1202 498q0 52-38 90t-90 38q-53 0-90.5-37.5t-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm-964-996q0 66-47 113t-113 47-113-47-47-113 47-113 113-47 113 47 47 113zm1170 498q0 53-37.5 90.5t-90.5 37.5-90.5-37.5-37.5-90.5 37.5-90.5 90.5-37.5 90.5 37.5 37.5 90.5zm-640-704q0 80-56 136t-136 56-136-56-56-136 56-136 136-56 136 56 56 136zm530 206q0 93-66 158.5t-158 65.5q-93 0-158.5-65.5t-65.5-158.5q0-92 65.5-158t158.5-66q92 0 158 66t66 158z"></path>
                </svg>
              )}
              Submit
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FeedbackPopup;
