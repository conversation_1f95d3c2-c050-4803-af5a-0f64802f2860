import React, { forwardRef } from "react";
import { k_SIDEBAR_WIDTH } from "@/consts/sidebar";
import { cn } from "@/lib/utils";
import { useSidebarContext } from "@/context/useSidebar";
import { useIsMobile } from "@/hooks/use-mobile";

interface SidebarProps {
  isOpen: boolean;
  type: "left" | "right";
  children: React.ReactNode;
}

const SideBar = forwardRef<HTMLElement, SidebarProps>(
  ({ isOpen, type, children }, ref) => {
    const { setOpen } = useSidebarContext();
    const isMobile = useIsMobile();

    return (
      <nav
        ref={ref}
        id={`sidebar-${type}`}
        className={cn(
          "transition-all duration-500 overflow-hidden",
          isMobile
            ? "fixed top-0 h-screen w-screen z-[20] flex items-start"
            : "relative h-full",
          {
            "translate-x-0 opacity-100": isOpen,
            "-translate-x-full opacity-0": !isOpen && type === "left",
            "translate-x-full opacity-0": !isOpen && type === "right",
            "left-0 flex-row": type === "left",
            "right-0 flex-row-reverse": type === "right",
          }
        )}
        style={{
          minWidth: isOpen ? k_SIDEBAR_WIDTH : 0,
        }}
      >
        <div
          className={cn(
            "flex flex-col h-full w-full transition-all duration-300",
            isMobile ? "bg-purple-900" : "bg-transparent",
            !isOpen ? "hidden pointer-events-none" : "p-2"
          )}
        >
          {children}
        </div>

        {/* Mobile close button with transition */}
        {isMobile && (
          <button
            onClick={() => setOpen(type, false)}
            className={cn(
              "md:hidden w-10 h-full transition-all duration-[3000ms]",
              isOpen
                ? "opacity-100 backdrop-blur-sm bg-black/10"
                : "opacity-0 pointer-events-none"
            )}
          />
        )}
      </nav>
    );
  }
);

SideBar.displayName = "SideBar";

export default SideBar;
