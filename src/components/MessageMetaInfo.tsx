"use client";

import React from "react";

interface MessageMetaInfoProps {
  timestamp: Date;
  content: string;
  labels: string[];
}

const MessageMetaInfo: React.FC<MessageMetaInfoProps> = ({
  timestamp,
  content,
  labels,
}) => {
  const charCount = content.length;
  const wordCount = content
    .split(" ")
    .filter((word) => word.trim() !== "").length; // Hitung jumlah kata
  const timeCount = timestamp.toLocaleTimeString(undefined, {
    hour: "numeric",
    minute: "numeric",
  });

  return (
    <>
      <p className="font-medium text-[#362167] text-xs max-w-max ">
        {timeCount}
      </p>
      <p className="font-medium text-[#362167] text-xs max-w-max ">
        {charCount} {charCount > 1 ? "chars" : "char"}
      </p>
      <p className="font-medium text-[#362167] text-xs max-w-max ">
        {wordCount} {wordCount > 1 ? "words" : "word"}
      </p>
      {labels.map((label) => (
        <p
          key={label}
          className="font-medium text-white text-xs max-w-max bg-[var(--bg-custom-purple-700)] rounded px-2"
        >
          {label}
        </p>
      ))}
    </>
  );
};

export default MessageMetaInfo;
