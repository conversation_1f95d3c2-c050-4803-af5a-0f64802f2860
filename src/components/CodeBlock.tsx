import React, { useEffect, useState } from "react";
import SyntaxHighlighter from "react-syntax-highlighter";
// import { darcula } from "react-syntax-highlighter/dist/cjs/styles/hljs";

interface CodeBlockProps {
  language: string;
  code: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ language, code }) => {
  const [copySuccess, setCopySuccess] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 1500);
  };

  const [style, setStyle] = useState({});
  useEffect(() => {
    import("react-syntax-highlighter/dist/esm/styles/prism/material-dark").then(
      (mod) => setStyle(mod.default)
    );
  }, []);

  return (
    <div className="bg-[var(--bg-custom-gray-800)] rounded-md my-2 overflow-hidden">
      <div className="flex justify-between items-center border-b p-1 px-2 bg-[var(--bg-custom-gray-900)]">
        <div className="text-white">{language}</div>
        <button
          className="p-1 px-2 text-white border-none cursor-pointer"
          onClick={copyToClipboard}
        >
          {copySuccess ? "Copied!" : "Copy"}
        </button>
      </div>
      <div className="overflow-x-auto">
        <SyntaxHighlighter
          language={language}
          style={style}
          showLineNumbers={true}
          wrapLongLines={true}
          wrapLines={true}
          lineProps={(lineNumber: number) => ({
            style: { display: "block", cursor: "pointer" },
            onClick() {
              alert(`Line Number Clicked: ${lineNumber}`);
            },
          })}
        >
          {code}
        </SyntaxHighlighter>
      </div>
    </div>
  );
};

export default CodeBlock;
