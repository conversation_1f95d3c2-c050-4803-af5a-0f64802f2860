import React from "react";
import { Prism as Syntax<PERSON><PERSON>light<PERSON> } from "react-syntax-highlighter";
import { coy } from "react-syntax-highlighter/dist/esm/styles/prism";

export const CodeViewer: React.FC<{ code: string }> = ({ code }) => {
  return (
    <SyntaxHighlighter
      language="typescript"
      style={coy}
      showLineNumbers={false}
    >
      {code}
    </SyntaxHighlighter>
  );
};
