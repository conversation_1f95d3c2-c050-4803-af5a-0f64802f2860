import { usePopup } from "@/context/usePopUp";
import Link from "next/link";
import React from "react";
import TextInfo from "./TextInfo";
import { PopupCloseButton } from "./Popup";

interface WelcomeBannerProps {
  imageSrc: string;
  title: string;
  description: string;
  gradientDirection?: "horizontal" | "vertical";
}

const WelcomeBanner: React.FC<WelcomeBannerProps> = ({
  imageSrc,
  title,
  description,
  gradientDirection,
}) => {
  const gradientClass =
    gradientDirection === "horizontal"
      ? "bg-gradient-to-r from-transparent to-white"
      : "bg-gradient-to-b from-transparent to-white";

  return (
    <div
      id="welcome-banner"
      className="relative w-1/2 h-1/2 rounded-lg bg-white overflow-clip flex flex-col"
    >
      <div className="relative w-full max-h-full flex justify-end bg-black items-center">
        <h1 className="text-white flex justify-center flex-grow text-5xl">
          <Link href={"/"} className="flex flex-col space-y-4">
            <span className="font-bold">賢 Tomio AI</span>
            <TextInfo className="text-2xl">Preview</TextInfo>
          </Link>
        </h1>
        <img
          src={imageSrc}
          alt="welcome banner"
          className="object-contain h-full"
        />
        <div className={`absolute inset-0`}>
          <PopupCloseButton />
        </div>
      </div>
      <div className="bg-white p-4 text-black">Azincourt</div>
    </div>
  );
};

export default WelcomeBanner;
