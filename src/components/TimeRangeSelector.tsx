import React, { useState, useEffect, useRef } from "react";
import { Calendar } from "./Calendar";

interface TimeRangeSelectorProps {
  onSelectRange: (range: { startDate: Date; endDate: Date }) => void;
}

export const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({
  onSelectRange,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showCustomPicker, setShowCustomPicker] = useState(false);
  const [selectedRangeLabel, setSelectedRangeLabel] =
    useState("Select Time Range");
  const dropdownRef = useRef<HTMLDivElement>(null);

  const timePresets = [
    {
      label: "1 Week",
      duration: (endDate: Date) => {
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        return startDate;
      },
    },
    {
      label: "1 Month",
      duration: (endDate: Date) => {
        const startDate = new Date(endDate);
        startDate.setMonth(startDate.getMonth() - 1);
        return startDate;
      },
    },
    {
      label: "3 Months",
      duration: (endDate: Date) => {
        const startDate = new Date(endDate);
        startDate.setMonth(startDate.getMonth() - 3);
        return startDate;
      },
    },
    {
      label: "1 Year",
      duration: (endDate: Date) => {
        const startDate = new Date(endDate);
        startDate.setFullYear(startDate.getFullYear() - 1);
        return startDate;
      },
    },
  ];

  const handlePresetChange = (preset: string) => {
    const endDate = new Date();
    const selectedPreset = timePresets.find((item) => item.label === preset);

    if (selectedPreset) {
      const startDate = selectedPreset.duration(endDate);
      setSelectedRangeLabel(`Last ${preset}`);
      onSelectRange({ startDate, endDate });
    }

    setShowCustomPicker(false);
    setShowDropdown(false);
  };

  const handleCustomRangeSelect = (range: {
    startDate: Date;
    endDate: Date;
  }) => {
    const startDate = range.startDate;
    const endDate = range.endDate;
    setSelectedRangeLabel(
      `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
    );
    onSelectRange(range);
    setShowCustomPicker(false);
    setShowDropdown(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="time-range-selector relative" ref={dropdownRef}>
      <button
        className="p-2 border rounded"
        onClick={() => {
          setShowDropdown(!showDropdown);
          setShowCustomPicker(false);
        }}
      >
        {selectedRangeLabel}
      </button>
      {showDropdown && (
        <ul className="absolute mt-2 p-2 border rounded bg-white shadow-lg z-10 w-40">
          {timePresets.map((preset) => (
            <li key={preset.label}>
              <button
                className="p-2 w-full text-left border-b"
                onClick={() => handlePresetChange(preset.label)}
              >
                {preset.label}
              </button>
            </li>
          ))}
          <li className="relative group/custom-date">
            <button
              className="p-2 w-full text-left"
              onClick={() => handlePresetChange("Custom")}
            >
              Custom Range
            </button>

            <div className="hidden group-hover/custom-date:block absolute left-full -top-32 z-30">
              <Calendar />
            </div>
          </li>
        </ul>
      )}
    </div>
  );
};
