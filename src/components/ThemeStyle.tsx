import React from "react";
import {
  AppearanceColorAdvanced,
  AppearanceColorSetting,
  AppearanceSetting,
} from "@/model/appSetting";
import Head from "next/head";
import { buildCssVarsShadcn, buildVariables } from "@/lib/css-builder";

export const k_THEME_WRAPPER_STYLE_ID = "theme_variable_style";

type ThemeStyleProps = {
  appearance: AppearanceSetting;
};

export function ThemeStyle({ appearance }: ThemeStyleProps) {
  const lightVars = {
    ...buildCssVarsShadcn(false, appearance.advanced.light, appearance),
    ...buildVariables(false, appearance),
  };

  const darkVars = {
    ...buildCssVarsShadcn(true, appearance.advanced.dark, appearance),

    ...buildVariables(true, appearance),
  };

  const lightCss = buildCssStringFromVars(lightVars);
  const darkCss = buildCssStringFromVars(darkVars);

  return (
    <style id={k_THEME_WRAPPER_STYLE_ID}>
      {`
    :root {
      ${lightCss}
    }
    .dark {
      ${darkCss}
    }
    `}
    </style>
  );
}

export function serverBuildCss({
  theme,
  appearance,
}: {
  theme: "light" | "dark" | "system";
  appearance: AppearanceSetting;
}) {
  const lightVars = {
    ...buildCssVarsShadcn(false, appearance.advanced.light, appearance),
    ...buildVariables(false, appearance),
  };

  const darkVars = {
    ...buildCssVarsShadcn(true, appearance.advanced.dark, appearance),
    ...buildVariables(true, appearance),
  };

  const lightCss = buildCssStringFromVars(lightVars);
  const darkCss = buildCssStringFromVars(darkVars);
  return { lightCss, darkCss };
}

function buildCssStringFromVars(
  vars: Record<string, string | number | undefined>
): string {
  return Object.entries(vars)
    .filter(([, v]) => v !== undefined)
    .map(([key, value]) => `${key}: ${value};`)
    .join("\n");
}

// Optional: dynamically update styles on the client (after hydration)
export function applyShadcnVariablesClient(
  theme: string,
  colorLight: AppearanceColorAdvanced,
  colorDark: AppearanceColorAdvanced,
  appearance: AppearanceSetting
): void {
  const isDark = document.documentElement.classList.contains("dark");

  // Dynamically update the <style> tag with the correct variables
  updateThemeStyleTag({
    lightVars: buildCssVarsShadcn(false, colorLight, appearance),
    darkVars: buildCssVarsShadcn(true, colorDark, appearance),
  });
}

export function applyCssVariablesClient(
  theme: string,
  colorLight: AppearanceColorSetting,
  colorDark: AppearanceColorSetting,
  appearance: AppearanceSetting
): void {
  const isDark = document.documentElement.classList.contains("dark");
  const vars = buildVariables(isDark, appearance);

  // Dynamically update the <style> tag with the correct variables
  updateThemeStyleTag({
    lightVars: buildVariables(false, appearance),
    darkVars: buildVariables(true, appearance),
  });
}

function updateThemeStyleTag({
  lightVars,
  darkVars,
}: {
  lightVars: Record<string, string | number | undefined>;
  darkVars: Record<string, string | number | undefined>;
}) {
  const root = document.documentElement;
  const darkEl = document.querySelector(".dark");

  for (const [key, value] of Object.entries(lightVars)) {
    if (value != null) {
      root.style.setProperty(key, value.toString());
    } else {
      root.style.removeProperty(key);
    }
  }

  if (darkEl) {
    for (const [key, value] of Object.entries(darkVars)) {
      if (value != null) {
        (darkEl as HTMLElement).style.setProperty(key, value.toString());
      } else {
        (darkEl as HTMLElement).style.removeProperty(key);
      }
    }
  }
}

export function resetStyleLocal() {
  const root = document.documentElement;
  const darkEl = document.querySelector(".dark");

  // Remove all inline styles from root
  root.removeAttribute("style");

  // Optionally, remove styles from dark element if it exists
  if (darkEl) {
    (darkEl as HTMLElement).removeAttribute("style");
  }
}
