import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  <PERSON>,
  Line,
  Radar,
  Doughnut,
  PolarArea,
  Scatter,
  Bubble,
} from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler
);

type ChartType =
  | "Bar"
  | "Pie"
  | "Line"
  | "Radar"
  | "Doughnut"
  | "PolarArea"
  | "Scatter"
  | "Bubble";

interface ChartDataProps {
  chartType: ChartType;
  label: string;
  data: any;
}

export const ChartView: React.FC<ChartDataProps> = ({
  data,
  label,
  chartType: defaultChartType,
}) => {
  const [chartType, setChartType] = useState<ChartType>(() => defaultChartType);
  const [parentWidth, setParentWidth] = useState(0);
  const [parentHeight, setParentHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const savedChartType = localStorage.getItem(
      `chartType-${label}`
    ) as ChartType;
    if (savedChartType) {
      setChartType(savedChartType);
    }
  }, [label]);

  useEffect(() => {
    localStorage.setItem(`chartType-${label}`, chartType);
  }, [chartType, label]);

  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        setParentWidth(containerRef.current.clientWidth);
        setParentHeight(containerRef.current.clientHeight);
      }
    };

    updateSize();

    const resizeObserver = new ResizeObserver(() => {
      updateSize();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);

  const options = {
    responsive: true,
    maintainAspectRatio: false, // Disable to allow custom height
  };

  const renderChart = () => {
    switch (chartType) {
      case "Bar":
        return (
          <Bar
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "Pie":
        return (
          <Pie
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "Line":
        return (
          <Line
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "Radar":
        return (
          <Radar
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "Doughnut":
        return (
          <Doughnut
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "PolarArea":
        return (
          <PolarArea
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "Scatter":
        return (
          <Scatter
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      case "Bubble":
        return (
          <Bubble
            data={data}
            options={options}
            width={parentWidth}
            height={parentHeight}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full flex flex-col items-center h-full">
      <div className="w-full flex justify-start h-10 items-center">
        <label htmlFor="chartType" className="mr-2">
          Select Chart Type:
        </label>
        <select
          id="chartType"
          value={chartType}
          onChange={(e) => setChartType(e.target.value as ChartType)}
        >
          <option value="Bar">Bar</option>
          <option value="Pie">Pie</option>
          <option value="Line">Line</option>
          <option value="Radar">Radar</option>
          <option value="Doughnut">Doughnut</option>
          <option value="PolarArea">PolarArea</option>
          <option value="Scatter">Scatter</option>
          <option value="Bubble">Bubble</option>
        </select>
      </div>
      <div
        ref={containerRef}
        className="flex items-center justify-center w-full h-[450px] overflow-auto"
      >
        {renderChart()}
      </div>
    </div>
  );
};
