import useJoinFeature from "@/app/hooks/useJoinFeature";
import { usePopup } from "@/context/usePopUp";
import Analytics from "@/services/analytics";
import { getUserEmailOrPopUp } from "@/services/getUserEmailOrPopUp";
import { useSearchParams } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";

interface PreviewPopupProps {}

export const PreviewPopup: React.FC<PreviewPopupProps> = () => {
  const [email, setEmail] = useState<string>("");
  const [isPopupVisible, setIsPopupVisible] = useState<boolean>(false);
  const searchParams = useSearchParams();
  const { joinFeature } = useJoinFeature();

  useEffect(() => {
    const isPopupAlreadyShown = localStorage.getItem("isPreviewPopupShown");

    if (!searchParams) {
      return;
    }
    const ref = searchParams.get("search");
    if (ref) {
      localStorage.setItem("ref", ref as string);
    }

    if (isPopupAlreadyShown !== "true") {
      Analytics.getInstance().track("Preview PopupOverlay Displayed", {
        ref: localStorage.getItem("ref"),
      });
      setIsPopupVisible(true);
    }
  }, []);

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleSubmit = async () => {
    try {
      localStorage.setItem("isPreviewPopupShown", "true");
      localStorage.setItem("userEmail", email);
      setIsPopupVisible(false);
      const referal = await joinFeature(email, "early-access");
      localStorage.setItem("referal_join", referal);
    } catch (err) {}
  };

  if (!isPopupVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-[var(--bg-custom-gray-700)] bg-opacity-50">
      <div className="bg-white p-6 rounded-lg shadow-md w-[500px]">
        <h2 className=" font-semibold mb-4">Welcome to the Preview Version!</h2>
        <p className="text-[var(--text-custom-gray-600)] mb-4">
          Feel free to use our unlimited API key. Help us by finding bugs and
          errors and reporting them to the developer.
        </p>
        <p className="text-[var(--text-custom-gray-600)] mb-4">
          Enjoy!! You can join the others to get early free access too 🤑
        </p>
        <input
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={handleEmailChange}
          className="w-full p-2 border border-[var(--border-custom-gray-300)] rounded mb-4"
        />
        <button
          onClick={() => {
            handleSubmit();
            Analytics.getInstance().track("Preview PopupOverlay Submit Email", {
              button_title: "Okay! I'm in ☝️ I like savings",
            });
          }}
          className="inline-flex w-full justify-center rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[var(--bg-custom-indigo-500)] sm:ml-3 sm:w-auto"
        >
          {"Okay! I'm in ☝️ I like savings"}
        </button>
        <button
          onClick={() => {
            setIsPopupVisible(false);
            Analytics.getInstance().track("Preview PopupOverlay Close", {
              button_title: "Awesome. I'll try first 😎",
            });
          }}
          className="bg-[var(--bg-custom-white-500)] text-[var(--text-custom-red-500)] inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibol shadow-sm sm:ml-3 sm:w-auto"
        >
          {"Awesome. I'll try first 😎"}
        </button>
      </div>
    </div>
  );
};
