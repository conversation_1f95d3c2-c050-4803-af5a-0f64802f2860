import { usePopup } from "@/context/usePopUp";
import { PopUp } from "@/model/popup";
import Analytics from "@/services/analytics";
import React, { useEffect } from "react";
import { Dialog } from "../Dialog";

interface PopupProps {}

export const PopupOverlay: React.FC<PopupProps> = ({}) => {
  const { popUp, closePopup } = usePopup();

  useEffect(() => {
    if (!popUp) {
      return;
    }
    popUp.sendAnalytics();
  }, [popUp]);

  if (!popUp) {
    return null;
  }

  return <Dialog>{popUp.render()}</Dialog>;
};

export const PopupCloseButton: React.FC = () => {
  const { closePopup } = usePopup();
  return (
    <button
      onClick={closePopup}
      className="absolute z-50 top-4 right-4 p-1 rounded-full bg-[var(--bg-custom-red-500)] text-white hover:bg-[var(--bg-custom-red-700)] cursor-pointer"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </button>
  );
};
