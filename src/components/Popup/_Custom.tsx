import React from "react";
import { usePopup } from "@/context/usePopUp";
import { PopUp } from "@/model/popup";
import Analytics from "@/services/analytics";

export class CustomPopUp implements PopUp {
  context: string;
  view: React.ReactNode;

  constructor({ context, view }: { context: string; view: React.ReactNode }) {
    this.context = context;
    this.view = view;
  }

  render(): React.ReactNode {
    return this.view;
  }

  sendAnalytics() {
    Analytics.getInstance().track("Show Popup", {
      context: this.context,
    });
  }
}
