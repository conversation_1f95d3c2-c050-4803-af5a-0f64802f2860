import { usePopup } from "@/context/usePopUp";
import { PopUp } from "@/model/popup";
import Analytics from "@/services/analytics";

export interface GeneralPopUpButton {
  title: string;
  isPrimary: boolean;
  onClick: () => void;
}

export class GeneralPopUp implements PopUp {
  context: string;
  title: string;
  content: string;
  buttons: GeneralPopUpButton[];

  constructor({
    context,
    title,
    content,
    buttons,
  }: {
    context: string;
    title: string;
    content: string;
    buttons: GeneralPopUpButton[];
  }) {
    this.context = context;
    this.title = title;
    this.content = content;
    this.buttons = buttons;
  }

  render(): React.ReactNode {
    return <GeneralPopUpView data={this} />;
  }

  sendAnalytics() {
    Analytics.getInstance().track("Show PopupOverlay", {
      context: this.context,
      title: this.title,
      content: this.content,
    });
  }
}

const GeneralPopUpView: React.FC<{ data: GeneralPopUp }> = ({ data }) => {
  const { closePopup } = usePopup();

  return (
    <div className="flex min-h-screen items-center justify-center p-4 text-center sm:items-center sm:p-0">
      <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-xl">
        <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
          <div className="sm:flex sm:items-start">
            <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-[var(--bg-custom-indigo-100)] sm:mx-0 sm:h-10 sm:w-10">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="indigo"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="10" />
                <line x1="12" y1="8" x2="12" y2="12" />
                <line x1="12" y1="16" x2="12" y2="16" />
              </svg>
            </div>
            <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3
                className="text-base font-semibold leading-6 text-[var(--text-custom-gray-900)]"
                id="modal-title"
              >
                {data.title}
              </h3>
              <div className="mt-2">
                <span className="text-sm text-[var(--text-custom-gray-500)]">
                  {data.content}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="px-4 py-3 flex flex-row-reverse space-x-2 gap-4 sm:gap-0">
          {data.buttons.map((btn, index) => {
            if (btn.isPrimary) {
              return (
                <button
                  key={index}
                  onClick={() => {
                    btn.onClick();
                    closePopup();
                  }}
                  type="button"
                  className="inline-flex w-full justify-center rounded-md bg-[var(--bg-custom-indigo-600)] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[var(--bg-custom-indigo-500)] sm:ml-3 sm:w-auto"
                >
                  {btn.title}
                </button>
              );
            } else {
              return (
                <button
                  key={index}
                  onClick={() => {
                    btn.onClick();
                    closePopup();
                  }}
                  type="button"
                  className="inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-[var(--text-custom-gray-900)] shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                >
                  {btn.title}
                </button>
              );
            }
          })}
        </div>
      </div>
    </div>
  );
};
