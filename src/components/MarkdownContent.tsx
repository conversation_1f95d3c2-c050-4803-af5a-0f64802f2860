"use client";

import MarkdownRenderer from "./markdown-renderer";

interface MarkdownContentProps {
  content: string;
}

const MarkdownContent: React.FC<MarkdownContentProps> = ({ content }) => {
  return <MarkdownRenderer content={content} isChatMessage />;
};

export default MarkdownContent;

// import React, { useMemo, useState } from "react";
// import ReactMarkdown from "react-markdown";
// import rehypeRaw from "rehype-raw";
// import remarkMath from "remark-math";
// import remarkGfm from "remark-gfm";
// import remarkToc from "remark-toc";
// import rehypeKatex from "rehype-katex";
// import SyntaxHighlighter from "react-syntax-highlighter";
// import { a11yDark } from "react-syntax-highlighter/dist/cjs/styles/hljs";
// import { twMerge } from "tailwind-merge";
// import "katex/dist/katex.min.css";
// import { PluggableList } from "react-markdown/lib/react-markdown";

// interface MarkdownContentProps {
//   content: string;
// }

// const MarkdownContent: React.FC<MarkdownContentProps> = ({
//   content: originalContent,
// }) => {
//   const [gfm, setGfm] = useState(true);
//   const [raw, setRaw] = useState(true);
//   const [copySuccessId, setCopySuccessId] = useState("");

//   const copyToClipboard = (id: string, code: string) => {
//     navigator.clipboard.writeText(code);
//     setCopySuccessId(id);
//     setTimeout(() => setCopySuccessId(""), 1500);
//   };

//   const rehypePlugins: PluggableList = [
//     ...(raw ? [rehypeRaw, rehypeKatex] : []),
//     rehypeKatex as any,
//   ];
//   const remarkPlugins: PluggableList = [
//     remarkToc,
//     remarkMath,
//     ...(gfm ? [remarkGfm] : []),
//   ];

//   function replaceForKatex(originalContent: String) {
//     const result = originalContent
//       .replaceAll("\\(", "$")
//       .replaceAll("\\)", "$")
//       .replaceAll("\\[", "$")
//       .replaceAll("\\]", "$");
//     return result;
//   }

//   const content = useMemo(
//     () => replaceForKatex(originalContent),
//     [originalContent]
//   );

//   const isRTL = (text: string) => {
//     const rtlChars = /[\u0591-\u07FF\uFB1D-\uFDFD\uFE70-\uFEFC]/;
//     return rtlChars.test(text);
//   };

//   const getTailwindClasses = (text: string) => {
//     return isRTL(text) ? "rtl text-right" : "ltr text-left";
//   };

//   return (
//     <div className="markdown-content">
//       <div className="relative result">
//         <ReactMarkdown
//           className={twMerge(
//             "markdown-body prose max-w-full",
//             getTailwindClasses(content)
//           )}
//           remarkPlugins={remarkPlugins}
//           rehypePlugins={rehypePlugins}
//           components={{
//             h1: (props) => (
//               <h1 className="font-bold text-2xl py-2" {...props} />
//             ),
//             h2: (props) => <h2 className="font-bold py-2" {...props} />,
//             h3: (props) => <h3 className="font-semibold py-2" {...props} />,
//             h4: (props) => (
//               <h4 className="font-semibold text-base py-2" {...props} />
//             ),
//             h5: (props) => (
//               <h5 className="font-medium text-sm py-2" {...props} />
//             ),
//             h6: (props) => (
//               <h6 className="font-medium text-xs py-2" {...props} />
//             ),
//             hr: (props) => <hr className="py-2" {...props} />,
//             ul: (props) => (
//               <ul
//                 className="py-2"
//                 style={{
//                   display: "block",
//                   listStyleType: "disc",
//                   paddingInlineStart: "40px",
//                 }}
//                 {...props}
//               />
//             ),
//             ol: (props) => (
//               <ul
//                 className="py-2"
//                 style={{
//                   display: "block",
//                   listStyleType: "decimal",
//                   paddingInlineStart: "40px",
//                 }}
//                 {...props}
//               />
//             ),
//             a: (props) => <a style={{ color: "red" }} {...props} />,
//             link: (props) => (
//               <link
//                 className="py-2"
//                 style={{
//                   color: "red",
//                   fontWeight: "bold",
//                   fontSize: "1.24rem",
//                 }}
//                 {...props}
//               />
//             ),
//             p: (props) => <p className="py-2" {...props} />,
//             code: ({ inline, className, children, ...props }) => {
//               const match = /language-(\w+)/.exec(className || "");
//               const identifier = children.toString();
//               const codeContent = children.toString();
//               return !inline && match ? (
//                 <div className="bg-[var(--bg-custom-gray-800)] rounded-md overflow-hidden">
//                   <div className="flex justify-between items-center border-b p-1 px-2">
//                     <div className="text-white">{match[1]}</div>
//                     <button
//                       className="p-1 px-2 text-white border-none cursor-pointer bg-[var(--bg-custom-purple-500)]"
//                       onClick={() => copyToClipboard(identifier, codeContent)}
//                     >
//                       {copySuccessId === identifier ? "Copied!" : "Copy"}
//                     </button>
//                   </div>
//                   <div className="overflow-x-auto">
//                     <SyntaxHighlighter
//                       {...props}
//                       style={a11yDark}
//                       language={match[1]}
//                       wrapLongLines
//                       wrapLines
//                     >
//                       {codeContent}
//                     </SyntaxHighlighter>
//                   </div>
//                 </div>
//               ) : (
//                 <code
//                   className={twMerge(
//                     "text-[var(--text-custom-purple-600)] pl-1 pr-1 border rounded bg-[var(--bg-custom-gray-200)]",
//                     className
//                   )}
//                   {...props}
//                 >
//                   {children}
//                 </code>
//               );
//             },
//           }}
//         >
//           {content}
//         </ReactMarkdown>
//       </div>
//     </div>
//   );
// };
