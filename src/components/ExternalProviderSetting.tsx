import React, { useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import { ExternalProvider } from "@/model/externalProvider";
import CodeEditor from "./CodeEditor";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface ExternalProviderSettingsProps {
  currentProvider?: { isCustom: boolean; provider: ExternalProvider };
  presetProviders: ExternalProvider[];
  savedCustomProviders: ExternalProvider[];
  onSave: (provider: ExternalProvider) => void;
  onRemove: (id: string) => void;
  onProviderChange: (isCustom: boolean, provider: ExternalProvider) => void;
}

type CustomOrPreset = "Custom" | "Preset";

export const ExternalProviderSettings: React.FC<
  ExternalProviderSettingsProps
> = ({
  currentProvider,
  presetProviders,
  savedCustomProviders,
  onSave,
  onRemove,
  onProviderChange,
}) => {
  const [providerType, setProviderType] = useState<CustomOrPreset>("Preset");
  const [id, setId] = useState("");
  const [name, setName] = useState("");
  const [endpoint, setEndpoint] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [payloadScript, setPayloadScript] = useState("");

  useEffect(() => {
    if (currentProvider) {
      const { isCustom, provider } = currentProvider;
      setProviderType(isCustom ? "Custom" : "Preset");
      updateData(provider);
    }
  }, [currentProvider]);

  function updateData(provider: ExternalProvider) {
    setId(provider.id);
    setName(provider.name);
    setEndpoint(provider.endpoint);
    setApiKey(provider.apiKey);
    setPayloadScript(provider.payloadScript);
  }

  const handleProviderTypeChange = (value: CustomOrPreset) => {
    setProviderType(value);
    if (value === "Preset" && presetProviders.length > 0) {
      updateData(presetProviders[0]);
    } else {
      resetFields();
    }
  };

  const handleProviderChange = (selectedId: string) => {
    const selected = [...presetProviders, ...savedCustomProviders].find(
      (p) => p.id === selectedId
    );
    if (selected) {
      updateData(selected);
      onProviderChange(providerType === "Custom", selected);
    } else {
      resetFields();
    }
  };

  function resetFields() {
    setId("");
    setName("");
    setEndpoint("");
    setApiKey("");
    setPayloadScript("");
  }

  const isValid = () => {
    const urlPattern = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
    return (
      name.trim() !== "" &&
      urlPattern.test(endpoint.trim()) &&
      apiKey.trim() !== "" &&
      payloadScript.trim() !== ""
    );
  };

  const handleSaveCustomProvider = () => {
    const newProvider: ExternalProvider = {
      id: id || uuidv4(),
      name,
      endpoint,
      apiKey,
      payloadScript,
    };
    updateData(newProvider);
    onSave(newProvider);
  };

  const handleRemoveCustomProvider = () => {
    if (id) {
      onRemove(id);
      resetFields();
      setProviderType("Custom");
    }
  };

  return (
    <Card className="p-4 space-y-6 w-full">
      <div className="space-y-2">
        <Label>Provider Type</Label>
        <Select
          value={providerType}
          onValueChange={(val) =>
            handleProviderTypeChange(val as CustomOrPreset)
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Select provider type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Preset">Preset</SelectItem>
            <SelectItem value="Custom">Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {providerType === "Preset" && (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Preset Provider</Label>
            <Select value={id} onValueChange={handleProviderChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a provider" />
              </SelectTrigger>
              <SelectContent>
                {presetProviders.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>API Key</Label>
            <Input
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter API key"
            />
          </div>
        </div>
      )}

      {providerType === "Custom" && (
        <Card className="p-4 space-y-4 border border-[var(--border-custom-purple-500)]">
          <div className="space-y-2">
            <Label>Saved Custom Providers</Label>
            <Select value={id} onValueChange={handleProviderChange}>
              <SelectTrigger>
                <SelectValue placeholder="Create a new provider or select existing" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Create New</SelectItem>
                {savedCustomProviders.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Custom Provider Name</Label>
            <Input value={name} onChange={(e) => setName(e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label>Endpoint</Label>
            <Input
              value={endpoint}
              onChange={(e) => setEndpoint(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label>API Key</Label>
            <Input value={apiKey} onChange={(e) => setApiKey(e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label>Payload Script</Label>
            <CodeEditor value={payloadScript} onChange={setPayloadScript} />
          </div>

          <div className="flex gap-2">
            <Button onClick={handleSaveCustomProvider} disabled={!isValid()}>
              {id ? "Update Custom Provider" : "Save Custom Provider"}
            </Button>
            {id && (
              <Button
                variant="destructive"
                onClick={handleRemoveCustomProvider}
              >
                Remove Custom Provider
              </Button>
            )}
          </div>
        </Card>
      )}
    </Card>
  );
};
