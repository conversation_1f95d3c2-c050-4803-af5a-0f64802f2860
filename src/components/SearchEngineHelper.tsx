import React from "react";
import Image, { StaticImageData } from "next/image";
import google from "../../public/google.png";

interface SearchItem {
  text: string;
  icon: StaticImageData;
  link: string;
}

interface SearchEngineHelperProps {
  role: string;
  moreContent: boolean;
  searchEngineQueries: string[];
}

const SearchEngineHelper: React.FC<SearchEngineHelperProps> = ({
  role,
  moreContent,
  searchEngineQueries,
}) => {
  if (!(moreContent && role === "assistant")) {
    return null;
  }

  const searchItems: SearchItem[] = searchEngineQueries.map((searchItem) => ({
    text: searchItem,
    icon: google,
    link: "https://www.google.com",
  }));

  return (
    <div className="w-full flex gap-2 mt-3 duration-300 ease-in-out">
      {searchItems.map((item, id) => (
        <a
          href={item.link}
          target="_blank"
          rel="noopener noreferrer"
          className={`flex items-center gap-2 border rounded-2xl p-1 px-3 ${
            role !== "assistant" ? "bg-[#E2DCF1]" : "bg-white"
          }`}
          key={id}
        >
          <Image src={item.icon} alt="" className="w-3 h-3" />
          <p className="flex items-center text-[10px]">
            {item.text} <span className="ml-1">↗</span>
          </p>
        </a>
      ))}
    </div>
  );
};

export default SearchEngineHelper;
