import { useState, ReactNode, useMemo, useCallback } from "react";
import { Icon } from "./Icon";
import { twMerge } from "tailwind-merge";

export interface Tab {
  id: string;
  title: string;
  content: ReactNode;
}

interface TabPanelProps {
  tag: string;
  current: string;
  children: ReactNode;
  inactiveMode: "hidden" | "remove";
}

const TabPanel: React.FC<TabPanelProps> = ({
  tag,
  current,
  children,
  inactiveMode,
}) => {
  if (inactiveMode == "remove") {
    if (current !== tag) {
      return null;
    }
  }
  return (
    <div
      className={twMerge(
        "w-full h-full",
        inactiveMode == "hidden" && current !== tag && "hidden"
      )}
    >
      {children}
    </div>
  );
};

interface TabViewProps {
  title?: string;
  tabs: Tab[];
  onClose?: (id: string) => void;
  initialTab?: string;
  config?: {
    tab?: {
      inactive?: {
        class: string;
      };
    };
  };
  inactiveMode?: "hidden" | "remove";
}

export const TabView: React.FC<TabViewProps> = ({
  title,
  tabs,
  initialTab,
  onClose,
  config,
  inactiveMode,
}) => {
  const [currentTab, setCurrentTab] = useState(initialTab || tabs[0]?.id || "");

  const memoizedTabs = useMemo(() => tabs, [tabs]);

  const handleTabClose = useCallback(
    (tabId: string) => {
      if (onClose) {
        onClose(tabId);
      }

      const tabIndex = memoizedTabs.findIndex((tab) => tab.id === tabId);

      if (tabIndex === -1) return; // Tab not found

      const previousTab = memoizedTabs[tabIndex - 1]?.id;
      const nextTab = memoizedTabs[tabIndex + 1]?.id;

      if (nextTab) {
        setCurrentTab(nextTab);
      } else if (previousTab) {
        setCurrentTab(previousTab);
      } else {
        setCurrentTab(memoizedTabs[0]?.id || "");
      }
    },
    [memoizedTabs, onClose]
  );

  return (
    <div className="flex flex-col text-white w-full relative h-full">
      <div className="sticky top-0 flex overflow-x-auto no-scrollbar z-10 ml-2 min-h-8">
        {title && <h3 className="p-2 flex items-center">{title}</h3>}
        {memoizedTabs.map((tab) => (
          <div
            key={tab.id}
            className={twMerge(
              "flex group space-x-1 px-2",
              currentTab === tab.id
                ? "rounded-t bg-[var(--bg-custom-gray-100)] text-black"
                : twMerge(
                    "bg-transparent mt-1 text-black",
                    config?.tab?.inactive?.class
                  )
            )}
          >
            <button
              className="px-2 py-1 text-nowrap w-full"
              onClick={() => setCurrentTab(tab.id)}
            >
              {tab.title}
            </button>
            {onClose && (
              <button onClick={() => handleTabClose(tab.id)}>
                <Icon iconName="Close" />
              </button>
            )}
          </div>
        ))}
      </div>
      <div className="relative w-full  p-2 border-t border-[var(--border-custom-gray-400)] h-full text-black">
        {memoizedTabs.map((tab) => (
          <TabPanel
            key={tab.id}
            current={currentTab}
            tag={tab.id}
            inactiveMode={inactiveMode || "hidden"}
          >
            {tab.content}
          </TabPanel>
        ))}
      </div>
    </div>
  );
};
