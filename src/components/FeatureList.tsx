import React from 'react';

export interface Feature {
  icon: React.ReactNode;
  id: number;
  text: string;
}

const FeatureList: React.FC<{ features: Feature[] }> = ({ features }) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      {features.map(feature => (
        <div key={feature.id} className="flex items-start space-x-2">
          {feature.icon}
          <span>{feature.text}</span>
        </div>
      ))}
    </div>
  );
};

export default FeatureList;