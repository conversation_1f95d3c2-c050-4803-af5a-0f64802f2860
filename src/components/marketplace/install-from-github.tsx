"use client";

import type React from "react";

import { useState } from "react";
import { Github, Download, Loader2, Check, AlertCircle } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export function InstallFromGithub() {
  const [url, setUrl] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");
  const [itemPreview, setItemPreview] = useState<any>(null);

  const handleValidate = (e: React.FormEvent) => {
    e.preventDefault();
    setIsValidating(true);
    setError("");
    setIsSuccess(false);

    // Simulate GitHub URL validation
    setTimeout(() => {
      setIsValidating(false);

      // For demo purposes, show a preview if URL contains "github.com"
      if (url.includes("github.com")) {
        const repoName = url.split("/").pop() || "Repository Name";
        const username = url.split("/")[3] || "username";

        setItemPreview({
          name: repoName
            .replace(/-/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase()),
          author: username,
          description: `A ${
            repoName.includes("tool")
              ? "tool"
              : repoName.includes("prompt")
              ? "prompt collection"
              : "extension"
          } for enhancing LLM capabilities.`,
          type: repoName.includes("tool")
            ? "Tool"
            : repoName.includes("prompt")
            ? "Prompt"
            : "Extension",
          lastUpdated: new Date().toISOString().split("T")[0],
        });
      } else {
        setError(
          "Invalid GitHub URL. Please enter a valid GitHub repository URL."
        );
      }
    }, 1500);
  };

  const handleInstall = () => {
    setIsInstalling(true);

    // Simulate installation
    setTimeout(() => {
      setIsInstalling(false);
      setIsSuccess(true);
      setItemPreview(null);
      setUrl("");
    }, 2000);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Github className="h-5 w-5 mr-2" />
          Install from GitHub
        </CardTitle>
        <CardDescription>
          Directly install tools, prompts, or extensions from GitHub
          repositories
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isSuccess ? (
          <Alert className="bg-green-50 dark:bg-[var(--bg-custom-green-950)]/20 border-[var(--border-custom-green-200)] dark:border-[var(--border-custom-green-900)]">
            <Check className="h-4 w-4 text-[var(--text-custom-green-600)] dark:text-[var(--text-custom-green-400)]" />
            <AlertTitle className="text-[var(--text-custom-green-800)] dark:text-[var(--text-custom-green-300)]">
              Installation Successful
            </AlertTitle>
            <AlertDescription className="text-[var(--text-custom-green-700)] dark:text-[var(--text-custom-green-400)]">
              The item has been successfully installed to your local
              environment.
            </AlertDescription>
          </Alert>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : null}

        <form onSubmit={handleValidate} className="mt-4 space-y-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-grow">
              <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="https://github.com/username/repository"
                className="pl-9"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                disabled={isValidating || isInstalling}
              />
            </div>
            <Button
              type="submit"
              disabled={isValidating || isInstalling || !url.trim()}
              className="whitespace-nowrap"
            >
              {isValidating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Validating...
                </>
              ) : (
                "Validate URL"
              )}
            </Button>
          </div>
        </form>

        {itemPreview && (
          <div className="mt-4 p-4 border rounded-lg">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h3 className="text-lg font-semibold">{itemPreview.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {itemPreview.description}
                </p>
                <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                  <div className="flex items-center">
                    <Github className="h-3 w-3 mr-1" />
                    {itemPreview.author}
                  </div>
                  <div>Type: {itemPreview.type}</div>
                  <div>Updated: {itemPreview.lastUpdated}</div>
                </div>
              </div>
              <Button
                onClick={handleInstall}
                disabled={isInstalling}
                className="whitespace-nowrap"
              >
                {isInstalling ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Installing...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Install Now
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="text-xs text-muted-foreground">
        <p>
          You can also install via CLI:{" "}
          <code className="bg-muted px-1 py-0.5 rounded">
            llm-client install [GitHub URL]
          </code>
        </p>
      </CardFooter>
    </Card>
  );
}
