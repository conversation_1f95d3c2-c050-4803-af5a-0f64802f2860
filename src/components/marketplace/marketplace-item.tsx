import Image from "next/image";
import Link from "next/link";
import { Download, Star, Github } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  <PERSON>Header,
} from "@/components/ui/card";

interface MarketplaceItemProps {
  item: {
    id: string;
    title: string;
    description: string;
    category: string;
    developer: string;
    lastUpdate: string;
    compatibility: string;
    rating: number;
    downloads: number;
    image: string;
    repository?: string;
    githubUrl?: string;
  };
}

export function MarketplaceItem({ item }: MarketplaceItemProps) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        <div className="relative h-48 w-full">
          <Image
            src={item.image || "/placeholder.svg"}
            alt={item.title}
            fill
            className="object-cover"
          />
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <Badge variant="default">{item.category}</Badge>
          <div className="flex items-center">
            <Star className="h-4 w-4 fill-primary text-primary mr-1" />
            <span className="text-sm">{item.rating}</span>
          </div>
        </div>
        <h3 className="font-semibold text-lg mb-1">{item.title}</h3>
        <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
          {item.description}
        </p>
        <div className="text-xs text-muted-foreground">
          <p>By {item.developer}</p>
          <p>Updated: {new Date(item.lastUpdate).toLocaleDateString()}</p>
          <p>Compatibility: {item.compatibility}</p>
          {item.githubUrl && (
            <div className="flex items-center mt-1">
              <Github className="h-3 w-3 mr-1" />
              <span>GitHub Hosted</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="flex items-center text-xs text-muted-foreground">
          <Download className="h-3 w-3 mr-1" />
          {item.downloads.toLocaleString()}
        </div>
        <Link href={`/marketplace/item/${item.id}`}>
          <Button size="sm">View Details</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
