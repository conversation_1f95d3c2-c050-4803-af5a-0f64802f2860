import Image from "next/image";
import Link from "next/link";
import { Download, Star } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface MarketplaceItemListProps {
  item: {
    id: string;
    title: string;
    description: string;
    category: string;
    developer: string;
    lastUpdate: string;
    compatibility: string;
    rating: number;
    downloads: number;
    image: string;
  };
}

export function MarketplaceItemList({ item }: MarketplaceItemListProps) {
  return (
    <div className="flex flex-col sm:flex-row border rounded-lg overflow-hidden">
      <div className="relative h-48 sm:h-auto sm:w-48 shrink-0">
        <Image
          src={item.image || "/placeholder.svg"}
          alt={item.title}
          fill
          className="object-cover"
        />
      </div>
      <div className="flex flex-col p-4 w-full">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
          <div>
            <h3 className="font-semibold text-lg">{item.title}</h3>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>By {item.developer}</span>
              <span>•</span>
              <Badge variant="default">{item.category}</Badge>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <Star className="h-4 w-4 fill-primary text-primary mr-1" />
              <span className="text-sm">{item.rating}</span>
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <Download className="h-3 w-3 mr-1" />
              {item.downloads.toLocaleString()}
            </div>
          </div>
        </div>
        <p className="text-sm text-muted-foreground mb-4 flex-grow">
          {item.description}
        </p>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
          <div className="text-xs text-muted-foreground">
            <p>Updated: {new Date(item.lastUpdate).toLocaleDateString()}</p>
            <p>Compatibility: {item.compatibility}</p>
          </div>
          <Link href={`/marketplace/item/${item.id}`}>
            <Button size="sm">View Details</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
