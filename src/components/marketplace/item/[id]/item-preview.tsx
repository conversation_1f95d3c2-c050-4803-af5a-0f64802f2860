"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";

interface ItemPreviewProps {
  item: {
    id: string;
    title: string;
    category: string;
  };
}

export function ItemPreview({ item }: ItemPreviewProps) {
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = () => {
    setIsLoading(true);

    // Simulate processing time
    setTimeout(() => {
      // Generate a demo output based on the item category and input
      let result = "";

      if (item.category === "Tools") {
        result = `Analysis of your input:\n\n${input}\n\nKey points:\n- This appears to be about ${
          input.split(" ").length > 5
            ? input.split(" ").slice(0, 3).join(" ")
            : "the topic"
        }\n- Contains ${input.length} characters\n- Sentiment: ${
          Math.random() > 0.5 ? "Positive" : "Neutral"
        }\n\nRecommended actions:\n1. Expand on the second point\n2. Add more specific details`;
      } else if (item.category === "Prompts") {
        result = `Enhanced version:\n\n"${
          input.charAt(0).toUpperCase() + input.slice(1)
        }. Consider the implications of this from multiple perspectives. Analyze the historical context and potential future developments. Provide concrete examples to support your reasoning."`;
      } else if (item.category === "Models") {
        result = `Model output:\n\n${input}\n\nThe above text was processed using our advanced language model. Confidence score: ${(
          Math.random() * 0.3 +
          0.7
        ).toFixed(2)}\n\nAlternative formulations:\n1. "${input
          .split(" ")
          .reverse()
          .join(" ")}"\n2. "${input.toUpperCase()}"`;
      } else {
        result = `Processed with ${item.title}:\n\n${input}\n\nThis is a preview of how ${item.title} would process your input. Install the full version for complete functionality.`;
      }

      setOutput(result);
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="p-4">
      <Tabs defaultValue="demo">
        <TabsList className="mb-4">
          <TabsTrigger value="demo">Interactive Demo</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="demo" className="space-y-4">
          <div>
            <h3 className="text-lg font-medium mb-2">Try {item.title}</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Enter some text below to see how {item.title} works. This is a
              limited preview of the functionality.
            </p>

            <div className="space-y-4">
              <div>
                <label htmlFor="input" className="text-sm font-medium">
                  Input
                </label>
                <Textarea
                  id="input"
                  placeholder="Enter your text here..."
                  className="mt-1"
                  rows={5}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                />
              </div>

              <Button
                onClick={handleSubmit}
                disabled={!input.trim() || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Process with " + item.title
                )}
              </Button>

              {(output || isLoading) && (
                <div>
                  <label htmlFor="output" className="text-sm font-medium">
                    Output
                  </label>
                  <div className="mt-1 p-3 border rounded-md bg-muted/30 min-h-[120px] whitespace-pre-wrap">
                    {isLoading ? (
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                      </div>
                    ) : (
                      output
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <div>
            <h3 className="text-lg font-medium mb-2">Configuration Options</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Customize how {item.title} works with these settings. These
              settings are for preview purposes only.
            </p>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="setting1" className="text-sm font-medium">
                    Processing Mode
                  </label>
                  <select
                    id="setting1"
                    className="w-full p-2 border rounded-md"
                    defaultValue="standard"
                  >
                    <option value="standard">Standard</option>
                    <option value="advanced">Advanced</option>
                    <option value="expert">Expert</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="setting2" className="text-sm font-medium">
                    Output Format
                  </label>
                  <select
                    id="setting2"
                    className="w-full p-2 border rounded-md"
                    defaultValue="text"
                  >
                    <option value="text">Plain Text</option>
                    <option value="markdown">Markdown</option>
                    <option value="html">HTML</option>
                    <option value="json">JSON</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="setting3" className="text-sm font-medium">
                    Processing Speed
                  </label>
                  <div className="flex items-center">
                    <Input
                      id="setting3"
                      type="range"
                      min="1"
                      max="10"
                      defaultValue="5"
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">5</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="setting4" className="text-sm font-medium">
                    Detail Level
                  </label>
                  <div className="flex items-center">
                    <Input
                      id="setting4"
                      type="range"
                      min="1"
                      max="10"
                      defaultValue="7"
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">7</span>
                  </div>
                </div>
              </div>

              <Button className="w-full">Apply Settings</Button>

              <p className="text-xs text-muted-foreground">
                Note: Some settings may be limited in the preview version.
                Install the full version to access all configuration options.
              </p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
