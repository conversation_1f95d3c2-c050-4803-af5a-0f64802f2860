"use client";

import { useState } from "react";
import { Download, Loader2, Check } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DirectInstallProps {
  item: {
    id: string;
    title: string;
    githubUrl?: string;
  };
  fullWidth?: boolean;
}

export function DirectInstall({ item, fullWidth = false }: DirectInstallProps) {
  const [isInstalling, setIsInstalling] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  const handleInstall = () => {
    setIsInstalling(true);

    // Simulate installation process
    setTimeout(() => {
      setIsInstalling(false);
      setIsInstalled(true);

      // Reset after 3 seconds
      setTimeout(() => {
        setIsInstalled(false);
      }, 3000);
    }, 2000);
  };

  return (
    <Button
      onClick={handleInstall}
      disabled={isInstalling || isInstalled}
      className={fullWidth ? "w-full" : ""}
      size={fullWidth ? "lg" : "default"}
    >
      {isInstalling ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Installing...
        </>
      ) : isInstalled ? (
        <>
          <Check className="h-4 w-4 mr-2" />
          Installed
        </>
      ) : (
        <>
          <Download className="h-4 w-4 mr-2" />
          Install Now
        </>
      )}
    </Button>
  );
}
