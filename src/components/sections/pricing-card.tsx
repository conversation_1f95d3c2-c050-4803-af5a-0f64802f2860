import { CheckCircle2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"

interface PricingCardProps {
  title: string
  price: string
  period: string
  features: string[]
  buttonText: string
  warningText?: string
  footerText?: string
}

export function PricingCard({ title, price, period, features, buttonText, warningText, footerText }: PricingCardProps) {
  return (
    <Card className="border-2 border-purple-600">
      <CardHeader className="text-center pb-2">
        <CardTitle className="text-2xl">{title}</CardTitle>
        <div className="mt-4 flex items-baseline justify-center">
          <span className="text-4xl font-bold">{price}</span>
          <span className="ml-1 text-gray-500">{period}</span>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-4">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center">
              <CheckCircle2 className="h-5 w-5 text-purple-600 mr-2" />
              <span>{feature}</span>
            </div>
          ))}
        </div>
        <div className="mt-8">
          <Button className="w-full bg-purple-600 hover:bg-purple-700">{buttonText}</Button>
          {warningText && <p className="mt-4 text-sm text-red-500 font-medium">{warningText}</p>}
        </div>
      </CardContent>
      {footerText && (
        <CardFooter className="bg-gray-50 dark:bg-gray-800 px-6 py-4 border-t">
          <p className="text-sm text-gray-500 dark:text-gray-400 text-center w-full">{footerText}</p>
        </CardFooter>
      )}
    </Card>
  )
}
