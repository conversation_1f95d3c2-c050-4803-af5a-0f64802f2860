interface VideoPlayerProps {
  videoUrl: string
  title: string
  posterUrl?: string
}

export function VideoPlayer({ videoUrl, title, posterUrl }: VideoPlayerProps) {
  return (
    <div className="w-full rounded-xl overflow-hidden border shadow-lg">
      <video
        className="w-full aspect-video"
        controls
        poster={posterUrl || "/placeholder.svg?height=720&width=1280"}
        preload="metadata"
      >
        <source src={videoUrl} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      {title && <p className="text-sm text-gray-500 mt-2 text-center">{title}</p>}
    </div>
  )
}
