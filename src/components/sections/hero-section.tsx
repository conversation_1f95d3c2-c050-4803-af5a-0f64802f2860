import React from "react";
import { ArrowRight, CheckCircle2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface HeroSectionProps {
  title: React.ReactNode;
  subtitle: string;
  primaryButtonText: string;
  secondaryButtonText: string;
  features: string[];
  imageAlt: string;
  imageUrl?: string;
}

export function HeroSection({
  title,
  subtitle,
  primaryButtonText,
  secondaryButtonText,
  features,
  imageAlt,
  imageUrl = "/placeholder.svg?height=600&width=1200",
}: HeroSectionProps) {
  return (
    <section className="relative overflow-hidden py-20 md:py-32 bg-gradient-to-br from-purple-50 to-white dark:from-purple-950 dark:to-background">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center text-center">
          <div className="space-y-4 max-w-3xl mx-auto mb-10">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
              {title}
            </h1>
            <p className="max-w-[700px] mx-auto text-gray-500 md:text-xl dark:text-gray-400">
              {subtitle}
            </p>
          </div>

          {/* Large Screenshot */}
          <div className="relative w-full max-w-5xl mx-auto mb-12">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl blur-3xl opacity-20 animate-pulse"></div>
            <div className="relative rounded-xl border shadow-xl overflow-hidden">
              <img
                src={imageUrl || "/placeholder.svg"}
                alt={imageAlt}
                className="w-full h-auto object-cover"
              />
            </div>
          </div>

          <div className="flex flex-col gap-4 items-center">
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Button className="bg-purple-600 hover:bg-purple-700">
                <Link href="/chat">{primaryButtonText}</Link>
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button variant="primary-outline">
                <Link href="/demo">{secondaryButtonText}</Link>
              </Button>
            </div>
            <div className="flex items-center gap-2 text-sm flex-wrap justify-center mt-4">
              {features.map((feature, index) => (
                <React.Fragment key={index}>
                  {index > 0 && <span className="ml-4"></span>}
                  <CheckCircle2 className="h-4 w-4 text-purple-600" />
                  <span>{feature}</span>
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
