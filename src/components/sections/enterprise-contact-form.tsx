import { Button } from "@/components/ui/button"

export function EnterpriseContactForm() {
  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 lg:p-8 shadow-lg">
      <h3 className="text-xl font-bold mb-4">Request Enterprise Information</h3>
      <div className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium" htmlFor="first-name">
              First Name
            </label>
            <input
              id="first-name"
              className="w-full px-3 py-2 bg-white/20 border border-purple-300 rounded-md text-white placeholder:text-purple-200"
              placeholder="John"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium" htmlFor="last-name">
              Last Name
            </label>
            <input
              id="last-name"
              className="w-full px-3 py-2 bg-white/20 border border-purple-300 rounded-md text-white placeholder:text-purple-200"
              placeholder="Doe"
            />
          </div>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium" htmlFor="company">
            Company
          </label>
          <input
            id="company"
            className="w-full px-3 py-2 bg-white/20 border border-purple-300 rounded-md text-white placeholder:text-purple-200"
            placeholder="Your Company"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium" htmlFor="email">
            Work Email
          </label>
          <input
            id="email"
            type="email"
            className="w-full px-3 py-2 bg-white/20 border border-purple-300 rounded-md text-white placeholder:text-purple-200"
            placeholder="<EMAIL>"
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium" htmlFor="employees">
            Number of Employees
          </label>
          <select
            id="employees"
            className="w-full px-3 py-2 bg-white/20 border border-purple-300 rounded-md text-white"
          >
            <option value="" className="text-gray-800">
              Select...
            </option>
            <option value="1-50" className="text-gray-800">
              1-50
            </option>
            <option value="51-200" className="text-gray-800">
              51-200
            </option>
            <option value="201-500" className="text-gray-800">
              201-500
            </option>
            <option value="501-1000" className="text-gray-800">
              501-1000
            </option>
            <option value="1000+" className="text-gray-800">
              1000+
            </option>
          </select>
        </div>
        <Button className="w-full bg-white text-purple-600 hover:bg-purple-50">Get Enterprise Information</Button>
      </div>
    </div>
  )
}
