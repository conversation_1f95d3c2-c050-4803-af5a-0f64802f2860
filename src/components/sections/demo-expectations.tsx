import type React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface ExpectationItem {
  icon: React.ElementType
  title: string
  description: string
}

interface DemoExpectationsProps {
  title: string
  subtitle: string
  expectations: ExpectationItem[]
}

export function DemoExpectations({ title, subtitle, expectations }: DemoExpectationsProps) {
  return (
    <section className="py-12">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">{title}</h2>
            <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed dark:text-gray-400">{subtitle}</p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {expectations.map((item, index) => {
            const Icon = item.icon
            return (
              <Card key={index} className="flex flex-col h-full">
                <CardHeader className="pb-2">
                  <Icon className="h-6 w-6 text-purple-600 mb-2" />
                  <CardTitle className="text-lg">{item.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{item.description}</CardDescription>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}
