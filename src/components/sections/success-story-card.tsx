import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"

interface SuccessStoryCardProps {
  company: {
    name: string
    industry: string
    logoUrl: string
  }
  quote: string
  person: {
    name: string
    title: string
  }
  results: string
}

export function SuccessStoryCard({ company, quote, person, results }: SuccessStoryCardProps) {
  return (
    <Card className="flex flex-col h-full">
      <CardHeader>
        <div className="flex items-center gap-4 mb-2">
          <div className="h-12 w-12 rounded-full overflow-hidden bg-gray-100">
            <img
              src={company.logoUrl || "/placeholder.svg"}
              alt={`${company.name} Logo`}
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <CardTitle className="text-lg">{company.name}</CardTitle>
            <p className="text-sm text-gray-500">{company.industry}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1">
        <blockquote className="border-l-4 border-purple-600 pl-4 italic text-gray-600 dark:text-gray-300">
          {quote}
        </blockquote>
        <div className="mt-4">
          <p className="font-medium">{person.name}</p>
          <p className="text-sm text-gray-500">{person.title}</p>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="flex items-center text-sm">
          <span className="font-medium mr-2">Results:</span>
          <span>{results}</span>
        </div>
      </CardFooter>
    </Card>
  )
}
