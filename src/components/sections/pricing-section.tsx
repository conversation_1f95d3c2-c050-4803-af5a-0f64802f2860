import Link from "next/link"
import { PricingCard } from "./pricing-card"

interface PricingSectionProps {
  title: string
  subtitle: string
  pricing: {
    title: string
    price: string
    period: string
    features: string[]
    buttonText: string
    warningText?: string
    footerText?: string
  }
  alternateLink?: {
    text: string
    href: string
  }
}

export function PricingSection({ title, subtitle, pricing, alternateLink }: PricingSectionProps) {
  return (
    <section id="pricing" className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{title}</h2>
            <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {subtitle}
            </p>
          </div>
        </div>
        <div className="mx-auto max-w-3xl pt-12">
          <PricingCard {...pricing} />
        </div>
        {alternateLink && (
          <div className="text-center mt-8">
            <Link href={alternateLink.href} className="text-purple-600 hover:text-purple-700 font-medium">
              {alternateLink.text}
            </Link>
          </div>
        )}
      </div>
    </section>
  )
}
