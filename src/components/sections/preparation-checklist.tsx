import { Check } from "lucide-react"

interface ChecklistItem {
  title: string
  description: string
}

interface PreparationChecklistProps {
  title: string
  subtitle?: string
  items: ChecklistItem[]
}

export function PreparationChecklist({ title, subtitle, items }: PreparationChecklistProps) {
  return (
    <div className="rounded-xl border p-6 bg-white dark:bg-gray-800 shadow-sm">
      <h3 className="text-xl font-bold mb-2">{title}</h3>
      {subtitle && <p className="text-gray-500 dark:text-gray-400 mb-6">{subtitle}</p>}
      <ul className="space-y-4">
        {items.map((item, index) => (
          <li key={index} className="flex">
            <div className="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center mr-3 mt-0.5">
              <Check className="h-3.5 w-3.5 text-purple-600" />
            </div>
            <div>
              <h4 className="font-medium">{item.title}</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{item.description}</p>
            </div>
          </li>
        ))}
      </ul>
    </div>
  )
}
