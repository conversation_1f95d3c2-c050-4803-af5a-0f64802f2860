import { Button } from "@/components/ui/button"

interface DemoBookingSectionProps {
  title: string
  subtitle: string
  buttonText: string
  disclaimer?: string
}

export function DemoBookingSection({ title, subtitle, buttonText, disclaimer }: DemoBookingSectionProps) {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{title}</h2>
            <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {subtitle}
            </p>
          </div>
          <div className="w-full max-w-sm space-y-2">
            <Button className="w-full bg-purple-600 hover:bg-purple-700">{buttonText}</Button>
            {disclaimer && <p className="text-xs text-gray-500 dark:text-gray-400">{disclaimer}</p>}
          </div>
        </div>
      </div>
    </section>
  )
}
