import { But<PERSON> } from "@/components/ui/button"
import { SuccessStoryCard } from "./success-story-card"

interface SuccessStory {
  company: {
    name: string
    industry: string
    logoUrl: string
  }
  quote: string
  person: {
    name: string
    title: string
  }
  results: string
}

interface SuccessStoriesSectionProps {
  title: string
  subtitle: string
  stories: SuccessStory[]
  ctaText: string
}

export function SuccessStoriesSection({ title, subtitle, stories, ctaText }: SuccessStoriesSectionProps) {
  return (
    <section id="customers" className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{title}</h2>
            <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {subtitle}
            </p>
          </div>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {stories.map((story, index) => (
            <SuccessStoryCard key={index} {...story} />
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button variant="primary-outline" className="border-purple-600 text-purple-600 hover:bg-purple-50">
            {ctaText}
          </Button>
        </div>
      </div>
    </section>
  )
}
