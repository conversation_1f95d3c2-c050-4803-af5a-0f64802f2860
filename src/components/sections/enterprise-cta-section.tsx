import { Button } from "@/components/ui/button"
import { EnterpriseContactForm } from "./enterprise-contact-form"

interface EnterpriseCTASectionProps {
  title: string
  subtitle: string
  primaryButtonText: string
  secondaryButtonText: string
  warningText?: string
}

export function EnterpriseCTASection({
  title,
  subtitle,
  primaryButtonText,
  secondaryButtonText,
  warningText,
}: EnterpriseCTASectionProps) {
  return (
    <section id="contact" className="py-16 md:py-24 bg-purple-600 text-white">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-2 items-center">
          <div className="space-y-4">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{title}</h2>
            <p className="text-purple-100 md:text-xl max-w-[600px]">{subtitle}</p>
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-purple-50">
                {primaryButtonText}
              </Button>
              <Button size="lg" variant="primary-outline" className="border-white text-white hover:bg-purple-700">
                {secondaryButtonText}
              </Button>
            </div>
            {warningText && <p className="text-sm text-purple-200 font-medium pt-2">{warningText}</p>}
          </div>
          <EnterpriseContactForm />
        </div>
      </div>
    </section>
  )
}
