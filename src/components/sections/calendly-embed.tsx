"use client"

import { useEffect } from "react"
import Script from "next/script"

interface CalendlyEmbedProps {
  url: string
  title?: string
  subtitle?: string
}

export function CalendlyEmbed({ url, title, subtitle }: CalendlyEmbedProps) {
  useEffect(() => {
    // This will re-initialize Calendly if the component re-renders
    // @ts-ignore - Calendly is loaded via the script tag
    if (window.Calendly) {
      // @ts-ignore
      window.Calendly.initInlineWidget({
        url: url,
        parentElement: document.getElementById("calendly-embed"),
        prefill: {},
        utm: {},
      })
    }
  }, [url])

  return (
    <section className="py-12">
      <Script src="https://assets.calendly.com/assets/external/widget.js" strategy="lazyOnload" />

      {(title || subtitle) && (
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
          {title && <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">{title}</h2>}
          {subtitle && <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed dark:text-gray-400">{subtitle}</p>}
        </div>
      )}

      <div className="container px-4 md:px-6">
        <div
          id="calendly-embed"
          className="calendly-inline-widget min-h-[650px] rounded-xl overflow-hidden shadow-lg border"
          data-url={url}
        ></div>
      </div>
    </section>
  )
}
