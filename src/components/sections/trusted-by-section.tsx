interface TrustedBySectionProps {
  title: string
  companies: {
    alt: string
    imageUrl: string
  }[]
}

export function TrustedBySection({ title, companies }: TrustedBySectionProps) {
  return (
    <section className="py-12 border-y bg-gray-50 dark:bg-gray-900">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-8">
          <h2 className="text-xl font-semibold tracking-tight">{title}</h2>
        </div>
        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16">
          {companies.map((company, index) => (
            <div
              key={index}
              className="flex items-center justify-center h-12 w-32 grayscale opacity-70 hover:grayscale-0 hover:opacity-100 transition-all"
            >
              <img src={company.imageUrl || "/placeholder.svg"} alt={company.alt} className="h-full object-contain" />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
