interface AudienceItem {
  title: string
  description: string
}

interface TargetAudienceSectionProps {
  title: string
  subtitle: string
  audiences: AudienceItem[]
}

export function TargetAudienceSection({ title, subtitle, audiences }: TargetAudienceSectionProps) {
  return (
    <section className="py-16 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">{title}</h2>
            <p className="max-w-[700px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              {subtitle}
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2 pt-12">
          {audiences.map((audience, index) => (
            <div key={index} className="flex flex-col space-y-2">
              <h3 className="text-xl font-bold">{audience.title}</h3>
              <p className="text-gray-500 dark:text-gray-400">{audience.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
