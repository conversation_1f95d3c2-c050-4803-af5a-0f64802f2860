"use client";

import { PopupProvider } from "@/context/usePopUp";
import FeedbackPopup from "@/components/FeedbackPopup";
import { PromptLibraryProvider } from "@/context/usePromptLibrary";
import { FeedbackProvider } from "@/context/useFeedback";
import PromptLibraryPopup from "@/components/PromptLibraryPopup";
import { ChatRoomProvider } from "@/context/useChatRoom";
import ChatRoomPopup from "@/components/ChatRoomPopUp";
import RootAppNavigation from "./RootAppNavigation";
import { ChatFolderProvider } from "@/context/useChatFolder";
import ChatFolderPopup from "./ChatFolderPopUp";
import { PopupOverlay, PreviewPopup } from "./Popup";
import { AppSettingsProvider, useAppSettings } from "@/context/useAppSetting";
import { ModelProvider } from "@/context/useModel";
import { PluginProvider } from "@/context/usePlugin";
import { AIAgentProvider } from "@/context/useAIAgents";
import { SidebarProvider } from "@/context/useSidebar";
import { useRoot } from "@/context/useRoot";
import { useEffect } from "react";

export default function ClientApp() {
  return (
    <>
      <AppSettingsProvider>
        <ModelProvider>
          <AIAgentProvider>
            <PluginProvider>
              <PromptLibraryProvider>
                <FeedbackProvider>
                  <PopupProvider>
                    <ChatFolderProvider>
                      <ChatRoomProvider>
                        <SidebarProvider>
                          <div className="w-screen h-screen">
                            <RootAppNavigation />
                            <PreviewPopup />
                            <FeedbackPopup />
                            <PromptLibraryPopup />
                            <ChatFolderPopup />
                            <ChatRoomPopup />
                          </div>
                        </SidebarProvider>
                      </ChatRoomProvider>
                    </ChatFolderProvider>
                  </PopupProvider>
                </FeedbackProvider>
              </PromptLibraryProvider>
            </PluginProvider>
          </AIAgentProvider>
        </ModelProvider>
      </AppSettingsProvider>
    </>
  );
}
