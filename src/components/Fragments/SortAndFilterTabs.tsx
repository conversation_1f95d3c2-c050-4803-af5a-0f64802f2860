import React, { useState } from "react";
import { SORT_OPTIONS, ORDER_OPTIONS } from "./constants";
import { TabView } from "../TabView";
import BulkActionSection from "./BulkActionSection";
import { TagFilter } from "./TagFilter";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SortAndFilterTabsProps {
  sortOption: { sort: string; order: string };
  filterTags: (tags: string[]) => void;
  handleSortChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  handleOrderChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  bulkAction: Set<string> | null;
  setBulkAction: React.Dispatch<React.SetStateAction<Set<string> | null>>;
  deleteBulk: () => void;
  archiveBulk: () => void;
}

const SortAndFilterTabs: React.FC<SortAndFilterTabsProps> = ({
  sortOption,
  filterTags,
  handleSortChange,
  handleOrderChange,
  bulkAction,
  setBulkAction,
  deleteBulk,
  archiveBulk,
}) => {
  const [notEmptyState, setNotEmptyState] = useState<string[]>([]);

  return (
    <div className="max-h-full">
      <TabView
        config={{ tab: { inactive: { class: "text-primary-foreground" } } }}
        tabs={[
          {
            id: "sorting",
            title: "Sort",
            content: (
              <div className="flex space-x-2 h-full py-4 text-black">
                <Select
                  value={sortOption.sort}
                  onValueChange={(val) => {
                    handleSortChange({
                      target: { value: val },
                    } as React.ChangeEvent<HTMLSelectElement>);
                  }}
                >
                  <SelectTrigger className="flex-grow bg-white">
                    <SelectValue placeholder="Sort by..." />
                  </SelectTrigger>
                  <SelectContent>
                    {SORT_OPTIONS.map((e) => (
                      <SelectItem key={e.value} value={e.value}>
                        {e.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={sortOption.order}
                  onValueChange={(val) => {
                    handleOrderChange({
                      target: { value: val },
                    } as React.ChangeEvent<HTMLSelectElement>);
                  }}
                >
                  <SelectTrigger className="w-[100px] bg-white">
                    <SelectValue placeholder="Order" />
                  </SelectTrigger>
                  <SelectContent>
                    {ORDER_OPTIONS.map((e) => (
                      <SelectItem key={e.value} value={e.value}>
                        {e.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ),
          },
          {
            id: "tag-filter",
            title: notEmptyState.includes("tag-filter")
              ? "Tag Filter*"
              : "Tag Filter",
            content: (
              <TagFilter
                onApplyFilter={(tags) => {
                  filterTags(tags);
                  if (tags.length > 0) {
                    setNotEmptyState((prev) => [...prev, "tag-filter"]);
                  } else {
                    setNotEmptyState((prev) =>
                      prev.filter((p) => p !== "tag-filter")
                    );
                  }
                }}
              />
            ),
          },
          {
            id: "bulk-action",
            title: "Bulk Action",
            content: (
              <BulkActionSection
                bulkAction={bulkAction}
                setBulkAction={setBulkAction}
                deleteBulk={deleteBulk}
                archiveBulk={archiveBulk}
              />
            ),
          },
        ]}
      />
    </div>
  );
};

export default SortAndFilterTabs;
