import React, { useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";

interface BulkActionSectionProps {
  bulkAction: Set<string> | null;
  setBulkAction: React.Dispatch<React.SetStateAction<Set<string> | null>>;
  deleteBulk: () => void;
  archiveBulk: () => void;
}

const BulkActionSection: React.FC<BulkActionSectionProps> = ({
  bulkAction,
  setBulkAction,
  deleteBulk,
  archiveBulk,
}) => {
  const sectionRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setBulkAction(new Set());
        } else {
          setBulkAction(null);
        }
      },
      { root: null, threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <div className="flex flex-col gap-4" ref={sectionRef}>
      <div className="flex items-center gap-2 mb-2">
        <Button
          variant="primary-outline"
          className="dark:text-primary"
          onClick={() => setBulkAction((prev) => (prev ? null : new Set()))}
        >
          Bulk Action{bulkAction && ` (${bulkAction.size})`}
        </Button>
      </div>
      <div className="flex gap-2">
        <Button variant="destructive" onClick={deleteBulk} className="px-4">
          Delete
        </Button>
        <Button onClick={archiveBulk} className="px-4">
          Archive
        </Button>
      </div>
    </div>
  );
};

export default BulkActionSection;
