import React from "react";
import ChatFolderComponent from "../ChatFolderComponent";
import ChatRoomView from "../ChatRoomView";
import EmptyChatFolder from "../EmptyChatRoom";
import { ChatFolder } from "@/model/chatFolder";
import { ChatRoom } from "@/model/chatroom";
import { twMerge } from "tailwind-merge";

interface ChatItemsListProps {
  sortedChatFoldersWithOrphans: Array<{
    folder?: ChatFolder;
    room?: ChatRoom;
    id: string;
    createdAt: Date;
    name: string;
  }>;
  activeMenu: ChatRoom | undefined;
  handleDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
  handleDragLeave: (event: React.DragEvent<HTMLDivElement>) => void;
  handleDrop: (event: React.DragEvent<HTMLDivElement>) => void;
  handleDragStart: (
    event: React.DragEvent<HTMLDivElement>,
    roomId: string
  ) => void;
  onBulkActionToggle: (id: string) => void;
  bulkAction: Set<string> | null;
  firstElementOfDragElementKeys: string | null;
}

const ChatItemsList: React.FC<ChatItemsListProps> = ({
  sortedChatFoldersWithOrphans,
  activeMenu,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  handleDragStart,
  onBulkActionToggle,
  bulkAction,
  firstElementOfDragElementKeys,
}) => {
  return (
    <div
      className={twMerge(
        "mt-5 w-full h-full p-0.5",
        firstElementOfDragElementKeys === "" &&
          "border border-[var(--border-custom-blue-300)]"
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {sortedChatFoldersWithOrphans.length > 0 ? (
        sortedChatFoldersWithOrphans.map((item) =>
          item.folder ? (
            <div
              key={item.folder.id}
              id={item.folder.id}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <ChatFolderComponent
                folder={item.folder}
                isDragOver={firstElementOfDragElementKeys === item.id}
                onDragStart={(event, roomId) => handleDragStart(event, roomId)}
                isBulkAction={bulkAction && bulkAction.has(item.id)}
                onBulkActionToggle={onBulkActionToggle}
              />
            </div>
          ) : (
            <ChatRoomView
              key={item.id}
              room={item.room!}
              isActive={activeMenu?.id === item.id}
              onDragStart={(event) => handleDragStart(event, item.id)}
              isBulkAction={bulkAction && bulkAction.has(item.id)}
              onBulkActionToggle={onBulkActionToggle}
            />
          )
        )
      ) : (
        <EmptyChatFolder />
      )}
    </div>
  );
};

export default ChatItemsList;
