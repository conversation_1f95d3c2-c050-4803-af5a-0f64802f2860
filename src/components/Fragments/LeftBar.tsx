import React, { useEffect, useState, useMemo } from "react";
import { usePathname } from "next/navigation";
import { usePopup } from "@/context/usePopUp";
import { useChatRoom } from "@/context/useChatRoom";
import { useChatFolder } from "@/context/useChatFolder";
import { ChatFolderRepo } from "@/repo/chatFolder";
import { ChatRoomRepo } from "@/repo/chatRoom";
import { ChatFolder } from "@/model/chatFolder";
import { ChatRoom } from "@/model/chatroom";
import { Icon } from "../Icon";
import { CustomPopUp } from "../Popup";
import { AppSetting } from "../AppSetting";
import { OrderOption, SortOption } from "./constants";
import Link from "next/link";
import TextInfo from "../TextInfo";
import SearchSection from "./SearchSection";
import SortAndFilterTabs from "./SortAndFilterTabs";
import BulkActionSection from "./BulkActionSection";
import ChatItemsList from "./ChatItemList";
import MobileAppBanner from "../MobileAppBanner";
import { ScrollArea } from "@/components/ui/scroll-area";

const LeftBar: React.FC = () => {
  const [chatRoomFolders, setChatRoomsWithFolders] = useState<ChatFolder[]>([]);
  const [orphanChatRooms, setOrphanChatRooms] = useState<ChatRoom[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const { viewModel: chatFolderVM, openChatFolder } = useChatFolder();
  const { viewModel: chatRoomVM, openRoom, openChatRoom, closeChatRoom } = useChatRoom();
  const [dragOverElementKeys, setDragOverElementKeys] = useState<string[]>([]);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [filterByTags, setFilterByTags] = useState<string[]>([]);
  const [sortOption, setSortOption] = useState({
    sort: "date",
    order: "desc",
  });
  const { openPopup } = usePopup();
  const [bulkAction, setBulkAction] = useState<Set<string> | null>(null);

  chatFolderVM.onFoldersUpdated = loadChatRooms;
  chatRoomVM.onRoomsUpdated = loadChatRooms;

  async function clearChatRoomWithEmptyName() {
    const chatRoomRepo = ChatRoomRepo.getInstance().getChatRoomRepo();
    const allChatRooms = await chatRoomRepo.getChatRooms([""]);

    let orphanRooms = allChatRooms[""] || [];

    const emptyMessageRooms = orphanRooms.filter((e) => e.name === "");
    for (const room of emptyMessageRooms) {
      chatRoomVM.deleteChatRoom(room.id);
    }
  }

  async function loadChatRooms() {
    const chatRoomRepo = ChatRoomRepo.getInstance().getChatRoomRepo();
    const chatFolderRepo = ChatFolderRepo.getInstance().getChatFolderRepo();

    const folders = await chatFolderRepo.getChatFolders();
    const allChatRooms = await chatRoomRepo.getChatRooms([
      "",
      ...folders.map((e) => e.id),
    ]);

    const chatRoomGroupedByFolder = folders.reduce((acc, folder) => {
      acc[folder.id] = allChatRooms[folder.id];
      return acc;
    }, {} as Record<string, ChatRoom[]>);

    const chatRoomsWithFolders = folders.map((folder) => ({
      ...folder,
      rooms: chatRoomGroupedByFolder[folder.id] || [],
    }));

    const orphanRooms = (allChatRooms[""] || []).filter((e) => e.name !== "");

    setChatRoomsWithFolders(chatRoomsWithFolders);
    setOrphanChatRooms(orphanRooms);
  }

  const newChatFolder = async () => {
    const newFolder = await chatFolderVM.createNewFolder();
    openChatFolder(newFolder);
  };

  const newChatRoom = async () => {
    openChatRoom(null);
  };

  useEffect(() => {
    clearChatRoomWithEmptyName();
    loadChatRooms();
  }, []);

  const filteredChatFolders = useMemo(() => {
    const filteredByQuery =
      searchQuery === ""
        ? chatRoomFolders
        : (chatRoomFolders
            .map((folder) => {
              if (
                folder.folderName
                  .toLowerCase()
                  .includes(searchQuery.toLowerCase())
              ) {
                return folder;
              }
              const filteredRooms = folder.rooms.filter((room) =>
                room.name.toLowerCase().includes(searchQuery.toLowerCase())
              );

              if (filteredRooms.length > 0) {
                return { ...folder, rooms: filteredRooms };
              } else {
                return null;
              }
            })
            .filter((folder) => folder !== null) as ChatFolder[]);

    const filteredByTags =
      filterByTags.length == 0
        ? filteredByQuery
        : filteredByQuery.map((folder) => {
            const filteredRoomsByTags = folder.rooms.filter((room) =>
              filterByTags.some((tag) => (room.tags || []).includes(tag))
            );
            const newFolder = {
              ...folder,
              rooms: filteredRoomsByTags,
            };
            return newFolder
          });

    return filteredByTags;
  }, [chatRoomFolders, searchQuery, filterByTags]);

  const filteredOrphanChatRooms = useMemo(() => {
    const filteredByQuery =
      searchQuery === ""
        ? orphanChatRooms
        : orphanChatRooms.filter((room) =>
            room.name.toLowerCase().includes(searchQuery.toLowerCase())
          );

    const filteredByTags =
      filterByTags.length == 0
        ? filteredByQuery
        : filteredByQuery.filter((room) =>
            filterByTags.some((tag) => (room.tags || []).includes(tag))
          );

    return filteredByTags;
  }, [orphanChatRooms, searchQuery, filterByTags]);

  const sortedChatFoldersWithOrphans = useMemo(() => {
    const foldersWithOrphans: {
      folder?: ChatFolder;
      room?: ChatRoom;
      id: string;
      createdAt: Date;
      name: string;
    }[] = [
      ...filteredChatFolders.map((e) => ({
        folder: e,
        id: e.id,
        name: e.folderName,
        createdAt: e.createdAt,
      })),
      ...filteredOrphanChatRooms.map((e) => ({
        room: e,
        id: e.id,
        name: e.name,
        createdAt: e.createdAt,
      })),
    ];

    const sorted = [...foldersWithOrphans].sort((a, b) => {
      switch (sortOption.sort) {
        case "date":
          return sortOption.order === "asc"
            ? a.createdAt.getTime() - b.createdAt.getTime()
            : b.createdAt.getTime() - a.createdAt.getTime();
        case "name":
          return sortOption.order === "asc"
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        default:
          return 0;
      }
    });

    return sorted;
  }, [filteredChatFolders, filteredOrphanChatRooms, sortOption]);

  const activeMenu = useMemo(
    () => orphanChatRooms.find((room) => room.id === openRoom?.id),
    [openRoom, orphanChatRooms]
  );

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (!event.currentTarget) {
      return;
    }

    const newId = event.currentTarget.id;

    setDragOverElementKeys((prev) => {
      const index = prev.indexOf(newId);
      if (index === -1) {
        return [...prev, newId];
      }
      return prev;
    });
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    if (!event.currentTarget) {
      return;
    }

    const idToRemove = event.currentTarget.id;

    setDragOverElementKeys((prev) => {
      return prev.filter((id) => id !== idToRemove);
    });
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const roomId = event.dataTransfer.getData("text/plain");

    const folderId = firstElementOfDragElementKeys() || "";
    await chatRoomVM.updateChatRoomFolder(roomId, folderId);
    setDragOverElementKeys([]);
    loadChatRooms();
  };

  const handleDragStart = async (
    event: React.DragEvent<HTMLDivElement>,
    roomId: string
  ) => {
    event.dataTransfer.setData("text/plain", roomId);
  };

  const firstElementOfDragElementKeys = (): string | null => {
    return dragOverElementKeys.length === 0 ? null : dragOverElementKeys[0];
  };

  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value as SortOption;
    setSortOption((prev) => ({ ...prev, sort: value }));
  };

  const handleOrderChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value as OrderOption;
    setSortOption((prev) => ({ ...prev, order: value }));
  };

  const handleFilterTags = (tags: string[]) => {
    setFilterByTags(tags);
  };

  function openSetting() {
    openPopup(
      new CustomPopUp({
        context: "app-setting",
        view: <AppSetting />,
      })
    );
  }

  function onBulkActionToggle(id: string) {
    setBulkAction((prev) => {
      if (prev) {
        const newSet = new Set(prev);
        if (newSet.has(id)) {
          newSet.delete(id);
        } else {
          newSet.add(id);
        }
        return newSet;
      }
      return new Set([id]);
    });
  }

  async function deleteBulk() {
    if (!bulkAction) {return}
    bulkAction.forEach(async (roomId) => {
      await chatRoomVM.deleteChatRoom(roomId);
      if (openRoom?.id === roomId) {
        closeChatRoom()
      }
    })
  }

  function archiveBulk() {
    // Handle archive bulk action
  }

  return (
    <>
      <div className="flex space-x-2 mb-5 w-full justify-between items-center px-2 text-sm">
        <h1 className="text-primary-foreground  flex justify-center flex-grow">
          <Link href={"/"}>
            賢 <span className="font-bold">Tomio AI</span>
            <TextInfo>Preview</TextInfo>
          </Link>
        </h1>
        <button className="hidden md:block text-primary-foreground hover:rotate-90" onClick={openSetting}>
          <Icon iconName="Setting" />
        </button>
      </div>
      <SearchSection
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        isInputFocused={isInputFocused}
        setIsInputFocused={setIsInputFocused}
        newChatFolder={newChatFolder}
        newChatRoom={newChatRoom}
      />
      <SortAndFilterTabs
        sortOption={sortOption}
        handleSortChange={handleSortChange}
        handleOrderChange={handleOrderChange}
        filterTags={handleFilterTags}
        bulkAction={bulkAction}
        setBulkAction={setBulkAction}
        deleteBulk={deleteBulk}
        archiveBulk={archiveBulk}
      />
      <ScrollArea className="w-full flex flex-col h-full">
        <ChatItemsList
          sortedChatFoldersWithOrphans={sortedChatFoldersWithOrphans}
          activeMenu={activeMenu}
          handleDragOver={handleDragOver}
          handleDragLeave={handleDragLeave}
          handleDrop={handleDrop}
          handleDragStart={handleDragStart}
          onBulkActionToggle={onBulkActionToggle}
          bulkAction={bulkAction}
          firstElementOfDragElementKeys={firstElementOfDragElementKeys()}
        />
      </ScrollArea>
      <div className="pt-3">
        <MobileAppBanner />
      </div>
    </>
  );
};

export default LeftBar;