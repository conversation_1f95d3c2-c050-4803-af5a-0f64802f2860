import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import chat from "../../../public/chat.png";
import FeedbackButton from "../FeedbackButton";
import { PromptData } from "@/model/promptData";
import { usePromptLibrary } from "@/context/usePromptLibrary";
import Analytics from "@/services/analytics";
import { usePopup } from "@/context/usePopUp";
import { CustomPopUp } from "../Popup";
import { Extension } from "../ExtensionPage";
import { Icon } from "../Icon";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Store } from "lucide-react";
import Link from "next/link";

const PromptLibrary = ({ prompt }: { prompt: PromptData }) => {
  const { openPromptLibrary, setUsePrompt, deletePromptLibrary } =
    usePromptLibrary();
  return (
    <div
      key={prompt.id}
      className="group min-h-[40px] flex justify-between rounded-lg p-2 pr-4 mb-1 transition hover:bg-white hover:bg-opacity-50"
    >
      <button
        className="flex-shrink text-primary-foreground text-start text-sm"
        onClick={() => setUsePrompt(prompt)}
      >
        {prompt.promptName}
      </button>
      <div className="ml-1 text-blue-50 gap-2 hidden group-hover:flex">
        <button
          className="text-sm"
          onClick={() => {
            openPromptLibrary(prompt);
          }}
        >
          <Icon iconName="Edit" />
        </button>
        <button
          className="text-sm"
          onClick={() => {
            deletePromptLibrary(prompt);
          }}
        >
          <Icon iconName="Trash" />
        </button>
      </div>
    </div>
  );
};

const EmptyPromptLibrarySection = () => (
  <div className="w-full rounded-lg p-4 border-dashed text-sm font-semibold text-muted-foreground border-2 text-center">
    <p>No Library Yet</p>
    <p>Click the button above to start a new Library</p>
  </div>
);

interface PromptSort {
  name: string;
  sort(prompts: PromptData[], direction: "asc" | "desc"): PromptData[];
}

class PromptSortByLastAdd implements PromptSort {
  name = "Last Add";
  sort(prompts: PromptData[], direction: "asc" | "desc"): PromptData[] {
    const sorted = [...prompts].sort(
      (a, b) => a.createdAt.getMilliseconds() - b.createdAt.getMilliseconds()
    );
    return direction === "desc" ? sorted.reverse() : sorted;
  }
}

class PromptSortByName implements PromptSort {
  name = "Name";
  sort(prompts: PromptData[], direction: "asc" | "desc"): PromptData[] {
    const sorted = [...prompts].sort((a, b) =>
      a.promptName.localeCompare(b.promptName)
    );
    return direction === "desc" ? sorted.reverse() : sorted;
  }
}

const promptSorts = [new PromptSortByLastAdd(), new PromptSortByName()];
const defaultPromptSort = promptSorts[0];

type SortOption = {
  value: string;
  label: string;
  status?: string;
  onClick: (this: SortOption) => void;
};

const RightBar = () => {
  const [prompts, setPrompts] = useState<PromptData[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sort, setSort] = useState<PromptSort>(defaultPromptSort);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const { openPromptLibrary, viewModel } = usePromptLibrary();
  const { openPopup } = usePopup();

  viewModel.onPromptsUpdated = () => {
    setPrompts(viewModel.getPrompts());
  };

  const createNewPromptLibrary = async () => {
    const newPrompt = await viewModel.createNewPropmt();
    openPromptLibrary(newPrompt);
    Analytics.getInstance().track("Add Prompt Library", {
      currentPromtsCount: prompts.length,
    });
  };

  const openMarket = () => {
    openPopup(new CustomPopUp({ context: "plugin", view: <Extension /> }));
  };

  const sortOptions: SortOption[] = promptSorts.map((e) => ({
    value: e.name,
    label: e.name,
    onClick: () => setSort(e),
  }));

  useEffect(() => {
    viewModel.loadPrompts();
  }, []);

  const filteredPrompts = prompts.filter((prompt) =>
    prompt.promptName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const displayedPrompts = sort.sort(filteredPrompts, sortOrder);

  return (
    <>
      <div className="flex flex-col h-full space-y-2 w-full">
        <div className="flex w-full space-x-2">
          <Link href="/marketplace" target="_blank" rel="noopener noreferrer">
            <Button
              className="flex items-center gap-2"
              variant="primary"
            >
              <Store width={20} height={20} />
            </Button>
          </Link>

          <Button
            variant="primary"
            className="w-full flex items-center gap-2"
            onClick={createNewPromptLibrary}
          >
            <Image src={chat} alt="Chat" width={20} height={20} />
            New Prompt Library
          </Button>
        </div>

        <Input
          type="text"
          placeholder="Search Awesome Prompt"
          value={searchQuery}
          className="bg-white"
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="w-full flex space-x-2">
          <Select
            value={sort.name}
            onValueChange={(value) => {
              const selected = promptSorts.find((s) => s.name === value);
              if (selected) setSort(selected);
            }}
          >
            <SelectTrigger className="w-fit bg-white">
              <SelectValue placeholder="Sort" />
            </SelectTrigger>
            <SelectContent>
              {promptSorts.map((option) => (
                <SelectItem key={option.name} value={option.name}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select
            value={sortOrder}
            onValueChange={(value) => {
              if (value === "asc" || value === "desc") {
                setSortOrder(value);
              }
            }}
          >
            <SelectTrigger className="w-fit bg-white ml-2">
              <SelectValue placeholder="Order" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc">⬆️ ASC</SelectItem>
              <SelectItem value="desc">⬇️ DESC</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Separator />

        <ScrollArea className="h-full space-y-2">
          {displayedPrompts.length > 0 ? (
            displayedPrompts.map((prompt, index) => (
              <PromptLibrary key={index} prompt={prompt} />
            ))
          ) : (
            <EmptyPromptLibrarySection />
          )}
        </ScrollArea>

        <FeedbackButton />
      </div>
    </>
  );
};

export default RightBar;