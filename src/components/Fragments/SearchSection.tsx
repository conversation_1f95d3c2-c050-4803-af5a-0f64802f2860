import React from "react";
import Image from "next/image";
import addSection from "../../../public/Button → SVG.png";
import { Icon } from "../Icon";

interface SearchSectionProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  isInputFocused: boolean;
  setIsInputFocused: (focused: boolean) => void;
  newChatFolder: () => void;
  newChatRoom: () => void;
}

const SearchSection: React.FC<SearchSectionProps> = ({
  searchQuery,
  setSearchQuery,
  isInputFocused,
  setIsInputFocused,
  newChatFolder,
  newChatRoom,
}) => {
  return (
    <div className="flex w-full space-x-2 max-h-8 min-h-8 mb-3">
      <input
        type="text"
        placeholder="Search..."
        className="w-full h-full p-1 border border-[var(--border-custom-gray-300)] rounded"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        onFocus={() => setIsInputFocused(true)}
        onBlur={() => setIsInputFocused(false)}
      />
      {!isInputFocused && (
        <div className="flex space-x-2 text-primary-foreground">
          <button
            className="rounded-md bg-[#362168] h-full aspect-square flex justify-center items-center border"
            onClick={newChatRoom}
          >
            <Icon iconName="Add" />
          </button>
          <button
            className="rounded-md bg-[#362168] h-full aspect-square flex justify-center items-center border"
            onClick={newChatFolder}
          >
            <Image src={addSection} alt="Add Section" />
          </button>
        </div>
      )}
    </div>
  );
};

export default SearchSection;
