import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface TagFilterProps {
  initialTags?: string[];
  onApplyFilter?: (tags: string[]) => void;
}

export const TagFilter: React.FC<TagFilterProps> = ({
  initialTags = [],
  onApplyFilter,
}) => {
  const [newTag, setNewTag] = useState("");
  const [tags, setTags] = useState<string[]>(initialTags);

  const handleTagChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewTag(e.target.value);
  };

  const handleAddTag = () => {
    const trimmed = newTag.trim();
    if (trimmed && !tags.includes(trimmed)) {
      setTags([...tags, trimmed]);
      setNewTag("");
    }
  };

  const handleDeleteTag = (tag: string) => {
    setTags(tags.filter((t) => t !== tag));
  };

  const handleResetFilter = () => {
    setTags([]);
    onApplyFilter?.([]);
  };

  const handleApplyFilter = () => {
    onApplyFilter?.(tags);
  };

  return (
    <div className="p-2 w-full">
      <div className="flex items-center mb-4 gap-2">
        <Input
          value={newTag}
          onChange={handleTagChange}
          placeholder="Enter tag"
          className="bg-white"
        />
        <Button onClick={handleAddTag} variant="primary">
          Add
        </Button>
      </div>

      {tags.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <div
                key={tag}
                className="bg-muted text-sm px-3 py-1 rounded-full flex items-center gap-1"
              >
                <span>{tag}</span>
                <button
                  onClick={() => handleDeleteTag(tag)}
                  className="text-[var(--text-custom-red-500)] hover:text-[var(--text-custom-red-700)]"
                  aria-label={`Remove ${tag}`}
                >
                  &times;
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleResetFilter}
          className="text-muted-foreground"
        >
          Reset
        </Button>
        <Button onClick={handleApplyFilter} size="sm" className="text-sm">
          Apply Filter
        </Button>
      </div>
    </div>
  );
};
