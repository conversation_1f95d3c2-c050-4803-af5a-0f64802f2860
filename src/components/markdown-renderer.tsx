"use client";

import type React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import remarkBreaks from "remark-breaks";
import rehypeKatex from "rehype-katex";
import "katex/dist/katex.min.css";
import Image from "next/image";
import Link from "next/link";
import MermaidDiagram from "./mermaid-diagram";
import ChartRenderer from "./chart-renderer";
import CodeBlock from "./code-block";
import MathBlock from "./math-block";
import { convertLatexDelimiters } from "../../utils/math-utils";

interface MarkdownRendererProps {
  content: string;
  isChatMessage?: boolean;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  isChatMessage = false,
}) => {
  const processedContent = convertLatexDelimiters(content);

  return (
    <ReactMarkdown
      //className={`markdown-body prose prose-slate max-w-none dark:prose-invert ${isChatMessage ? "prose-sm" : ""}`}
      remarkPlugins={[remarkGfm, remarkMath, remarkBreaks]}
      rehypePlugins={[rehypeKatex]}
      components={{
        h1: ({ node, ...props }) => (
          <h1
            className="text-3xl font-bold mt-8 mb-4 pb-2 border-b border-[var(--border-custom-gray-200)]"
            {...props}
          />
        ),
        h2: ({ node, ...props }) => (
          <h2 className="text-2xl font-bold mt-6 mb-3" {...props} />
        ),
        h3: ({ node, ...props }) => (
          <h3 className="text-xl font-bold mt-5 mb-2" {...props} />
        ),
        h4: ({ node, ...props }) => (
          <h4 className="text-lg font-bold mt-4 mb-2" {...props} />
        ),
        p: ({ node, ...props }) => (
          <p className="my-2 leading-relaxed" {...props} />
        ),
        a: ({ node, href, ...props }) => (
          <Link
            href={href || "#"}
            className="text-[var(--text-custom-primary-600)] hover:text-[var(--text-custom-primary-800)] hover:underline transition-colors"
            {...props}
          />
        ),
        ul: ({ node, ...props }) => (
          <ul className="list-disc pl-6 my-4 space-y-2" {...props} />
        ),
        ol: ({ node, ...props }) => (
          <ol className="list-decimal pl-6 my-4 space-y-2" {...props} />
        ),
        li: ({ node, ...props }) => <li className="mb-1" {...props} />,
        blockquote: ({ node, ...props }) => (
          <blockquote
            className="border-l-4 border-[var(--border-custom-gray-300)] pl-4 italic dark:bg-[var(--bg-custom-gray-800)] rounded"
            {...props}
          />
        ),
        table: ({ node, ...props }) => (
          <div className="overflow-x-auto my-6">
            <table
              className="min-w-full divide-y divide-gray-200 border border-[var(--border-custom-gray-200)] rounded"
              {...props}
            />
          </div>
        ),
        thead: ({ node, ...props }) => (
          <thead className="bg-gray-50 dark:bg-[var(--bg-custom-gray-700)]" {...props} />
        ),
        th: ({ node, ...props }) => (
          <th
            className="px-4 py-3 text-left text-sm font-medium text-[var(--text-custom-gray-500)] dark:text-[var(--text-custom-gray-300)] uppercase tracking-wider border-b"
            {...props}
          />
        ),
        tr: ({ node, ...props }) => (
          <tr className="hover:bg-gray-50 dark:hover:bg-[var(--bg-custom-gray-800)]" {...props} />
        ),
        td: ({ node, ...props }) => (
          <td
            className="px-4 py-3 text-sm border-b border-[var(--border-custom-gray-200)] dark:border-[var(--border-custom-gray-700)]"
            {...props}
          />
        ),
        img: ({ node, src, alt, ...props }) => {
          if (!src) {
            return null;
          }

          const lowerSrc = src.toLowerCase();

          if (
            lowerSrc.endsWith(".mp3") ||
            lowerSrc.endsWith(".wav") ||
            lowerSrc.endsWith(".ogg")
          ) {
            return (
              <div className="my-4">
                <audio
                  controls
                  className="mx-auto rounded shadow-md"
                  style={{ maxWidth: "100%" }}
                >
                  <source src={src} />
                  Your browser does not support the audio element.
                </audio>
              </div>
            );
          }

          if (
            lowerSrc.endsWith(".mp4") ||
            lowerSrc.endsWith(".webm") ||
            lowerSrc.endsWith(".ogg")
          ) {
            return (
              <div className="my-4">
                <video
                  controls
                  className="mx-auto rounded shadow-md"
                  style={{ maxWidth: "100%", height: "auto" }}
                >
                  <source src={src} />
                  Your browser does not support the video element.
                </video>
              </div>
            );
          }

          return (
            <div className="my-4">
              <img
                src={src}
                alt={alt || "Image"}
                width={800}
                height={400}
                className="rounded-lg mx-auto"
                style={{ maxWidth: "100%", height: "auto" }}
              />
            </div>
          );
        },
        pre: ({ node, ...props }) => (
          // @ts-ignore: returning div for pre element
          // this avoid issue where pre inside p element
          <div {...props} />
        ),
        // @ts-ignore: inline does not exist
        code: ({ node, inline, className, children, ...props }) => {
          const match = /language-(\w+)/.exec(className || "");

          // Handle Mermaid diagrams
          if (!inline && match && match[1] === "mermaid") {
            return (
              <MermaidDiagram
                chart={String(children).replace(/\n$/, "")}
                className="not-inline-match-mermaid"
              />
            );
          }

          // Handle Chart data
          if (!inline && match && match[1] === "chart") {
            return (
              <ChartRenderer
                chartData={String(children).replace(/\n$/, "")}
                className="not-inline-match-chart"
              />
            );
          }

          // Handle math blocks
          if (!inline && match && match[1] === "math") {
            return (
              <div className="not-inline-match-math relative my-6 parse-from-code">
                <MathBlock formula={String(children).replace(/\n$/, "")} />
                <div className="katex-display">
                  <span className="katex-display-math">{String(children)}</span>
                </div>
              </div>
            );
          }

          if (match) {
            if (!inline) {
              return (
                <CodeBlock
                  language={match[1]}
                  code={String(children).replace(/\n$/, "")}
                  className="match-not-inline"
                />
              );
            } else {
              return (
                <code
                  className="match-inline px-1.5 py-0.5 rounded bg-[var(--bg-custom-gray-100)] dark:bg-[var(--bg-custom-gray-800)] font-mono text-sm"
                  {...props}
                >
                  {children}
                </code>
              );
            }
          }

          const text = String(children).replace(/\n$/, "");
          const kMAX_LENGTH_FOR_INLINE = 100;
          const isInline = text.length < kMAX_LENGTH_FOR_INLINE;

          if (isInline) {
            return (
              <code
                className="not-match-inline px-1.5 py-0.5 rounded bg-[var(--bg-custom-gray-100)] dark:bg-[var(--bg-custom-gray-800)] font-mono text-sm"
                {...props}
              >
                {children}
              </code>
            );
          }
          return (
            <CodeBlock
              language="text"
              code={String(children).replace(/\n$/, "")}
              className={`not-match`}
            />
          );
        },
        hr: ({ node, ...props }) => (
          <hr
            className="my-6 border-t border-[var(--border-custom-gray-200)] dark:border-[var(--border-custom-gray-700)]"
            {...props}
          />
        ),
        strong: ({ node, ...props }) => (
          <strong className="font-bold" {...props} />
        ),
        em: ({ node, ...props }) => <em className="italic" {...props} />,
        // Add support for math inline elements
        // @ts-ignore: math does not exist
        math: ({ value }) => {
          return (
            <span className="inline-math parse-from-math">
              <span className="katex-inline">{value}</span>
            </span>
          );
        },
        // Add support for math block elements
        // @ts-ignore: value as any
        inlineMath: ({ value }) => {
          return (
            <span className="inline-math parse-inline-math">
              <span className="katex-inline">{value}</span>
              <MathBlock formula={value} isInline={true} />
            </span>
          );
        },
      }}
    >
      {processedContent}
    </ReactMarkdown>
  );
};

export default MarkdownRenderer;
