"use client";

import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface HttpActionField {
  id: string;
  label: string;
  placeholder?: string;
  type: "text" | "textarea" | "select";
  options?: string[];
  value: string;
}

export interface HTTPAction {
  httpMethod: string;
  endpointURL: string;
  requestHeaders: string;
  requestBody: string;
  requestBodyFormat: string;
  postProcessing: string;
  testVariables: string;
}

export const HttpActionInput: React.FC<{
  httpAction: HTTPAction;
  onChange: (value: HTTPAction) => void;
}> = ({ httpAction, onChange }) => {
  const [http, setHttp] = useState<HTTPAction>({} as HTTPAction);
  const [showHeaders, setShowHeaders] = useState(false);
  const [showBody, setShowBody] = useState(false);
  const [showPostProcessing, setShowPostProcessing] = useState(false);
  const [showTestVariables, setShowTestVariables] = useState(false);

  const handleCheckboxChange = (name: string, checked: boolean) => {
    if (name === "showHeaders") setShowHeaders(checked);
    if (name === "showBody") setShowBody(checked);
    if (name === "showPostProcessing") setShowPostProcessing(checked);
    if (name === "showTestVariables") setShowTestVariables(checked);
  };

  useEffect(() => {
    setHttp(httpAction);
  }, [httpAction]);

  const handleInputChange = (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setHttp((prev) => ({ ...prev, [name]: value }));
    onChange({ ...httpAction, [name]: value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setHttp((prev) => ({ ...prev, [name]: value }));
    onChange({ ...httpAction, [name]: value });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label className="mb-1 block">HTTP Method</Label>
          <Select
            value={http.httpMethod}
            onValueChange={(value) => handleSelectChange("httpMethod", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select HTTP Method" />
            </SelectTrigger>
            <SelectContent>
              {["GET", "POST", "PUT", "DELETE"].map((method) => (
                <SelectItem key={method} value={method}>
                  {method}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label className="mb-1 block">Endpoint URL</Label>
          <Input
            name="endpointURL"
            value={http.endpointURL}
            onChange={handleInputChange}
            placeholder="https://yourapi.example.com/last-10-tweets.json"
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-4">
        {[
          { id: "showHeaders", label: "Request Headers", checked: showHeaders },
          {
            id: "showBody",
            label: "Request Body",
            checked: showBody,
            disabled: http.httpMethod !== "POST" && http.httpMethod !== "PUT",
          },
          {
            id: "showPostProcessing",
            label: "Post-Processing",
            checked: showPostProcessing,
          },
          {
            id: "showTestVariables",
            label: "Test Variables",
            checked: showTestVariables,
          },
        ].map(({ id, label, checked, disabled }) => (
          <div key={id} className="flex items-center space-x-2">
            <Checkbox
              id={id}
              checked={checked}
              onCheckedChange={(val) => handleCheckboxChange(id, Boolean(val))}
              disabled={disabled}
            />
            <Label htmlFor={id}>{label}</Label>
          </div>
        ))}
      </div>

      {showHeaders && (
        <div>
          <Label className="mb-1 block">Request Headers (JSON)</Label>
          <Textarea
            name="requestHeaders"
            value={http.requestHeaders}
            onChange={handleInputChange}
            placeholder='{ "Content-Type": "application/json" }'
            rows={4}
          />
        </div>
      )}
      {showBody && (
        <>
          <div>
            <Label className="mb-1 block">Request Body (JSON)</Label>
            <Textarea
              name="requestBody"
              value={http.requestBody}
              onChange={handleInputChange}
              placeholder='{ "userID": "{userID}" }'
              rows={4}
            />
          </div>
          <div>
            <Label className="mb-1 block">Request Body Format</Label>
            <Select
              value={http.requestBodyFormat}
              onValueChange={(value) =>
                handleSelectChange("requestBodyFormat", value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Format" />
              </SelectTrigger>
              <SelectContent>
                {["JSON", "FormData"].map((format) => (
                  <SelectItem key={format} value={format}>
                    {format}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </>
      )}
      {showPostProcessing && (
        <div>
          <Label className="mb-1 block">Post-Processing</Label>
          <Textarea
            name="postProcessing"
            value={http.postProcessing}
            onChange={handleInputChange}
            placeholder="JMESPath Transform Expression"
            rows={4}
          />
        </div>
      )}
      {showTestVariables && (
        <div>
          <Label className="mb-1 block">Test Variables (JSON)</Label>
          <Textarea
            name="testVariables"
            value={http.testVariables}
            onChange={handleInputChange}
            placeholder='{ "param1": "test value" }'
            rows={4}
          />
        </div>
      )}

      <div className="flex justify-between items-center mt-4">
        <Button>Send Test Request</Button>
        <div className="flex items-center space-x-2">
          <Checkbox id="addTestVariable" />
          <Label htmlFor="addTestVariable">Add Test Variable</Label>
        </div>
      </div>
    </div>
  );
};

export default HttpActionInput;
