import React, { useState, useMemo } from "react";
import LeftPane from "./LeftPane";
import RightPane from "./RightPane";
import { usePopup } from "@/context/usePopUp";
import { Plugin } from "@/model/plugin";
import { PopupCloseButton } from "../Popup";

const plugins: Plugin[] = [
  {
    name: "Web Search",
    icon: "🔍",
    overview: "",
    settings: [],
    source: "",
    spec: "",
    id: "web",
  },
  {
    id: "dalle",
    name: "DALL-E",
    icon: "👁️",
    overview: "",
    settings: [],
    source: "",
    spec: "",
  },
];

export const PluginSettings: React.FC = () => {
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [isNewPlugin, setIsNewPlugin] = useState(false);

  const handleSelectPlugin = (plugin: Plugin | null) => {
    setSelectedPlugin(plugin);
  };

  const handleImport = () => {
    if (selectedPlugin) {
      console.log(`Importing ${selectedPlugin.name}`);
    }
  };

  const handleNewPlugin = () => {
    setIsNewPlugin(true);
  };

  return (
    <div
      id="model-setting"
      className="relative w-5/6 h-5/6 rounded-lg bg-white overflow-clip flex"
    >
      <PopupCloseButton />
      <LeftPane
        plugins={plugins}
        selectedPlugin={selectedPlugin}
        onSelectPlugin={(e) => {
          setIsNewPlugin(false);
          handleSelectPlugin(e);
        }}
        onImportPlugin={handleImport}
        onNewPlugin={handleNewPlugin}
      />
      {/* This should not be relative-absolute, but removing this will cause the layout broke */}
      {/* Until we figure it out, we keep this logic */}
      <div className="relative flex flex-col h-full w-3/4 pt-16 px-2">
        <div className="absolute left-5 top-0 min-h-16 flex items-center">
          <h3 className="font-bold ">{selectedPlugin?.name || "New Plugin"}</h3>
        </div>
        <RightPane
          selectedPlugin={isNewPlugin ? "New" : selectedPlugin}
          onImport={handleImport}
        />
      </div>
    </div>
  );
};
