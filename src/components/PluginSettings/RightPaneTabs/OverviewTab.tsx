import React from "react";
import { Plugin } from "@/model/plugin";
import MarkdownContent from "@/components/MarkdownContent";

const OverviewTab: React.FC<{ plugin: Plugin }> = ({ plugin }) => {
  const markdownContent = plugin.overview;

  return (
    <div className="prose dark:prose-invert">
      <MarkdownContent content={markdownContent}></MarkdownContent>
    </div>
  );
};

export default OverviewTab;
