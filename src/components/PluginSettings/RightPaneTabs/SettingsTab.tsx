import React, { useState } from "react";
import { Plugin } from "@/model/plugin";

const SettingsTab: React.FC<{ plugin: Plugin }> = ({ plugin }) => {
  const [config, setConfig] = useState({
    someSetting: "default",
    enableFeature: false,
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type, checked } = e.target as any;
    setConfig((prevConfig) => ({
      ...prevConfig,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  return (
    <div>
      <h3 className="font-semibold">Settings</h3>
      <div className="mt-4">
        <label className="block text-sm font-medium">
          Some Setting
          <input
            type="text"
            name="someSetting"
            value={config.someSetting}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-[var(--border-custom-gray-300)] shadow-sm focus:border-[var(--border-custom-blue-300)] focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
        </label>
        <label className="block mt-4 flex items-center">
          <input
            type="checkbox"
            name="enableFeature"
            checked={config.enableFeature}
            onChange={handleChange}
            className="rounded border-[var(--border-custom-gray-300)] text-[var(--text-custom-blue-600)] shadow-sm focus:border-[var(--border-custom-blue-300)] focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
          <span className="ml-2">Enable Feature</span>
        </label>
      </div>
    </div>
  );
};

export default SettingsTab;
