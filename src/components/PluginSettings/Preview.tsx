import React from "react";
import { TabView } from "../TabView";
import OverviewTab from "./RightPaneTabs/OverviewTab";
import SettingsTab from "./RightPaneTabs/SettingsTab";
import SpecsTab from "./RightPaneTabs/SpecsTab";
import SourceTab from "./RightPaneTabs/SourceTab";
import { Plugin } from "@/model/plugin";

interface PreviewProps {
  plugin: Plugin;
}

export const Preview: React.FC<PreviewProps> = ({ plugin }) => {
  return (
    <div className="w-full flex flex-col">
      <TabView
        tabs={[
          {
            id: "overview",
            title: "Overview",
            content: <OverviewTab plugin={plugin} />,
          },
          {
            id: "settings",
            title: "Settings",
            content: <SettingsTab plugin={plugin} />,
          },
          {
            id: "specs",
            title: "Specs",
            content: <SpecsTab plugin={plugin} />,
          },
          {
            id: "source",
            title: "Source",
            content: <SourceTab plugin={plugin} />,
          },
        ]}
      />
    </div>
  );
};
