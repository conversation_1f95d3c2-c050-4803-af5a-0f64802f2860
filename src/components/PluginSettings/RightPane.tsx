import React from "react";
import { TabView } from "../TabView";
import OverviewTab from "./RightPaneTabs/OverviewTab";
import SettingsTab from "./RightPaneTabs/SettingsTab";
import SpecsTab from "./RightPaneTabs/SpecsTab";
import SourceTab from "./RightPaneTabs/SourceTab";

import { Plugin } from "@/model/plugin";
import Form from "./Form";
import { Preview } from "./Preview";

interface RightPaneProps {
  selectedPlugin: Plugin | null | "New";
  onImport: () => void;
}

const RightPane: React.FC<RightPaneProps> = ({ selectedPlugin, onImport }) => {
  if (selectedPlugin === "New") {
    return (
      <div className="h-full w-full">
        <Form onSubmit={(data) => {}} />
      </div>
    );
  }

  if (selectedPlugin === null) {
    return <div>Select a plugin to view details</div>;
  }

  return (
    <div className="w-full flex flex-col">
      <Preview plugin={selectedPlugin} />
    </div>
  );
};

export default RightPane;
