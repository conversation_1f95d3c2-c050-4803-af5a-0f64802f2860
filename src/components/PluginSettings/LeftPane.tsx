'use client';

import React, { useState } from 'react';
import { Plugin } from '@/model/plugin';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Menu } from 'lucide-react';

interface LeftPaneProps {
  plugins: Plugin[];
  selectedPlugin: Plugin | null;
  onSelectPlugin: (plugin: Plugin | null) => void;
  onNewPlugin: () => void;
  onImportPlugin: () => void;
}

const groupBy = (plugins: Plugin[], criteria: 'status') => {
  const groups: { [key: string]: Plugin[] } = {};
  plugins.forEach((plugin) => {
    const key =
      criteria === 'status' ? (true ? 'Installed' : 'Available') : 'Unknown';
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(plugin);
  });
  return groups;
};

const LeftPane: React.FC<LeftPaneProps> = ({
  plugins,
  selectedPlugin,
  onSelectPlugin,
  onNewPlugin,
  onImportPlugin,
}) => {
  const [grouping, setGrouping] = useState<'status'>('status');
  const [isOpen, setIsOpen] = useState(false);
  const groupedPlugins = groupBy(plugins, grouping);

  return (
    <>
      {/* Mobile Toggle Button */}
      <div className='md:hidden fixed top-4 left-4 z-50'>
        <Button
          variant='ghost'
          size='icon'
          onClick={() => setIsOpen(!isOpen)}
          className='bg-background'
        >
          <Menu className='h-4 w-4' />
        </Button>
      </div>

      {/* Left Pane */}
      <div
        className={`
          fixed md:relative
          top-0 left-0
          w-[280px] md:w-72
          h-full
          bg-background
          border-r
          transition-transform duration-300 ease-in-out
          z-40
          ${isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
        `}
      >
        <div className='p-4 flex flex-col justify-between h-full'>
          <div className='flex flex-col space-y-4'>
            <div>
              <label className='mt-10 md:mt-0 block text-sm font-medium mb-1'>Group By</label>
              <Select
                value={grouping}
                onValueChange={(value) => setGrouping(value as 'status')}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Group by' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='status'>Status</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <ScrollArea className='flex flex-col space-y-6'>
              {Object.entries(groupedPlugins).map(([group, plugins]) => (
                <div key={group}>
                  <h3 className='text-sm font-semibold text-muted-foreground mb-2'>
                    {group}
                  </h3>
                  <div className='space-y-1'>
                    {plugins.map((plugin) => (
                      <Button
                        key={plugin.name}
                        variant={
                          selectedPlugin?.name === plugin.name
                            ? 'secondary'
                            : 'ghost'
                        }
                        className='w-full justify-start gap-2'
                        onClick={() => {
                          onSelectPlugin(plugin);
                          setIsOpen(false); // Close sidebar on mobile after selection
                        }}
                      >
                        <span className='w-4 h-4 flex items-center justify-center'>
                          {plugin.icon.startsWith('http') ? (
                            <img
                              src={plugin.icon}
                              alt={plugin.name}
                              className='w-full h-full rounded object-contain'
                            />
                          ) : (
                            <span>{plugin.icon}</span>
                          )}
                        </span>
                        <span>{plugin.name}</span>
                      </Button>
                    ))}
                  </div>
                  <Separator className='my-3' />
                </div>
              ))}
            </ScrollArea>
          </div>

          <div className='flex flex-col space-y-2 pt-4 border-t mt-4'>
            <Button
              onClick={() => {
                onNewPlugin();
                setIsOpen(false);
              }}
            >
              New Plugin
            </Button>
            <Button
              variant='primary-outline'
              onClick={() => {
                onImportPlugin();
                setIsOpen(false);
              }}
            >
              Import Plugin
            </Button>
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className='fixed inset-0 bg-black/50 z-30 md:hidden'
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};

export default LeftPane;
