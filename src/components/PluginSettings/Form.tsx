import React, { useMemo, useState } from "react";
import HttpActionInput, { HTTPAction } from "./HttpActionInput";
import { Preview } from "./Preview";
import { Plugin } from "@/model/plugin";
import CodeEditor from "../CodeEditor";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface FormProps {
  onSubmit: (data: any) => void;
}

const Form: React.FC<FormProps> = ({ onSubmit }) => {
  const [formData, setFormData] = useState([
    {
      id: "iconURL",
      label: "Plugin Icon (Drag & Drop)",
      type: "file",
      value: "",
    },
    {
      id: "pluginName",
      label: "Plugin Name*",
      placeholder: "E.g. Link Reader",
      type: "text",
      value: "",
    },
    {
      id: "overview",
      label: "Overview (Markdown Supported)",
      placeholder:
        "Describe what your plugin does. E.g. This plugin fetches link previews.",
      type: "textarea",
      value: "",
    },
    {
      id: "functionSpec",
      label: "OpenAI Function Spec*",
      placeholder: '{ "name": "get_link_preview", ... }',
      type: "textarea",
      value: "",
    },
    {
      id: "userSettings",
      label: "User Settings (JSON, Optional)",
      placeholder: '{ "apiKey": "your_api_key" }',
      type: "textarea",
      value: "",
    },
    {
      id: "implementationType",
      label: "Implementation Type",
      type: "select",
      options: ["Javascript", "Http Action"],
      value: "Javascript",
      children: "",
    },
    {
      id: "outputTarget",
      label: "Output Options",
      type: "select",
      options: ["Give Output to AI", "Render as Markdown", "Render as HTML"],
      value: "Give Output to AI",
    },
  ]);

  const find = (
    id: string,
    key: "value" | "children" = "value",
    defaultValue: string = ""
  ) => {
    const item = formData.find((e: any) => e.id === id);
    return key === "value"
      ? item?.value || defaultValue
      : item?.children || defaultValue;
  };

  const formDataAsPlugin = useMemo<Plugin>(() => {
    return {
      name: find("pluginName"),
      icon: find("iconURL"),
      overview: find("overview"),
      settings: [],
      spec: find("functionSpec"),
      source: find("implementationType", "children"),
      id: "",
    };
  }, [formData]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { id, value } = e.target;
    setFormData((prev: any) =>
      prev.map((field: any) => (field.id === id ? { ...field, value } : field))
    );
  };

  const handleSelectChange = (id: string, value: string) => {
    setFormData((prev: any) =>
      prev.map((field: any) => (field.id === id ? { ...field, value } : field))
    );
  };

  const handleIconUpload = (file: File) => {
    const url = URL.createObjectURL(file);
    setFormData((prev: any) =>
      prev.map((field: any) =>
        field.id === "iconURL" ? { ...field, value: url } : field
      )
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const renderField = (field: (typeof formData)[0]) => {
    switch (field.type) {
      case "textarea":
        return (
          <div key={field.id} className="space-y-1">
            <Label htmlFor={field.id}>{field.label}</Label>
            <Textarea
              id={field.id}
              value={field.value}
              onChange={handleInputChange}
              placeholder={field.placeholder}
              rows={4}
            />
          </div>
        );

      case "select":
        return (
          <div key={field.id} className="space-y-1">
            <Label htmlFor={field.id}>{field.label}</Label>
            <Select
              value={field.value}
              onValueChange={(value) => handleSelectChange(field.id, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {field.value === "Javascript" && (
              <CodeEditor
                value={field.children || ""}
                onChange={(code) =>
                  setFormData((prev: any) =>
                    prev.map((e: any) =>
                      e.id === field.id ? { ...e, children: code } : e
                    )
                  )
                }
              />
            )}
            {field.value === "Http Action" && (
              <HttpActionInput
                httpAction={field.children as any as HTTPAction}
                onChange={(data) =>
                  setFormData((prev: any) =>
                    prev.map((e: any) =>
                      e.id === field.id ? { ...e, children: data } : e
                    )
                  )
                }
              />
            )}
          </div>
        );

      case "file":
        return (
          <div
            key={field.id}
            className="flex flex-col items-center justify-center border-dashed border-2 border-muted-foreground/40 rounded-md p-4 text-center hover:border-primary transition"
            onDragOver={(e: any) => e.preventDefault()}
            onDrop={(e: any) => {
              e.preventDefault();
              const file = e.dataTransfer.files[0];
              if (file?.type.startsWith("image/")) handleIconUpload(file);
            }}
          >
            <p className="mb-2 font-medium">{field.label}</p>
            {field.value ? (
              <img
                src={field.value}
                alt="Uploaded Icon"
                className="w-16 h-16 object-contain"
              />
            ) : (
              <p className="text-sm text-muted-foreground">
                Drag & drop your icon here, or click to upload
              </p>
            )}
            <Input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e: any) => {
                if (e.target.files?.[0]) {
                  handleIconUpload(e.target.files[0]);
                }
              }}
            />
          </div>
        );

      default:
        return (
          <div key={field.id} className="space-y-1">
            <Label htmlFor={field.id}>{field.label}</Label>
            <Input
              id={field.id}
              value={field.value}
              onChange={handleInputChange}
              placeholder={field.placeholder}
            />
          </div>
        );
    }
  };

  return (
    <div className="flex h-full">
      <form
        onSubmit={handleSubmit}
        className="flex flex-col space-y-6 p-6 w-1/2 overflow-y-auto"
      >
        {formData.map(renderField)}

        <Button type="submit" className="w-fit">
          Save Plugin
        </Button>
      </form>

      <div className="w-1/2 p-6 overflow-y-auto">
        <Preview plugin={formDataAsPlugin} />
      </div>
    </div>
  );
};

export default Form;
