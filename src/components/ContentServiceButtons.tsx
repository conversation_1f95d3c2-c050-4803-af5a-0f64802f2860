import { Role } from "@/model/sendMessage";
import React from "react";
import { twMerge } from "tailwind-merge";

interface ContentServiceButtonProps {
  role: Role;
  contentService: ContentServiceItem[];
  content: string;
  messageId: string;
}

const ContentServiceButtons: React.FC<ContentServiceButtonProps> = ({ role, contentService }) => {
  const isAssistant = role === "assistant";

  return (
    <div className={twMerge("flex group/message duration-300 ease-in-out p-2 gap-2 rounded-b-xl opacity-0 group-hover/message:opacity-100", isAssistant ? "bg-[#E2DCF0]" : "bg-white")}>
      {contentService.map((item, id) => (
        <div
          className={`rounded-md ${isAssistant ? "bg-white" : "border border-[#362167]"}`}
          key={id}
        >
          <button className="first-line:text-[10px] font-normal px-3 py-1 rounded-md" onClick={item.onClick}>
            {item.label}
          </button>
        </div>
      ))}
    </div>
  );
};

interface ContentServiceItem {
  label: string;
  onClick: () => void;
}

export type { ContentServiceItem };
export default ContentServiceButtons;