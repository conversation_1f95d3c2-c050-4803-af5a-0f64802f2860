"use client";

import type React from "react";

import { useEffect, useRef, useState } from "react";
import mermaid from "mermaid";
import { Copy, Download, ExternalLink, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface MermaidDiagramProps {
  chart: string;
  className?: string;
}

const MermaidDiagram: React.FC<MermaidDiagramProps> = ({
  chart,
  className = "",
}) => {
  const [svg, setSvg] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // Track loading state
  const mermaidRef = useRef<HTMLDivElement>(null);
  const [id] = useState(
    `mermaid-${Math.random().toString(36).substring(2, 11)}`
  );

  useEffect(() => {
    // Initialize mermaid with configuration
    mermaid.initialize({
      startOnLoad: true,
      theme: document.documentElement.classList.contains("dark")
        ? "dark"
        : "default",
      securityLevel: "loose",
      fontFamily: "inherit",
      suppressErrorRendering: true,
    });

    const renderDiagram = async () => {
      if (!chart) return;

      setIsLoading(true); // Start loading when rendering begins
      try {
        // Clean the chart definition
        const cleanChart = chart.trim();

        // Render the diagram
        const { svg } = await mermaid.render(id, cleanChart);
        setSvg(svg);
        setError(null);
      } catch (err) {
        console.error("Mermaid rendering error:", err);
        setError("Failed to render diagram. Please check your syntax.");
      } finally {
        setIsLoading(false); // Stop loading after rendering attempt
      }
    };

    renderDiagram();

    // Re-render when theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "class" &&
          (document.documentElement.classList.contains("dark") ||
            !document.documentElement.classList.contains("dark"))
        ) {
          renderDiagram();
        }
      });
    });

    observer.observe(document.documentElement, { attributes: true });

    return () => {
      observer.disconnect();
    };
  }, [chart, id]);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(chart);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleExportImage = () => {
    if (!svg) return;

    // Create a canvas element
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Create an image from the SVG
    const img = new Image();
    const svgBlob = new Blob([svg], { type: "image/svg+xml;charset=utf-8" });
    const url = URL.createObjectURL(svgBlob);

    img.onload = () => {
      // Set canvas dimensions
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw white background
      ctx.fillStyle = "white";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw the image
      ctx.drawImage(img, 0, 0);

      // Convert to PNG and download
      canvas.toBlob((blob) => {
        if (!blob) return;

        const downloadUrl = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = downloadUrl;
        a.download = "diagram.png";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(downloadUrl);
      });

      URL.revokeObjectURL(url);
    };

    img.src = url;
    img.crossOrigin = "anonymous";
  };

  const handleOpenEditor = () => {
    const encodedDef = encodeURIComponent(chart);
    window.open(`https://mermaid.live/edit#${encodedDef}`, "_blank");
  };

  const handleReload = () => {
    setSvg(""); // Clear previous diagram
    setError(null); // Reset any error state
    // Trigger re-render
    mermaid.init();
  };

  if (error) {
    return (
      <div className="p-4 border border-[var(--border-custom-red-300)] bg-red-50 dark:bg-[var(--bg-custom-red-900)]/20 dark:border-[var(--border-custom-red-800)] rounded-md text-[var(--text-custom-red-600)] dark:text-[var(--text-custom-red-400)]">
        <p className="font-medium">Diagram Error</p>
        <p className="text-sm">{error}</p>
        <pre className="mt-2 p-2 bg-[var(--bg-custom-gray-100)] dark:bg-[var(--bg-custom-gray-800)] rounded text-xs overflow-x-auto">
          {chart}
        </pre>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="absolute right-2 top-2 z-10 flex gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/90 dark:bg-[var(--bg-custom-gray-800)]/90 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-700)] border-none"
                onClick={handleCopy}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Copy Mermaid code</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/90 dark:bg-[var(--bg-custom-gray-800)]/90 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-700)] border-none"
                onClick={handleExportImage}
              >
                <Download className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Export as PNG</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/90 dark:bg-[var(--bg-custom-gray-800)]/90 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-700)] border-none"
                onClick={handleOpenEditor}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Open in Mermaid Live Editor</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {/* Reload button */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/90 dark:bg-[var(--bg-custom-gray-800)]/90 hover:bg-[var(--bg-custom-gray-100)] dark:hover:bg-[var(--bg-custom-gray-700)] border-none"
                onClick={handleReload}
                disabled={isLoading} // Disable while loading
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Reload Diagram</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div
        ref={mermaidRef}
        className={`mermaid-diagram overflow-auto my-4 p-4 bg-gray-50 dark:bg-[var(--bg-custom-gray-800)] rounded-lg border border-[var(--border-custom-gray-200)] dark:border-[var(--border-custom-gray-700)] ${className}`}
        dangerouslySetInnerHTML={{ __html: svg }}
      />
    </div>
  );
};

export default MermaidDiagram;
