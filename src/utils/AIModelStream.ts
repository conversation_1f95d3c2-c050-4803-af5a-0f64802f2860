import {
  create<PERSON><PERSON><PERSON>,
  ParsedEvent,
  ReconnectInterval,
} from "eventsource-parser";
import { FinishReason, ModelStreamResponse } from "./ModelStreamResponse";
import { MessageToolCall, SendMessage } from "@/model/sendMessage";

export type TomioAgent = "user" | "system" | "assistant";

export interface ToolFunction {
  name: string;
  strict: boolean;
  parameters: {
    type: string;
    properties: Record<string, { type: string; enum?: string[] }>;
    required: string[];
    additionalProperties: boolean;
  };
}

export interface Tool {
  type: string;
  function: ToolFunction;
}

type ToolChoice =
  | "auto"
  | "required"
  | { type: "function"; function: { name: string } };

export interface AIModelStreamPayload {
  model: string;
  messages: SendMessage[];
  temperature: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  max_tokens: number;
  stream: boolean;
  logprobs?: number;
  stop?: string[];
  user?: string;
  n: number;
  tools?: Tool[];
  tool_choice?: ToolChoice;
}

export async function AIModelStream(
  endpoint: string,
  headers: Record<string, string>,
  payload: AIModelStreamPayload,
  signal: AbortSignal
) {
  const decoder = new TextDecoder();

  let counter = 0;
  let collectedToolCalls: {
    [index: number]: MessageToolCall;
  } = {};
  let finishReason: FinishReason = null;

  const requestHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    ...headers,
  };

  const body = JSON.stringify(payload);

  const res = await fetch(endpoint, {
    headers: requestHeaders,
    method: "POST",
    body: body,
    signal: signal,
  });

  if (!res.ok) {
    console.error(
      "Failed to fetch. Status:",
      res.status,
      "StatusText:",
      res.statusText
    );
    throw new Error("Fetch failed");
  }

  const stream = new ReadableStream<{
    text: string | null;
    toolCalls?: { [index: number]: MessageToolCall };
    is_finished: FinishReason;
  }>({
    async start(controller) {
      const parser = createParser(onParse);

      function onParse(event: ParsedEvent | ReconnectInterval) {
        if (event.type === "event") {
          const data = event.data;
          if (data === "[DONE]") {
            if (Object.keys(collectedToolCalls).length > 0) {
              controller.enqueue({
                text: null,
                toolCalls: collectedToolCalls,
                is_finished: "tool_calls",
              });
            }
            controller.close();
            return;
          }
          try {
            const json: ModelStreamResponse = JSON.parse(data);
            const text = json.choices[0].delta?.content;
            const toolCallsDelta = json.choices[0].delta?.tool_calls;
            finishReason = json.choices[0].finish_reason;

            if (toolCallsDelta) {
              toolCallsDelta.forEach((call) => {
                const { index, function: func } = call;
                if (func) {
                  const existingCall = collectedToolCalls[index] || {
                    id: call.id || "",
                    type: "function",
                    function: {
                      arguments: func.arguments || "",
                      name: func.name || "",
                    },
                  };
                  collectedToolCalls[index] = {
                    id: call.id || existingCall.id,
                    type: call.type || existingCall.type,
                    function: {
                      name: func.name || existingCall.function.name,
                      arguments:
                        existingCall.function.arguments + func.arguments,
                    },
                  };
                }
              });
            }

            if (text !== null) {
              controller.enqueue({ text: text, is_finished: finishReason });
            }

            counter++;
          } catch (e) {
            controller.error(e);
          }
        }
      }

      const reader = res.body?.getReader();
      if (!reader) {
        throw new Error("Failed to get reader from response body");
      }

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        parser.feed(decoder.decode(value, { stream: true }));
      }

      if (finishReason === "length") {
        console.log(
          "Model stopped due to max token limit. Consider continuing the conversation."
        );
        // You could trigger a continuation logic here, e.g., refetch with the last message.
      }
    },
  });
  return stream;
}
