import { SendMessage } from "@/model/sendMessage";
import {
  AIModelStreamPayload,
  AIModelStream as DefaultAIModelStream,
  Tool,
} from "./AIModelStream";
import { CustomError, REQUIRED_KEY_NOT_FOUND } from "@/model/custom_error";
import { scriptRegistry, ModelVendorAPI } from "./scripts_ai";
import { OpenAIScript } from "./scripts_ai/openai";

export type AIModelStreamFunction = typeof DefaultAIModelStream;

export class AIHandler {
  private endpoint: string;
  private headers: Record<string, string>;
  private keepLocal: { required: string[]; optional: string[] };
  private model: string;
  private temperature: number;
  private topP: number;
  private frequencyPenalty: number;
  private presencePenalty: number;
  private maxTokens: number;
  private n: number;
  private tools?: Tool[];
  private model_vendor_api: ModelVendorAPI;
  private fn_aiModelStream: AIModelStreamFunction;

  constructor(
    endpoint: string,
    headers: Record<string, string>,
    keepLocal: { required: string[]; optional: string[] },
    model: string = "gpt-4o",
    temperature: number = 0.7,
    topP: number = 1.0,
    frequencyPenalty: number = 0.0,
    presencePenalty: number = 0.0,
    maxTokens: number = 4096,
    n: number = 1,
    tools?: Tool[],
    model_vendor_api: ModelVendorAPI = "openai",
    fn_aiModelStream: AIModelStreamFunction = DefaultAIModelStream
  ) {
    this.endpoint = endpoint;
    this.headers = headers;
    this.keepLocal = keepLocal;
    this.model = model;
    this.temperature = temperature;
    this.topP = topP;
    this.frequencyPenalty = frequencyPenalty;
    this.presencePenalty = presencePenalty;
    this.maxTokens = maxTokens;
    this.n = n;
    this.tools = tools;
    this.model_vendor_api = model_vendor_api;
    this.fn_aiModelStream = fn_aiModelStream;
  }

  async completion(
    params: { messages: SendMessage[]; user: string },
    abortSignal: AbortSignal
  ) {
    try {
      const apiKeys = this.__getApiKeys();

      const script = scriptRegistry[this.model_vendor_api] || OpenAIScript;

      // Create request using the script
      const request = script.createRequest(
        {
          endpoint: this.endpoint,
          model: this.model,
          headers: this.headers,
          resolvedKeys: apiKeys,
          placeholders: this.keepLocal,
        },
        {
          messages: params.messages,
          tools: this.tools,
          stream: true,
          topP: this.topP,
          frequencyPenalty: this.frequencyPenalty,
          presencePenalty: this.presencePenalty,
          n: this.n,
          temperature: this.temperature,
          maxTokens: this.maxTokens,
          user: params.user,
        }
      );

      // Use the injected fn_aiModelStream function instead of the global one
      return this.fn_aiModelStream(
        request.endpoint,
        request.headers,
        request.body,
        abortSignal
      );
    } catch (e) {
      throw e;
    }
  }
  __getApiKeys(): Record<string, string> {
    const requiredKeys = this.keepLocal.required;
    const optionalKeys = this.keepLocal.optional;

    const mapKeysWithResolvedValues: Record<string, string> = {}; // Object to store resolved keys

    // Function to get local storage items for the specified keys
    const getLocalStorageItems = (keys: string[]) => {
      return keys.reduce((acc, key) => {
        const value = localStorage.getItem(key);
        acc[key] = value;
        return acc;
      }, {} as Record<string, string | null>);
    };

    // Get the local storage items for required and optional keys
    const requiredLocals = getLocalStorageItems(requiredKeys);
    const optionalLocals = getLocalStorageItems(optionalKeys);

    // Check for required keys and throw error if not found
    requiredKeys.forEach((key) => {
      const value = requiredLocals[key];
      if (!value) {
        throw new CustomError(
          `Required local storage item '${key}' not found.`,
          REQUIRED_KEY_NOT_FOUND
        );
      }
      mapKeysWithResolvedValues[key] = value; // Store the resolved required key
    });

    // Add the optional keys if they exist
    optionalKeys.forEach((key) => {
      const value = optionalLocals[key];
      if (value) {
        mapKeysWithResolvedValues[key] = value; // Store the resolved optional key if present
      }
      // If optional key is not present, do not throw an error
    });

    return mapKeysWithResolvedValues;
  }
}
