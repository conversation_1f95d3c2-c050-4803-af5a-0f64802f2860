interface FunctionDetails {
  arguments: string;
  name?: string;
}

interface ToolCall {
  id?: string;
  index: number;
  function?: FunctionDetails;
  type?: "function";
}

interface Delta {
  content: string | null;
  role?: string;
  tool_calls?: ToolCall[];
}

interface ModelStreamChoice {
  finish_reason: FinishReason;
  index: number;
  logprobs: any | null;
  delta: Delta;
}

export interface ModelStreamResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  system_fingerprint: string;
  choices: ModelStreamChoice[];
}

export type FinishReason = "stop" | "length" | "tool_calls" | null;
