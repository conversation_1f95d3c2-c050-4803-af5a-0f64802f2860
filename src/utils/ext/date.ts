export const dateFormat = function (date: Date, pattern: string): string {
  const zeroPad = (num: number, places: number): string =>
    String(num).padStart(places, "0");
  const year = date.getFullYear();
  const month = zeroPad(date.getMonth() + 1, 2); // Months are 0-based
  const day = zeroPad(date.getDate(), 2);
  const hours = zeroPad(date.getHours(), 2);
  const minutes = zeroPad(date.getMinutes(), 2);
  const seconds = zeroPad(date.getSeconds(), 2);

  return pattern
    .replace(/YYYY/g, String(year))
    .replace(/MM/g, month)
    .replace(/DD/g, day)
    .replace(/HH/g, hours)
    .replace(/mm/g, minutes)
    .replace(/ss/g, seconds);
};
