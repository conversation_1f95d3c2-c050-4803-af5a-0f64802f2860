import {
  describe,
  expect,
  it,
  beforeEach,
  afterEach,
  mock,
  spyOn,
} from "bun:test";
import { AIHandler } from "../AIHandler";
import { AIModelStreamPayload } from "../AIModelStream";
import { MessageToolCall, SendMessage } from "@/model/sendMessage";
import { FinishReason } from "../ModelStreamResponse";
import { OpenAIScript } from "../scripts_ai/openai";

const mockAIModelStream = mock(
  (
    endpoint: string,
    headers: Record<string, string>,
    payload: AIModelStreamPayload,
    signal: AbortSignal
  ) => {
    const stream = new ReadableStream<{
      text: string | null;
      toolCalls?: { [index: number]: MessageToolCall };
      is_finished: FinishReason;
    }>({
      start(controller) {
        controller.enqueue({
          text: "Test response",
          is_finished: "stop",
        });
        controller.close();
      },
    });
    return Promise.resolve(stream);
  }
);

// Mock localStorage
const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: function (key: string) {
    return this.store[key] || null;
  },
  setItem: function (key: string, value: string) {
    this.store[key] = value;
  },
  clear: function () {
    this.store = {};
  },
};

describe("AIHandler completion method", () => {
  beforeEach(() => {
    // Setup mock localStorage
    Object.defineProperty(globalThis, "localStorage", {
      value: mockLocalStorage,
      writable: true,
    });

    // Clear mocks and localStorage
    mockAIModelStream.mockClear();
    mockLocalStorage.clear();

    // Set up API keys in localStorage
    mockLocalStorage.setItem("OPENAI_API_KEY", "test-openai-key");
  });

  it("should call AIModelStream with correct parameters for OpenAI", async () => {
    // Arrange
    const handler = new AIHandler(
      "https://api.openai.com/v1/chat/completions",
      { Authorization: "Bearer OPENAI_API_KEY" },
      { required: ["OPENAI_API_KEY"], optional: [] },
      "gpt-4o",
      0.7,
      1.0,
      0.0,
      0.0,
      4096,
      1,
      undefined,
      "openai",
      mockAIModelStream
    );

    const messages: SendMessage[] = [
      { role: "user", content: "Hello, world!" },
    ];
    const abortController = new AbortController();

    // const createRequestSpy = spyOn(OpenAIScript, "createRequest");
    // createRequestSpy.mockImplementation(() => ({
    //     endpoint: "https://api.openai.com/v1/chat/completions",
    //     headers: {
    //         "Authorization": "Bearer test-openai-key",
    //         "Content-Type": "application/json"
    //     },
    //     body: {
    //         model: "gpt-4o",
    //         messages: messages,
    //         temperature: 0.7,
    //         max_tokens: 4096,
    //         stream: true,
    //         user: "test-user",
    //         tools: undefined
    //     }
    // }));

    // Act
    await handler.completion(
      { messages, user: "test-user" },
      abortController.signal
    );

    // Assert
    // expect(createRequestSpy).toHaveBeenCalledTimes(1);
    expect(mockAIModelStream).toHaveBeenCalledTimes(1);
  });

  it("should handle streaming responses correctly", async () => {
    // Create a custom streaming mock
    const streamingMock = mock(
      (
        endpoint: string,
        headers: Record<string, string>,
        payload: AIModelStreamPayload,
        signal: AbortSignal
      ) => {
        const stream = new ReadableStream<{
          text: string | null;
          toolCalls?: { [index: number]: MessageToolCall };
          is_finished: FinishReason;
        }>({
          start(controller) {
            // Simulate multiple chunks of streaming data
            controller.enqueue({
              text: "Hello",
              is_finished: null,
            });

            setTimeout(() => {
              controller.enqueue({
                text: " world",
                is_finished: null,
              });
            }, 10);

            setTimeout(() => {
              controller.enqueue({
                text: "!",
                is_finished: "stop",
              });
              controller.close();
            }, 20);
          },
        });
        return Promise.resolve(stream);
      }
    );

    // Arrange
    const handler = new AIHandler(
      "https://api.openai.com/v1/chat/completions",
      { Authorization: "Bearer OPENAI_API_KEY" },
      { required: ["OPENAI_API_KEY"], optional: [] },
      "gpt-4o",
      0.7,
      1.0,
      0.0,
      0.0,
      4096,
      1,
      undefined,
      "openai",
      streamingMock
    );

    const messages: SendMessage[] = [
      { role: "user", content: "Generate a greeting" },
    ];
    const abortController = new AbortController();

    // Act
    const stream = await handler.completion(
      { messages, user: "test-user" },
      abortController.signal
    );

    // Assert
    expect(streamingMock).toHaveBeenCalledTimes(1);

    // Test reading from the stream
    const reader = stream.getReader();

    // First chunk
    const chunk1 = await reader.read();
    expect(chunk1.done).toBe(false);
    expect(chunk1.value?.text).toBe("Hello");
    expect(chunk1.value?.is_finished).toBeNull();

    // Second chunk
    const chunk2 = await reader.read();
    expect(chunk2.done).toBe(false);
    expect(chunk2.value?.text).toBe(" world");
    expect(chunk2.value?.is_finished).toBeNull();

    // Third chunk
    const chunk3 = await reader.read();
    expect(chunk3.done).toBe(false);
    expect(chunk3.value?.text).toBe("!");
    expect(chunk3.value?.is_finished).toBe("stop");

    // Stream should be done
    const chunk4 = await reader.read();
    expect(chunk4.done).toBe(true);
  });

  it("should handle abort signal from caller side", async () => {
    // Create a streaming mock that simulates a long-running response
    const longRunningMock = mock(
      (
        endpoint: string,
        headers: Record<string, string>,
        payload: AIModelStreamPayload,
        signal: AbortSignal
      ) => {
        const stream = new ReadableStream<{
          text: string | null;
          toolCalls?: { [index: number]: MessageToolCall };
          is_finished: FinishReason;
        }>({
          start(controller) {
            // Check if already aborted
            if (signal.aborted) {
              controller.error(
                new DOMException("The operation was aborted", "AbortError")
              );
              return;
            }

            // Add abort listener
            const abortListener = () => {
              controller.error(
                new DOMException("The operation was aborted", "AbortError")
              );
            };
            signal.addEventListener("abort", abortListener);

            // Send first chunk
            controller.enqueue({
              text: "Starting a long response",
              is_finished: null,
            });

            // Setup a long sequence of chunks with delays
            const timer1 = setTimeout(() => {
              if (!signal.aborted) {
                controller.enqueue({
                  text: " that continues",
                  is_finished: null,
                });
              }
            }, 50);

            const timer2 = setTimeout(() => {
              if (!signal.aborted) {
                controller.enqueue({
                  text: " for a while",
                  is_finished: null,
                });
                controller.close();
              }
            }, 100);

            // Cleanup function
            return () => {
              signal.removeEventListener("abort", abortListener);
              clearTimeout(timer1);
              clearTimeout(timer2);
            };
          },
        });

        return Promise.resolve(stream);
      }
    );

    // Arrange
    const handler = new AIHandler(
      "https://api.openai.com/v1/chat/completions",
      { Authorization: "Bearer OPENAI_API_KEY" },
      { required: ["OPENAI_API_KEY"], optional: [] },
      "gpt-4o",
      0.7,
      1.0,
      0.0,
      0.0,
      4096,
      1,
      undefined,
      "openai",
      longRunningMock
    );

    const messages: SendMessage[] = [
      {
        role: "user",
        content: "THIS IS SPY FROM AIHANDLER: Generate a long response",
      },
    ];
    const abortController = new AbortController();

    // Act
    const stream = await handler.completion(
      { messages, user: "test-user" },
      abortController.signal
    );
    const reader = stream.getReader();

    // Read the first chunk
    const chunk1 = await reader.read();
    expect(chunk1.done).toBe(false);
    expect(chunk1.value?.text).toBe("Starting a long response");

    // Abort the request from the caller side
    abortController.abort();

    // Try to read after abort - should throw an error
    try {
      await reader.read();
      expect("This line should not be reached").fail();
    } catch (error) {
      // Verify we got an abort error
      expect((error as Error).name).toBe("AbortError");
    }

    // Verify the mock was called
    expect(longRunningMock).toHaveBeenCalledTimes(1);
    expect(longRunningMock).toHaveBeenCalledWith(
      expect.any(String),
      expect.any(Object),
      expect.any(Object),
      abortController.signal
    );
  });
});
