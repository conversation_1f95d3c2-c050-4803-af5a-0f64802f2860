import { SendMessage } from "@/model/sendMessage";

export interface ScriptArgs {
  messages: SendMessage[];
  tools?: any[];
  stream?: boolean;
  temperature?: number;
  maxTokens?: number;
  user?: string;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  n?: number;
}
export interface ModelInfo {
  endpoint: string;
  model: string;
  headers: Record<string, string>;
  resolvedKeys: Record<string, string>;
  placeholders: { required: string[]; optional: string[] };
}
