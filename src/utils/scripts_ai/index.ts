import { GeminiScript } from "./gemini";
import { ClaudeScript } from "./claude";
import { OpenAIScript } from "./openai";
import { ModelInfo, ScriptArgs } from "./params";

export type ModelVendorAPI = "openai" | "gemini" | "claude";

export const scriptRegistry: Record<
  ModelVendorAPI,
  {
    createRequest: (
      self: ModelInfo,
      args: ScriptArgs
    ) => {
      endpoint: string;
      headers: Record<string, string>;
      body: any;
    };
  }
> = {
  openai: OpenAIScript,
  gemini: GeminiScript,
  claude: ClaudeScript,
};
