import { ModelInfo, ScriptArgs } from "./params";

export const GeminiScript = {
  createRequest: (self: ModelInfo, args: ScriptArgs) => {
    const {
      messages,
      tools = [],
      stream = false,
      temperature = 0.7,
      maxTokens = 4096,
      topP,
      frequencyPenalty,
      presencePenalty,
      n,
    } = args;

    // Convert OpenAI-style messages to Gemini format
    const contents = messages
      .filter((m) => m.role !== "system")
      .map((m) => ({
        role: m.role === "assistant" ? "model" : m.role,
        parts: [{ text: m.content }],
      }));

    // in the future we differentiate between stream and non-stream endpoints
    let endpoint = stream ? self.endpoint : self.endpoint;
    let headers = { ...self.headers };

    for (const key of [
      ...self.placeholders.required,
      ...self.placeholders.optional,
    ]) {
      endpoint = endpoint.replace(key, self.resolvedKeys[key]);
      for (const pairKey in headers) {
        if (headers.hasOwnProperty(pairKey)) {
          const value = headers[pairKey];
          headers[pairKey] = value.replace(key, self.resolvedKeys[key]);
        }
      }
    }

    // Prepare the request body
    const body = {
      contents,
      tools,
      generationConfig: {
        temperature,
        maxOutputTokens: maxTokens,
        topP,
        frequencyPenalty,
        presencePenalty,
        // n
      },
    };

    return {
      endpoint,
      headers,
      body,
    };
  },
};
