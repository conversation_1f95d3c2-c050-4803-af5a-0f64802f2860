import { ModelInfo, ScriptArgs } from "./params";

export const OpenAIScript = {
  createRequest: (self: ModelInfo, args: ScriptArgs) => {
    const {
      messages,
      tools = [],
      stream = false,
      temperature = 0.7,
      maxTokens = 4096,
      user,
      topP,
      frequencyPenalty,
      presencePenalty,
      n,
    } = args;

    const body = {
      model: self.model,
      messages,
      temperature,
      max_tokens: maxTokens,
      stream,
      user,
      tools: tools.length > 0 ? tools : undefined,
      top_p: topP,
      frequency_penalty: frequencyPenalty,
      presence_penalty: presencePenalty,
      n,
    };

    let endpoint = self.endpoint;
    let headers = { ...self.headers };

    for (const key of [
      ...self.placeholders.required,
      ...self.placeholders.optional,
    ]) {
      endpoint = endpoint.replace(key, self.resolvedKeys[key]);

      for (const pairKey in headers) {
        if (headers.hasOwnProperty(pairKey)) {
          const value = headers[pairKey];
          headers[pairKey] = value.replace(key, self.resolvedKeys[key]);
        }
      }
    }

    return {
      endpoint: self.endpoint,
      headers,
      body,
    };
  },
};
