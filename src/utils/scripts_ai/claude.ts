import { ModelInfo, ScriptArgs } from "./params";

export const ClaudeScript = {
  createRequest: (self: ModelInfo, args: ScriptArgs) => {
    const {
      messages,
      tools = [],
      stream = false,
      temperature = 0.7,
      maxTokens = 4096,
      topP,
      frequencyPenalty,
      presencePenalty,
      n,
    } = args;

    // Convert OpenAI-style messages to Claude format
    const formattedMessages = messages.map((m) => ({
      role: m.role,
      content: m.content,
    }));

    let endpoint = self.endpoint;
    let headers = { ...self.headers };

    // Apply the resolved keys to the endpoint and headers placeholders
    for (const key of [
      ...self.placeholders.required,
      ...self.placeholders.optional,
    ]) {
      endpoint = endpoint.replace(key, self.resolvedKeys[key]);

      for (const pairKey in headers) {
        if (headers.hasOwnProperty(pairKey)) {
          const value = headers[pairKey];
          headers[pairKey] = value.replace(key, self.resolvedKeys[key]);
        }
      }
    }

    // Prepare the request body
    const body = {
      model: self.model,
      messages: formattedMessages,
      temperature,
      max_tokens: maxTokens,
      stream,
      tools,
      top_p: topP,
      frequency_penalty: frequencyPenalty,
      presence_penalty: presencePenalty,
      n,
    };

    return {
      endpoint,
      headers,
      body,
    };
  },
};
