import { describe, expect, it } from "bun:test";
import { GeminiScript } from "../gemini";
import { ClaudeScript } from "../claude";
import { ScriptArgs } from "../params";
import { OpenAIScript } from "../openai";

describe("LLM Scripts", () => {
  describe("OpenAIScript", () => {
    it("should create a proper OpenAI request", () => {
      // Arrange
      const modelInfo = {
        endpoint: "https://api.openai.com/v1/chat/completions",
        model: "gpt-4o",
      };

      const args: ScriptArgs = {
        messages: [{ role: "user", content: "Hello, world!" }],
        key: "test-openai-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
        user: "test-user",
      };

      // Act
      const request = OpenAIScript.createRequest(modelInfo, args);

      // Assert
      expect(request.endpoint).toBe(
        "https://api.openai.com/v1/chat/completions"
      );
      expect(request.headers).toEqual({
        "Content-Type": "application/json",
        Authorization: "Bearer test-openai-key",
      });

      const body = request.body;
      expect(body).toEqual({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: "Hello, world!",
          },
        ],
        temperature: 0.7,
        max_tokens: 4096,
        stream: true,
        user: "test-user",
        tools: undefined,
      });
    });

    it("should include tools when provided", () => {
      // Arrange
      const modelInfo = {
        endpoint: "https://api.openai.com/v1/chat/completions",
        model: "gpt-4o",
      };

      const tools = [
        {
          type: "function",
          function: {
            name: "get_weather",
            description: "Get the current weather",
            parameters: {
              type: "object",
              properties: {
                location: {
                  type: "string",
                  description: "The city and state, e.g. San Francisco, CA",
                },
              },
              required: ["location"],
            },
          },
        },
      ];

      const args: ScriptArgs = {
        messages: [{ role: "user", content: "What's the weather like?" }],
        tools,
        key: "test-openai-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
        user: "test-user",
      };

      // Act
      const request = OpenAIScript.createRequest(modelInfo, args);

      // Assert
      const body = request.body;
      expect(body.tools).toEqual(tools);
    });

    it("should include model when provided", () => {
      // Arrange
      const modelInfo = {
        endpoint: "https://api.openai.com/v1/chat/completions",
        model: "gpt-4o",
      };

      const args: ScriptArgs = {
        messages: [{ role: "user", content: "Hello, world!" }],
        key: "test-openai-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
      };

      // Act
      const request = OpenAIScript.createRequest(modelInfo, args);

      // Assert
      const body = request.body;
      expect(body.model).toBe("gpt-4o");
    });
  });

  describe("GeminiScript", () => {
    it("should create a proper Gemini request", () => {
      // Arrange
      const modelInfo = {
        endpoint:
          "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro",
      };

      const args: ScriptArgs = {
        messages: [{ role: "user", content: "Hello, world!" }],
        key: "test-gemini-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
      };

      // Act
      const request = GeminiScript.createRequest(modelInfo, args);

      // Assert
      expect(request.endpoint).toContain(
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro"
      );
      expect(request.endpoint).toContain("key=test-gemini-key");
      expect(request.headers).toEqual({
        "Content-Type": "application/json",
      });

      const body = request.body;
      expect(body).toHaveProperty("contents");

      // Check that the message was properly converted to Gemini format
      expect(body.contents[0].role).toBe("user");
      expect(body.contents[0].parts[0].text).toBe("Hello, world!");

      // Check generation config
      expect(body.generationConfig).toEqual({
        temperature: 0.7,
        maxOutputTokens: 4096,
      });
    });

    it("should handle multiple messages correctly", () => {
      // Arrange
      const modelInfo = {
        endpoint:
          "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro",
      };

      const args: ScriptArgs = {
        messages: [
          { role: "user", content: "Hello" },
          { role: "assistant", content: "Hi there! How can I help you?" },
          { role: "user", content: "Tell me about AI" },
        ],
        key: "test-gemini-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
      };

      // Act
      const request = GeminiScript.createRequest(modelInfo, args);

      // Assert
      const body = request.body;
      expect(body.contents.length).toBe(3);
      expect(body.contents[0].role).toBe("user");
      expect(body.contents[1].role).toBe("model");
      expect(body.contents[2].role).toBe("user");
    });
  });

  describe("ClaudeScript", () => {
    it("should create a proper Claude request", () => {
      // Arrange
      const modelInfo = {
        endpoint: "https://api.anthropic.com/v1/messages",
      };

      const args: ScriptArgs = {
        messages: [{ role: "user", content: "Hello, world!" }],
        key: "test-claude-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
      };

      // Act
      const request = ClaudeScript.createRequest(modelInfo, args);

      // Assert
      expect(request.endpoint).toBe("https://api.anthropic.com/v1/messages");
      expect(request.headers).toEqual({
        "Content-Type": "application/json",
        "x-api-key": "test-claude-key",
        "anthropic-version": "2023-06-01",
      });

      const body = request.body;
      expect(body).toHaveProperty("messages");
      expect(body.messages).toEqual([
        { role: "user", content: "Hello, world!" },
      ]);
      expect(body).toHaveProperty("model");
      expect(body).toHaveProperty("temperature");
      expect(body).toHaveProperty("max_tokens");
      expect(body).toHaveProperty("stream");
    });

    it("should set the model when provided", () => {
      // Arrange
      const modelInfo = {
        endpoint: "https://api.anthropic.com/v1/messages",
      };

      const args: ScriptArgs = {
        messages: [{ role: "user", content: "Hello, world!" }],
        key: "test-claude-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
      };

      // Act
      const request = ClaudeScript.createRequest(modelInfo, args);

      // Assert
      const body = request.body;
      expect(body.model).toBe("claude-3-opus-20240229");
    });

    it("should handle system messages correctly", () => {
      // Arrange
      const modelInfo = {
        endpoint: "https://api.anthropic.com/v1/messages",
      };

      const args: ScriptArgs = {
        messages: [
          { role: "system", content: "You are a helpful assistant." },
          { role: "user", content: "Hello, world!" },
        ],
        key: "test-claude-key",
        temperature: 0.7,
        maxTokens: 4096,
        stream: true,
      };

      // Act
      const request = ClaudeScript.createRequest(modelInfo, args);

      // Assert
      const body = request.body;
      expect(body.messages).toEqual([
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "Hello, world!" },
      ]);
    });
  });
});
