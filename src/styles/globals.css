@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:where(.dark, .dark *));

@layer components {
  .bg-primary {
    @apply bg-[#f7f7f7] dark:bg-customGray-dark;
  }

  .bg-secondary {
    @apply bg-customGray-base dark:bg-customGray-light;
  }

  .text-primary {
    @apply text-black dark:text-white;
  }

  .text-secondary {
    @apply text-gray-500 dark:text-slate-500;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

a div button {
  -webkit-tap-highlight-color: transparent;
}

@keyframes slide-up-down {
  0%,
  100% {
    opacity: 0.8;
    transform: translateY(10%);
  }
  50% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up-down {
  animation: slide-up-down 0.6s ease-in-out infinite;
}

@layer base {
  :root {
  }

  .dark {
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
