import { Role } from "@/model/sendMessage";
import { OpenAIModel } from "./openai";

export interface Message {
  role: Role;
  content: string;
}

export interface ChatBody {
  model: OpenAIModel;
  messages: Message[];
  key: string;
  prompt: string;
  temperature: number;
}

export interface Conversation {
  id: string;
  name: string;
  messages: Message[];
  model: OpenAIModel;
  prompt: string;
  temperature: number;
  folderId: string | null;
}

export const DUMMY: Conversation = {
  id: "your_conversation_id",
  name: "Your Conversation Name",
  messages: [
    {
      role: "user",
      content: "Hello!",
    },
    {
      role: "assistant",
      content: "Hi there!",
    },
  ],
  model: {
    id: "gpt-3.5-turbo", // Replace with your actual model ID
    name: "GPT-3.5",
    maxLength: 12000,
    tokenLimit: 4000,
  },
  prompt: "Start of your conversation prompt",
  temperature: 0.7,
  folderId: "your_folder_id_or_null",
};
