import { DisplayedMessage as ChatMessage } from "@/model/displayedMessage";
import { IndexDBRepo } from "../IndexDb";
import { ChatRoom } from "@/model/chatroom";

class LocalRepo {
  indexDB = new IndexDBRepo();
  room: ChatRoom;
  constructor(room: ChatRoom) {
    this.room = room;
  }

  async addChatMessage(message: ChatMessage): Promise<void> {
    this.room.messages.push(message);
    await this.indexDB.updateChatRoom(this.room.id, {
      messages: this.room.messages,
    });
  }

  async updateChatMessage(message: ChatMessage): Promise<void> {
    await this.indexDB.updateChatRoom(this.room.id, {
      messages: this.room.messages.map((e) =>
        e.messageId === message.messageId ? message : e
      ),
    });
  }

  async getChatMessages(): Promise<ChatMessage[]> {
    return this.room.messages;
  }

  async deleteChatMessage(id: string): Promise<void> {
    this.room.messages = this.room.messages.filter((e) => e.messageId !== id);
    await this.indexDB.updateChatRoom(this.room.id, {
      messages: this.room.messages,
    });
  }

  async deleteAllChatMessages(): Promise<void> {
    this.room.messages = [];
    await this.indexDB.updateChatRoom(this.room.id, {
      messages: this.room.messages,
    });
  }
}

export default LocalRepo;
