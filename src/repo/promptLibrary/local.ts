import { PromptData, PromptData as PromptLibrary } from "@/model/promptData";
import { IndexDBRepo } from "../IndexDb";

class LocalRepo {
  indexDB = new IndexDBRepo();
  async addPromptLibrary(newPrompt: PromptData): Promise<void> {
    await this.indexDB.addPromptLibrary(newPrompt);
  }

  async getPromptLibrarys(): Promise<PromptLibrary[]> {
    return await this.indexDB.getPromptLibrarys();
  }

  async getPromptLibraryById(id: string): Promise<PromptLibrary | null> {
    return await this.indexDB.getPromptLibraryById(id);
  }

  async updatePromptLibrary(
    id: string,
    name: string,
    description: string,
    instruction: string
  ): Promise<void> {
    await this.indexDB.updatePromptLibrary(id, name, description, instruction);
  }

  async deletePromptLibrary(id: string): Promise<void> {
    await this.indexDB.deletePromptLibrary(id);
  }
}

export default LocalRepo;
