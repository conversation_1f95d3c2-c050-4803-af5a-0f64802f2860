// import LocalRepo from './local'; // Update the path to your LocalRepo class
// require("fake-indexeddb/auto");

// describe('LocalRepo', () => {
//   let localRepo: LocalRepo;

//   beforeEach(async () => {
//     localRepo = new LocalRepo();
//   });

//   afterEach(async () => {
//     const db = await localRepo.getDB();
//     await db.clear('PromptLibrarys');
//   });

//   it('should add a PromptLibrary', async () => {
//     // await localRepo.addPromptLibrary('Test PromptLibrary');
//     // const PromptLibrarys: PromptLibrary[] = await localRepo.getPromptLibrarys();
//     // expect(PromptLibrarys.length).toBe(1);
//   });

//   it('should update a PromptLibrary', async () => {
//     // await localRepo.addPromptLibrary('Test PromptLibrary');
//     // const PromptLibrarys: PromptLibrary[] = await localRepo.getPromptLibrarys();
//     // const PromptLibraryToUpdate: PromptLibrary = PromptLibrarys[0];
//     // const newName: string = 'Updated PromptLibrary';
//     // await localRepo.updatePromptLibrary(PromptLibraryToUpdate.id, newName);
//     // const updatedPromptLibrary: PromptLibrary | null = await localRepo.getPromptLibraryById(PromptLibraryToUpdate.id);
//     // expect(updatedPromptLibrary?.name).toBe(newName);
//   });

//   it('should delete a PromptLibrary', async () => {
//     // await localRepo.addPromptLibrary('Test PromptLibrary');
//     // const PromptLibrarys: PromptLibrary[] = await localRepo.getPromptLibrarys();
//     // const PromptLibraryToDelete: PromptLibrary = PromptLibrarys[0];
//     // await localRepo.deletePromptLibrary(PromptLibraryToDelete.id);
//     // const updatedPromptLibrarys: PromptLibrary[] = await localRepo.getPromptLibrarys();
//     // expect(updatedPromptLibrarys.length).toBe(0);
//   });
// });
