import LocalRepo from "./local";

export class PromptLibraryRepo {
  private static instance: PromptLibraryRepo | null = null;
  private localRepo: LocalRepo;

  private constructor() {
    this.localRepo = new LocalRepo();
  }

  public static getInstance(): PromptLibraryRepo {
    if (!PromptLibraryRepo.instance) {
      PromptLibraryRepo.instance = new PromptLibraryRepo();
    }
    return PromptLibraryRepo.instance;
  }

  public getPromptLibraryRepo(): LocalRepo {
    return this.localRepo;
  }
}
