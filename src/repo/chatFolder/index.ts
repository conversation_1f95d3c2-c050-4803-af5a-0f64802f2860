import LocalRepo from "./local";

export class ChatFolderRepo {
  private static instance: ChatFolderRepo | null = null;
  private localRepo: LocalRepo;

  private constructor() {
    this.localRepo = new LocalRepo();
  }

  public static getInstance(): ChatFolderRepo {
    if (!ChatFolderRepo.instance) {
      ChatFolderRepo.instance = new ChatFolderRepo();
    }
    return ChatFolderRepo.instance;
  }

  public getChatFolderRepo(): LocalRepo {
    return this.localRepo;
  }
}
