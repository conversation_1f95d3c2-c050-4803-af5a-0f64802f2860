// import LocalRepo from './local'; // Update the path to your LocalRepo class
// require("fake-indexeddb/auto");

// describe('LocalRepo', () => {
//   let localRepo: LocalRepo;

//   beforeEach(async () => {
//     localRepo = new LocalRepo();
//   });

//   afterEach(async () => {
//     const db = await localRepo.getDB();
//     await db.clear('ChatFolders');
//   });

//   it('should add a ChatFolder', async () => {
//     // await localRepo.saveChatFolder('Test ChatFolder');
//     // const ChatFolders: ChatFolder[] = await localRepo.getChatFolders();
//     // expect(ChatFolders.length).toBe(1);
//   });

//   it('should update a ChatFolder', async () => {
//     // await localRepo.saveChatFolder('Test ChatFolder');
//     // const ChatFolders: ChatFolder[] = await localRepo.getChatFolders();
//     // const ChatFolderToUpdate: ChatFolder = ChatFolders[0];
//     // const newName: string = 'Updated ChatFolder';
//     // await localRepo.updateChatFolder(ChatFolderToUpdate.id, newName);
//     // const updatedChatFolder: ChatFolder | null = await localRepo.getChatFolderById(ChatFolderToUpdate.id);
//     // expect(updatedChatFolder?.name).toBe(newName);
//   });

//   it('should delete a ChatFolder', async () => {
//     // await localRepo.saveChatFolder('Test ChatFolder');
//     // const ChatFolders: ChatFolder[] = await localRepo.getChatFolders();
//     // const ChatFolderToDelete: ChatFolder = ChatFolders[0];
//     // await localRepo.deleteChatFolder(ChatFolderToDelete.id);
//     // const updatedChatFolders: ChatFolder[] = await localRepo.getChatFolders();
//     // expect(updatedChatFolders.length).toBe(0);
//   });
// });
