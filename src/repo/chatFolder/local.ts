import { ChatFolder } from "@/model/chatFolder";
import { IndexDBRepo } from "../IndexDb";

class LocalRepo {
  indexDB = new IndexDBRepo();
  async saveChatFolder(newFolder: ChatFolder): Promise<void> {
    await this.indexDB.saveChatFolder(newFolder);
  }

  async getChatFolders(): Promise<ChatFolder[]> {
    return await this.indexDB.getChatFolders();
  }

  async getChatFolderById(id: string): Promise<ChatFolder | null> {
    return await this.indexDB.getChatFolderById(id);
  }

  async updateChatFolder(
    id: string,
    folderName: string,
    description: string
  ): Promise<void> {
    await this.indexDB.updateChatFolder(id, folderName, description);
  }

  async deleteChatFolder(id: string): Promise<void> {
    await this.indexDB.deleteChatFolder(id);
  }
}

export default LocalRepo;
