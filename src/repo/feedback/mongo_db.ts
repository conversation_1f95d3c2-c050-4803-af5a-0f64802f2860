import { MongoClient, Db, Collection } from "mongodb";

export default class MongoFeedbackRepository {
  private mongoClient: MongoClient;
  private db: Db;
  private feedbackCollection: Collection;

  constructor() {
    this.mongoClient = new MongoClient(process.env.MONGODB_URI as string, {});
    this.db = this.mongoClient.db(process.env.MONGODB_DBNAME as string);
    this.feedbackCollection = this.db.collection("feedback");
  }

  async saveFeedback(feedback: string): Promise<void> {
    try {
      await this.connect();
      const timestamp = Date.now();
      await this.feedbackCollection.insertOne({
        feedback,
        timestamp,
      });
    } catch (error) {
      throw new Error("Error saving to the feedback");
    } finally {
      await this.close();
    }
  }

  async connect(): Promise<void> {
    try {
      await this.mongoClient.connect();
    } catch (error) {
      throw new Error("Error connecting to the database");
    }
  }

  async close(): Promise<void> {
    try {
      await this.mongoClient.close();
    } catch (error) {
      throw new Error("Error closing the database connection");
    }
  }
}
