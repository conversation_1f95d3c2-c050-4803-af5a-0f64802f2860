import { MongoClient, Db, Collection } from "mongodb";

export default class MongoWaitlistRepository {
  private mongoClient: MongoClient;
  private db: Db;
  private waitlistCollection: Collection;

  constructor() {
    this.mongoClient = new MongoClient(process.env.MONGODB_URI as string, {});
    this.db = this.mongoClient.db(process.env.MONGODB_DBNAME as string);
    this.waitlistCollection = this.db.collection("waitlist");
  }

  async getEmailForRefCode(referral: string): Promise<string | null> {
    try {
      await this.connect();
      const waitlistItem = await this.waitlistCollection.findOne({
        refCode: referral,
      });
      return waitlistItem ? waitlistItem.email : null;
    } catch (error) {
      throw new Error("Error retrieving email for referral code");
    } finally {
      await this.close();
    }
  }

  async getReferralCode(email: string): Promise<string | null> {
    try {
      await this.connect();
      const waitlistItem = await this.waitlistCollection.findOne({ email });
      return waitlistItem ? waitlistItem.refCode : null;
    } catch (error) {
      throw new Error("Error retrieving referral code for email");
    } finally {
      await this.close();
    }
  }

  async saveWaitlist(
    email: string,
    feature: string,
    refCode: string
  ): Promise<void> {
    try {
      await this.connect();
      const timestamp = Date.now();

      const existingEntry = await this.waitlistCollection.findOne({ email });

      const newFeatureSet = new Set(existingEntry?.feature || []);

      newFeatureSet.add(feature);

      const updatedFeatures = Array.from(newFeatureSet);

      if (existingEntry) {
        await this.waitlistCollection.updateOne(
          { email },
          {
            $set: {
              feature: updatedFeatures,
              refCode: refCode,
              timestamp: timestamp,
            },
          }
        );
      } else {
        await this.waitlistCollection.insertOne({
          email,
          feature: updatedFeatures,
          refCode,
          timestamp,
        });
      }
    } catch (error) {
      throw new Error(`Error saving to the waitlist: ${error}`);
    } finally {
      await this.close();
    }
  }

  async connect(): Promise<void> {
    try {
      await this.mongoClient.connect();
    } catch (error) {
      throw new Error("Error connecting to the database");
    }
  }

  async close(): Promise<void> {
    try {
      await this.mongoClient.close();
    } catch (error) {
      throw new Error("Error closing the database connection");
    }
  }
}
