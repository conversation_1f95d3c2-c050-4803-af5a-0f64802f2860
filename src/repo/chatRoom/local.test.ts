// import LocalRepo from './local'; // Update the path to your LocalRepo class
// import { ChatRoom } from '@/model/chatroom';
// require("fake-indexeddb/auto");

// describe('LocalRepo', () => {
//   let localRepo: LocalRepo;

//   beforeEach(async () => {
//     localRepo = new LocalRepo();
//   });

//   afterEach(async () => {
//     const db = await localRepo.getDB();
//     await db.clear('ChatRooms');
//   });

//   it('should add a ChatRoom', async () => {
//     await localRepo.addChatRoom('Test ChatRoom');
//     const ChatRooms: ChatRoom[] = await localRepo.getChatRooms();
//     expect(ChatRooms.length).toBe(1);
//   });

//   it('should update a ChatRoom', async () => {
//     await localRepo.addChatRoom('Test ChatRoom');
//     const ChatRooms: ChatRoom[] = await localRepo.getChatRooms();
//     const ChatRoomToUpdate: ChatRoom = ChatRooms[0];
//     const newName: string = 'Updated ChatRoom';
//     await localRepo.updateChatRoom(ChatRoomToUpdate.id, newName);
//     const updatedChatRoom: ChatRoom | null = await localRepo.getChatRoomById(ChatRoomToUpdate.id);
//     expect(updatedChatRoom?.name).toBe(newName);
//   });

//   it('should delete a ChatRoom', async () => {
//     await localRepo.addChatRoom('Test ChatRoom');
//     const ChatRooms: ChatRoom[] = await localRepo.getChatRooms();
//     const ChatRoomToDelete: ChatRoom = ChatRooms[0];
//     await localRepo.deleteChatRoom(ChatRoomToDelete.id);
//     const updatedChatRooms: ChatRoom[] = await localRepo.getChatRooms();
//     expect(updatedChatRooms.length).toBe(0);
//   });
// });
