import LocalRepo from "./local";

export class ChatRoomRepo {
  private static instance: ChatRoomRepo | null = null;
  private localRepo: LocalRepo;

  private constructor() {
    this.localRepo = new LocalRepo();
  }

  public static getInstance(): ChatRoomRepo {
    if (!ChatRoomRepo.instance) {
      ChatRoomRepo.instance = new ChatRoomRepo();
    }
    return ChatRoomRepo.instance;
  }

  public getChatRoomRepo(): LocalRepo {
    return this.localRepo;
  }
}
