import { ChatR<PERSON> } from "@/model/chatroom";
import { IndexDBRepo } from "../IndexDb";

class LocalRepo {
  indexDB = new IndexDBRepo();

  async saveChatRoom(
    name: string,
    { folderId, id, tags }: { folderId?: string; id?: string; tags?: string[] }
  ): Promise<ChatRoom> {
    return await this.indexDB.saveChatRoom(name, { folderId, id, tags });
  }

  async getChatRooms(
    folderIds: string[]
  ): Promise<{ [key: string]: ChatRoom[] }> {
    return await this.indexDB.getChatRooms(folderIds);
  }

  async getChatRoomById(id: string): Promise<ChatRoom | null> {
    return await this.indexDB.getChatRoomById(id);
  }

  async updateChatRoom(id: string, updates: Partial<ChatRoom>): Promise<void> {
    await this.indexDB.updateChatRoom(id, updates);
  }

  async deleteChatRoom(id: string): Promise<void> {
    await this.indexDB.deleteChatRoom(id);
  }
}

export default LocalRepo;
