import { Document, ObjectId, WithId } from "mongodb";
import MongoRepository, { MONGO_COLLECTIONS } from "../mongo";

interface SharedDocumentDocument {
  blocks?: any[];
  markdown?: string;
  html?: string;
  createdAt: Date;
  expiresAt: Date;
}

export default class MongoShareDocumentRepository extends MongoRepository {
  constructor() {
    super({ collection: MONGO_COLLECTIONS.SHARED_DOCUMENTS });
  }

  async saveSharedDocuments(data: SharedDocumentDocument): Promise<string> {
    try {
      await this.connect();

      const result = await this.collection.insertOne(data);
      return result.insertedId.toString();
    } catch (error) {
      throw new Error(`Error saving shared blocks: ${error}`);
    } finally {
      await this.close();
    }
  }

  async getSharedDocuments(id: string): Promise<SharedDocumentDocument | null> {
    try {
      await this.connect();

      const objectId = this.toObjectId(id);
      const result = await this.collection.findOne({ _id: objectId });

      if (!result) return null;

      return asSharedDocumentDocument(result);
    } catch (error) {
      throw new Error(`Error fetching shared chat with ID ${id}: ${error}`);
    } finally {
      await this.close();
    }
  }

  protected toObjectId(id: string): ObjectId {
    return new ObjectId(id);
  }
}

function asSharedDocumentDocument(
  dto: WithId<Document>
): SharedDocumentDocument {
  return {
    blocks: dto.blocks,
    html: dto.html,
    markdown: dto.markdown,
    createdAt: dto.createdAt,
    expiresAt: dto.expiresAt,
  };
}
