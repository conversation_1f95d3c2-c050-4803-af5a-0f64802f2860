import { Document, ObjectId, WithId } from "mongodb";
import MongoRepository, { MONGO_COLLECTIONS } from "../mongo";
import { DisplayedMessage } from "@/model/displayedMessage";

interface SharedChatDocument {
  messages: DisplayedMessage[];
  createdAt: Date;
  expiresAt: Date;
}

export default class MongoShareChatsRepository extends MongoRepository {
  constructor() {
    super({ collection: MONGO_COLLECTIONS.SHARED_CHATS });
  }

  async saveSharedMessages(data: SharedChatDocument): Promise<string> {
    try {
      await this.connect();

      const result = await this.collection.insertOne(data);
      return result.insertedId.toString();
    } catch (error) {
      throw new Error(`Error saving shared messages: ${error}`);
    } finally {
      await this.close();
    }
  }

  async getSharedMessages(id: string): Promise<SharedChatDocument | null> {
    try {
      await this.connect();

      const objectId = this.toObjectId(id);
      const result = await this.collection.findOne({ _id: objectId });

      if (!result) return null;

      return asSharedChatDocument(result);
    } catch (error) {
      throw new Error(`Error fetching shared chat with ID ${id}: ${error}`);
    } finally {
      await this.close();
    }
  }

  protected toObjectId(id: string): ObjectId {
    return new ObjectId(id);
  }
}

function asSharedChatDocument(dto: WithId<Document>): SharedChatDocument {
  return {
    messages: dto.messages,
    createdAt: dto.createdAt,
    expiresAt: dto.expiresAt,
  };
}
