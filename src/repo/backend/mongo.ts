import { MongoClient, Db, Collection } from "mongodb";

export default class MongoRepository {
  protected mongoClient: MongoClient;
  protected db: Db;
  protected collection: Collection;

  constructor(params: { collection: string }) {
    this.mongoClient = new MongoClient(process.env.MONGODB_URI as string, {});
    this.db = this.mongoClient.db(process.env.MONGODB_DBNAME as string);
    this.collection = this.db.collection(params.collection); // Use params.collection instead of this.collection
  }

  protected async connect(): Promise<void> {
    try {
      await this.mongoClient.connect();
    } catch (error) {
      throw new Error("Error connecting to the database");
    }
  }

  protected async close(): Promise<void> {
    try {
      await this.mongoClient.close();
    } catch (error) {
      throw new Error("Error closing the database connection");
    }
  }
}

export const MONGO_COLLECTIONS = {
  MODELS: "models",
  AIAGENTS: "ai_agents",
  PLUGIN_ITEMS: "plugin_items",
  SHARED_CHATS: "shared_chats",
  SHARED_DOCUMENTS: "shared_documents",
  STRINGS: "strings",
};
