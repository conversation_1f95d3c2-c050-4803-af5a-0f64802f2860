import { Document } from "mongodb";
import { IModelRepository } from "./interface";
import MongoRepository, { MONGO_COLLECTIONS } from "../mongo";
import { Model } from "@/model/ai_model";

export default class MongoModelRepository
  extends MongoRepository
  implements IModelRepository
{
  constructor() {
    super({ collection: MONGO_COLLECTIONS.MODELS });
  }

  async getModels(): Promise<Model[]> {
    try {
      await this.connect();
      const modelDtos = await this.collection.find().toArray();
      return modelDtos.map((dto) => as(dto));
    } catch (error) {
      throw new Error("Error retrieving models from the database");
    } finally {
      await this.close();
    }
  }
}

function as(dto: Document): Model {
  return {
    id: dto.id || "",
    name: dto.name,
    vendor: dto.vendor,
    keepLocal: dto.keepLocal,
    endpoint: dto.endpoint,
    headers: dto.headers,
    features: dto.features,
    config: dto.config,
    model_vendor_api: dto.model_vendor_api,
  };
}
