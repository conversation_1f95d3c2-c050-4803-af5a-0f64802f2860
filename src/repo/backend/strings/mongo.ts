import MongoRepository, { MONGO_COLLECTIONS } from "../mongo";
import { IStringsDocument, IStringsRepository } from "./interface";

export default class MongoStringsRepository
  extends MongoRepository
  implements IStringsRepository
{
  constructor() {
    super({ collection: MONGO_COLLECTIONS.STRINGS });
  }

  async saveString(
    key: string,
    translations: Record<string, string>
  ): Promise<string> {
    try {
      await this.connect();

      const result = await this.collection.updateOne(
        { key },
        { $set: { key, translations } },
        { upsert: true }
      );

      if (result.upsertedId) {
        return result.upsertedId.toString();
      }

      return key;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw new Error(`Error saving string: ${error.message}`);
      } else {
        throw new Error("An unknown error occurred while saving the string.");
      }
    } finally {
      await this.close();
    }
  }

  async getString(key: string, language: string): Promise<string | null> {
    try {
      await this.connect();

      const result = await this.collection.findOne({ key });

      if (!result) {
        return null;
      }

      const translation = result.translations[language];
      return translation || null; // Return translation or null if not found
    } catch (error: unknown) {
      // Type error as unknown
      if (error instanceof Error) {
        throw new Error(`Error fetching string: ${error.message}`);
      } else {
        throw new Error("An unknown error occurred while fetching the string.");
      }
    } finally {
      await this.close();
    }
  }
}
