import { Document } from "mongodb";
import { IAIAgentRepository } from "./interface";
import MongoRepository, { MONGO_COLLECTIONS } from "../mongo";
import { AIAgent } from "@/model/ai_agent";

export default class MongoAIAgentRepository
  extends MongoRepository
  implements IAIAgentRepository
{
  constructor() {
    super({ collection: MONGO_COLLECTIONS.AIAGENTS });
  }

  async getAIAgents(): Promise<AIAgent[]> {
    try {
      await this.connect();
      const agentDtos = await this.collection
        .aggregate([
          {
            $project: {
              id: 1,
              icon: 1,
              title: 1,
              description: 1,
              systemInstruction: 1,
              queries: 1,
            },
          },
        ])
        .toArray();
      return agentDtos.map((dto) => as(dto));
    } catch (error) {
      throw new Error("Error retrieving AIAgents from the database");
    } finally {
      await this.close();
    }
  }
}

function as(dto: Document): AIAgent {
  return {
    id: dto.id || "",
    icon: dto.icon,
    title: dto.title,
    description: dto.description,
    systemInstruction: dto.systemInstruction,
    queries: dto.queries,
  };
}
