import { Document } from "mongodb";
import { GetExtensionItemsParams, IExtensionRepository } from "./interface";
import MongoRepository, { MONGO_COLLECTIONS } from "../mongo";
import { ExtensionItem } from "@/model/extension";

export default class MongoExtensionRepository
  extends MongoRepository
  implements IExtensionRepository
{
  constructor() {
    super({ collection: MONGO_COLLECTIONS.PLUGIN_ITEMS });
  }

  async getExtensionItems({
    name,
    description,
    filterType,
    sortBy,
    sortOrder,
  }: GetExtensionItemsParams): Promise<ExtensionItem[]> {
    try {
      await this.connect();

      const query: Record<string, any> = {};

      if (name) {
        query.name = { $regex: name, $options: "i" }; // case-insensitive regex
      }

      if (description) {
        query.description = { $regex: description, $options: "i" }; // case-insensitive regex
      }

      if (filterType) {
        query.type = filterType;
      }

      const sort: Record<string, any> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === "asc" ? 1 : -1;
      }

      const modelDtos = await this.collection.find(query).sort(sort).toArray();

      return modelDtos.map((dto) => as(dto));
    } catch (error) {
      throw new Error(`Error retrieving models from the database: ${error}`);
    } finally {
      await this.close();
    }
  }
}

function as(dto: Document): ExtensionItem {
  return {
    id: dto.id || "",
    image: dto.image,
    title: dto.title,
    description: dto.description,
    downloadTimes: dto.downloadTimes,
    starRating: dto.starRating,
    creator: dto.creator,
    creatorUrl: dto.creatorUrl,
    lastReleased: dto.lastReleased,
    lastUpdated: dto.lastUpdated,
    installUrl: dto.installUrl,
    published: dto.published,
    version: dto.version,
  };
}
