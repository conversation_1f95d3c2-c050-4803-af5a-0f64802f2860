"use client";

import { ChatFolder } from "@/model/chatFolder";
import { DisplayedMessage as ChatMessage } from "@/model/displayedMessage";
import { ChatRoom } from "@/model/chatroom";
import { PromptData as PromptLibrary } from "@/model/promptData";
import { v4 as uuidv4 } from "uuid";
import { openDB, DBSchema, IDBPDatabase, IDBPTransaction } from "idb";

interface CombinedDB extends DBSchema {
  ChatFolders: {
    value: ChatFolder;
    key: number;
    indexes: { folderName: string };
  };
  ChatMessages: {
    value: ChatMessage;
    key: number;
  };
  ChatRooms: {
    value: ChatRoom;
    key: number;
    indexes: { name: string };
  };
  PromptLibrarys: {
    value: PromptLibrary;
    key: number;
    indexes: { name: string };
  };
  Analytics: {
    value: {
      timestamp: Date;
      event: string;
      value: { [key: string]: any };
    };
    key: number;
    indexes: {
      eventWithTimestamp: string;
      sendMessageWithModel: string;
      timestamp: Date;
    };
  };
}

export class IndexDBRepo {
  private dbPromise: Promise<IDBPDatabase<CombinedDB>>;

  constructor() {
    this.dbPromise = openDB<CombinedDB>("key-value-store", 7, {
      upgrade(database, oldVersion, newVersion, transaction) {
        if (!database.objectStoreNames.contains("ChatFolders")) {
          const ChatFoldersStore = database.createObjectStore("ChatFolders", {
            keyPath: "id",
          });
          ChatFoldersStore.createIndex("folderName", "folderName", {
            unique: false,
          });
        }
        if (!database.objectStoreNames.contains("ChatMessages")) {
          database.createObjectStore("ChatMessages", { keyPath: "messageId" });
        }
        if (!database.objectStoreNames.contains("ChatRooms")) {
          const ChatRoomsStore = database.createObjectStore("ChatRooms", {
            keyPath: "id",
          });
          ChatRoomsStore.createIndex("name", "name", { unique: false });
        }
        if (!database.objectStoreNames.contains("PromptLibrarys")) {
          const PromptLibrarysStore = database.createObjectStore(
            "PromptLibrarys",
            { keyPath: "id" }
          );
          PromptLibrarysStore.createIndex("name", "name", { unique: false });
        }
        if (!database.objectStoreNames.contains("Analytics")) {
          const AnalyticsStore = database.createObjectStore("Analytics", {
            keyPath: "id",
            autoIncrement: true,
          });
          AnalyticsStore.createIndex(
            "eventWithTimestamp",
            ["event", "timestamp"],
            { unique: false }
          );
          AnalyticsStore.createIndex("timestamp", "timestamp", {
            unique: false,
          });
        }

        const analyticsStore = transaction.objectStore("Analytics");
        analyticsStore.createIndex(
          "sendMessageWithModel",
          ["event", "timestamp", "value.model"],
          {
            unique: false,
          }
        );
      },
    });
  }

  async getDB(): Promise<IDBPDatabase<CombinedDB>> {
    return await this.dbPromise;
  }

  // MARK: ChatFolder

  async saveChatFolder(newFolder: ChatFolder): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatFolders", "readwrite");
    const store = tx.objectStore("ChatFolders");

    await store.add(newFolder);
    await tx.done;
  }

  async getChatFolders(): Promise<ChatFolder[]> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatFolders", "readonly");
    const store = tx.objectStore("ChatFolders");

    const allChatFolders = await store.getAll();
    return allChatFolders
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .map((e) => ({ ...e, rooms: [] }));
  }

  async getChatFolderById(id: string): Promise<ChatFolder | null> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatFolders", "readonly");
    const store = tx.objectStore("ChatFolders");

    const folder = await store.get(IDBKeyRange.only(id));
    await tx.done;

    return folder || null;
  }

  async updateChatFolder(
    id: string,
    folderName: string,
    description: string
  ): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatFolders", "readwrite");
    const store = tx.objectStore("ChatFolders");

    const existingChatFolder = await store.get(IDBKeyRange.only(id));
    if (existingChatFolder) {
      existingChatFolder.folderName = folderName;
      existingChatFolder.folderDescription = description;
      await store.put(existingChatFolder);
    }
    await tx.done;
  }

  async deleteChatFolder(id: string): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatFolders", "readwrite");
    const store = tx.objectStore("ChatFolders");

    await store.delete(IDBKeyRange.only(id));
    await tx.done;
  }

  // MARK: ChatRoom

  async saveChatRoom(
    name: string,
    { folderId, id, tags }: { folderId?: string; id?: string; tags?: string[] }
  ): Promise<ChatRoom> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatRooms", "readwrite");
    const store = tx.objectStore("ChatRooms");

    const chatRoom: ChatRoom = {
      id: id || uuidv4(),
      name: name,
      folderId: folderId || "",
      tags: tags || [],
      createdAt: new Date(),
      messages: [],
      model: "",
      plugins: [],
    };

    await store.add(chatRoom);
    await tx.done;
    return chatRoom;
  }

  async getChatRooms(
    folderIds: string[]
  ): Promise<{ [key: string]: ChatRoom[] }> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatRooms", "readonly");
    const store = tx.objectStore("ChatRooms");

    const allChatRooms = await store.getAll();
    const filteredChatRooms = allChatRooms.filter((room) =>
      folderIds.includes(room.folderId)
    );
    const sortedChatRooms = filteredChatRooms.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );

    const groupedChatRooms: { [key: string]: ChatRoom[] } = {};
    sortedChatRooms.forEach((room) => {
      if (!groupedChatRooms[room.folderId]) {
        groupedChatRooms[room.folderId] = [];
      }
      groupedChatRooms[room.folderId].push(room);
    });

    return groupedChatRooms;
  }

  async getChatRoomById(id: string): Promise<ChatRoom | null> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatRooms", "readonly");
    const store = tx.objectStore("ChatRooms");

    const room = await store.get(IDBKeyRange.only(id));
    await tx.done;

    return room || null;
  }

  async updateChatRoom(id: string, updates: Partial<ChatRoom>): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatRooms", "readwrite");
    const store = tx.objectStore("ChatRooms");

    const existingChatRoom = await store.get(IDBKeyRange.only(id));
    if (existingChatRoom) {
      const updatedChatRoom = { ...existingChatRoom, ...updates };
      await store.put(updatedChatRoom);
    }
    await tx.done;
  }

  async deleteChatRoom(id: string): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("ChatRooms", "readwrite");
    const store = tx.objectStore("ChatRooms");

    await store.delete(IDBKeyRange.only(id));
    await tx.done;
  }

  // MARK: PromptLibrary

  async addPromptLibrary(newPrompt: PromptLibrary): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("PromptLibrarys", "readwrite");
    const store = tx.objectStore("PromptLibrarys");

    await store.add(newPrompt);
    await tx.done;
  }

  async getPromptLibrarys(): Promise<PromptLibrary[]> {
    const db = await this.dbPromise;
    const tx = db.transaction("PromptLibrarys", "readonly");
    const store = tx.objectStore("PromptLibrarys");

    const allPromptLibrarys = await store.getAll();
    return allPromptLibrarys.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
  }

  async getPromptLibraryById(id: string): Promise<PromptLibrary | null> {
    const db = await this.dbPromise;
    const tx = db.transaction("PromptLibrarys", "readonly");
    const store = tx.objectStore("PromptLibrarys");

    const prompt = await store.get(IDBKeyRange.only(id));
    await tx.done;

    return prompt || null;
  }

  async updatePromptLibrary(
    id: string,
    name: string,
    description: string,
    instruction: string
  ): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("PromptLibrarys", "readwrite");
    const store = tx.objectStore("PromptLibrarys");

    const existingPromptLibrary = await store.get(IDBKeyRange.only(id));
    if (existingPromptLibrary) {
      existingPromptLibrary.promptName = name;
      existingPromptLibrary.promptDescription = description;
      existingPromptLibrary.promptInstruction = instruction;
      await store.put(existingPromptLibrary);
    }
    await tx.done;
  }

  async deletePromptLibrary(id: string): Promise<void> {
    const db = await this.dbPromise;
    const tx = db.transaction("PromptLibrarys", "readwrite");
    const store = tx.objectStore("PromptLibrarys");

    await store.delete(IDBKeyRange.only(id));
    await tx.done;
  }

  // MARK: Analytics
  async storeAnalyticsEvent(event: string, value: { [key: string]: any }) {
    const db = await this.dbPromise;
    const tx = db.transaction("Analytics", "readwrite");
    const store = tx.objectStore("Analytics");

    const newEvent = {
      timestamp: new Date(),
      event,
      value,
    };

    await store.add(newEvent);
    await tx.done;
  }

  async countEventsByDay(event: string, daysAgo = 7) {
    const db = await this.dbPromise;
    const tx = db.transaction("Analytics", "readonly");
    const store = tx.objectStore("Analytics");

    const index = store.index("eventWithTimestamp");
    const results: Record<string, number> = {};

    const upperBound = new Date();
    const lowerBound = new Date();
    lowerBound.setDate(lowerBound.getDate() - daysAgo);

    const range = IDBKeyRange.bound([event, lowerBound], [event, upperBound]);

    let cursor = await index.openCursor(range);

    while (cursor) {
      const record = cursor.value;
      const date = new Date(record.timestamp);
      const dayKey = date.toISOString().split("T")[0];

      results[dayKey] = (results[dayKey] || 0) + 1;

      cursor = await cursor.continue();
    }

    await tx.done;

    return results;
  }

  async countEventsByModel(event = "Send Message", daysAgo = 7) {
    const db = await this.dbPromise;
    const tx = db.transaction("Analytics", "readonly");
    const store = tx.objectStore("Analytics");

    const index = store.index("sendMessageWithModel");
    const results: Record<string, number> = {};

    const upperBound = new Date();
    const lowerBound = new Date();
    lowerBound.setDate(lowerBound.getDate() - daysAgo);

    const range = IDBKeyRange.bound([event, lowerBound], [event, upperBound]);

    let cursor = await index.openCursor(range);

    while (cursor) {
      const record = cursor.value;
      const model = record.value.model;

      if (!results[model]) {
        results[model] = 0;
      }

      results[model] += 1;

      cursor = await cursor.continue();
    }

    await tx.done;

    return results;
  }
}
