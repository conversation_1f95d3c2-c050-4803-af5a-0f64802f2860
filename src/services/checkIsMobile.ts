const checkIsMobile = () => {
  if (typeof window === "undefined") {
    return false;
  }
  const userAgent =
    typeof window.navigator === "undefined" ? "" : navigator.userAgent;
  const mobileRegExp =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
  const userAgentIsMobile = mobileRegExp.test(userAgent);
  const windowWidthIsMobile = window.innerWidth < 700;
  const isMobile = userAgentIsMobile || windowWidthIsMobile;
  return isMobile;
};

export default checkIsMobile;
