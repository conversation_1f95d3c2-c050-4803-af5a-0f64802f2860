import { IndexDBRepo } from "@/repo/IndexDb";
import posthog from "posthog-js";

export default class Analytics {
  private static instance: Analytics | null = null;
  private posthogInstance: any | null = null;
  private indexDb: IndexDBRepo = new IndexDBRepo();

  private constructor() {
    const token = process.env.NEXT_PUBLIC_POSTHOG_TOKEN || "";
    if (token) {
      this.posthogInstance = posthog.init(token, {
        api_host: "https://app.posthog.com",
        autocapture: false,
      });
    }
  }

  static getInstance(): Analytics {
    if (!Analytics.instance) {
      Analytics.instance = new Analytics();
    }
    return Analytics.instance;
  }

  track(event: string, properties: { [key: string]: any }) {
    if (this.posthogInstance) {
      this.posthogInstance.capture(event, properties);
    }

    this.indexDb.storeAnalyticsEvent(event, properties);
  }
}
