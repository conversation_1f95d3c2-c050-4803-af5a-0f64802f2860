import { GeneralPopUp } from "@/components/Popup";

export function getUserEmailOrPopUp(
  openPopup: (popup: GeneralPopUp) => void
): string | null {
  const email = localStorage.getItem("userEmail");

  if (!email) {
    openPopup(
      new GeneralPopUp({
        title: "You need to refresh this website to input email address",
        content: "You can input the email address at the Welcome popup",
        context: "Join Mobile App Waitlist",
        buttons: [
          {
            title: "Refresh",
            isPrimary: true,
            onClick: () => {
              location.reload();
            },
          },
          {
            title: "Cancel",
            isPrimary: false,
            onClick: () => {},
          },
        ],
      })
    );
    return null;
  }
  return email;
}
