"use client";

import { AppearanceSetting } from "@/model/appSetting";
import React, { createContext, useContext } from "react";

interface RootContextProps {
  leftBar: boolean;
  rightBar: boolean;
  appearanceSetting: AppearanceSetting;
}

const RootContext = createContext<RootContextProps | undefined>(undefined);

export const RootProvider: React.FC<{
  children: React.ReactNode;
  value: RootContextProps;
}> = ({ children, value }) => {
  return <RootContext.Provider value={value}>{children}</RootContext.Provider>;
};

export const useRoot = () => {
  const context = useContext(RootContext);
  if (!context) {
    throw new Error("useRoot must be used within a RootProvider");
  }
  return context;
};
