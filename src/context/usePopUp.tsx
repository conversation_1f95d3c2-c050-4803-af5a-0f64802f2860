import { PopupOverlay } from "@/components/Popup";
import { PopUp } from "@/model/popup";
import React, { createContext, useState, useContext, ReactNode } from "react";

interface PopupContextType {
  popUp: PopUp | null;
  openPopup: (popUp: PopUp, delay?: number) => void;
  closePopup: () => void;
}

const PopupContext = createContext<PopupContextType | undefined>(undefined);

export function usePopup(): PopupContextType {
  const context = useContext(PopupContext);

  if (!context) {
    throw new Error("usePopup must be used within a PopupProvider");
  }

  return context;
}

interface PopupProviderProps {
  children: ReactNode;
}

export function PopupProvider({
  children,
}: PopupProviderProps): React.ReactNode {
  const [popUp, setPopUp] = useState<PopUp | null>(null);

  const openPopup = async (popup: PopUp, delay?: number) => {
    if (delay) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
    setPopUp(popup);
  };

  const closePopup = () => {
    setPopUp(null);
  };

  return (
    <PopupContext.Provider value={{ popUp, openPopup, closePopup }}>
      {children}
      <PopupOverlay />
    </PopupContext.Provider>
  );
}
