import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
  useMemo,
} from "react";

import { Plugin } from "@/model/plugin";

interface PluginContextType {
  plugins: Plugin[];
  pluginsByIdMap: { [key: string]: Plugin };
}

const PluginContext = createContext<PluginContextType | undefined>(undefined);

export function usePlugin(): PluginContextType {
  const context = useContext(PluginContext);

  if (!context) {
    throw new Error("usePlugin must be used within a PluginProvider");
  }

  return context;
}

interface PluginProviderProps {
  children: ReactNode;
}

export function PluginProvider({
  children,
}: PluginProviderProps): React.ReactNode {
  const [plugins, setPlugins] = useState<Plugin[]>([]);

  const fetchPlugins = async (): Promise<Plugin[]> => {
    return new Promise<Plugin[]>((resolve) => {
      setTimeout(() => {
        const plugins: Plugin[] = [
          {
            id: "1",
            name: "<PERSON><PERSON><PERSON><PERSON>",
            icon: "🌤️",
            spec: JSON.stringify({
              type: "function",
              function: {
                name: "get_weather",
                strict: true,
                parameters: {
                  type: "object",
                  properties: {
                    location: { type: "string" },
                    unit: { type: "string", enum: ["c", "f"] },
                  },
                  required: ["location", "unit"],
                  additionalProperties: false,
                },
              },
            }),
            source:
              "function get_weather(params) { return axios.get(`https://api.weather.com/v3/weather/conditions?location=${params.location}&unit=${params.unit}`); }",
            overview: `This plugin gets weather data for a given location.`,
            settings: [],
          },
          {
            id: "2",
            name: "SimpleAddition",
            icon: "➕",
            spec: JSON.stringify({
              type: "function",
              function: {
                name: "simple_addition",
                strict: true,
                parameters: {
                  type: "object",
                  properties: {
                    number1: { type: "number" },
                    number2: { type: "number" },
                  },
                  required: ["number1", "number2"],
                  additionalProperties: false,
                },
              },
            }),
            source: `function simple_addition(params) { 
                 const number1 = params.number1;
                 const number2 = params.number2;
                 return number1 + number2;
              }`,
            overview: "This plugin adds two numbers.",
            settings: [],
          },
          {
            id: "3",
            name: "GetLocalStorage",
            icon: "📦",
            spec: JSON.stringify({
              type: "function",
              function: {
                name: "get_localstorage",
                strict: true,
                parameters: {
                  type: "object",
                  properties: {
                    key: { type: "string" },
                  },
                  required: ["key"],
                  additionalProperties: false,
                },
              },
            }),
            source:
              "function get_localstorage(params) { return localStorage.getItem(params.key); }",
            overview: `This plugin gets data from local storage.`,
            settings: [],
          },
        ];
        resolve(plugins);
      }, 1000);
    });
  };

  useEffect(() => {
    fetchPlugins().then((plugins) => {
      setPlugins(plugins);
    });
  }, []);

  const pluginsByIdMap = useMemo(() => {
    const pluginsByIdMap: { [key: string]: Plugin } = {};

    plugins.forEach((plugin) => {
      pluginsByIdMap[plugin.id] = plugin;
    });
    return pluginsByIdMap;
  }, [plugins]);

  return (
    <PluginContext.Provider value={{ plugins, pluginsByIdMap }}>
      {children}
    </PluginContext.Provider>
  );
}
