import { PromptData } from "@/model/promptData";
import PromptLibraryListViewModel from "@/viewModel/promptsViewModel";
import React, { createContext, useState, useContext, ReactNode } from "react";

interface PromptLibraryContextType {
  prompt: PromptData | null;
  viewModel: PromptLibraryListViewModel;
  usedPrompt: PromptData | null;
  setUsePrompt: (prompt: PromptData | null) => void;
  openPromptLibrary: (prompt: PromptData) => void;
  closePromptLibrary: () => void;
  deletePromptLibrary: (prompt: PromptData) => void;
}

const PromptLibraryContext = createContext<
  PromptLibraryContextType | undefined
>(undefined);

export function usePromptLibrary(): PromptLibraryContextType {
  const context = useContext(PromptLibraryContext);

  if (!context) {
    throw new Error(
      "usePromptLibrary must be used within a PromptLibraryProvider"
    );
  }

  return context;
}

interface PromptLibraryProviderProps {
  children: ReactNode;
}

export function PromptLibraryProvider({
  children,
}: PromptLibraryProviderProps): React.ReactNode {
  const [editPrompt, setEditPrompt] = useState<PromptData | null>(null);
  const [usedPrompt, setUsedPrompt] = useState<PromptData | null>(null);
  const viewModel = new PromptLibraryListViewModel();

  const openPromptLibrary = (popup: PromptData) => {
    setEditPrompt(popup);
  };

  function setUsePrompt(prompt: PromptData | null) {
    setUsedPrompt(prompt);
  }

  const closePromptLibrary = () => {
    setEditPrompt(null);
  };

  const deletePromptLibrary = (prompt: PromptData) => {
    viewModel.deletePrompt(prompt);
  };

  return (
    <PromptLibraryContext.Provider
      value={{
        prompt: editPrompt,
        usedPrompt,
        viewModel,
        setUsePrompt,
        openPromptLibrary,
        closePromptLibrary,
        deletePromptLibrary,
      }}
    >
      {children}
    </PromptLibraryContext.Provider>
  );
}
