import { ChatRoom } from "@/model/chatroom";
import RoomListViewModel from "@/viewModel/roomListViewModel";
import { useRouter } from "next/navigation";
import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";
import { usePopup } from "./usePopUp";
import { CustomPopUp } from "@/components/Popup";

interface ChatRoomContextType {
  openRoom: ChatRoom | null;
  editRoom: ChatRoom | null;
  viewModel: RoomListViewModel;
  openChatRoom: (room: ChatRoom | null) => void;
  closeChatRoom: () => void;
  openEditChatRoom: (room: ChatRoom) => void;
  closeEditChatRoom: () => void;
}

const ChatRoomContext = createContext<ChatRoomContextType | undefined>(
  undefined
);

export function useChatRoom(): ChatRoomContextType {
  const context = useContext(ChatRoomContext);

  if (!context) {
    throw new Error("useChatRoom must be used within a ChatRoomProvider");
  }

  return context;
}

interface ChatRoomProviderProps {
  children: ReactNode;
}

export function ChatRoomProvider({
  children,
}: ChatRoomProviderProps): React.ReactNode {
  const [editRoom, setEditRoom] = useState<ChatRoom | null>(null);
  const [openRoom, setOpenRoom] = useState<ChatRoom | null>(null);
  const { openPopup } = usePopup();
  const viewModel = new RoomListViewModel();
  const router = useRouter();

  const openEditChatRoom = (room: ChatRoom) => {
    setEditRoom(room);
  };

  const openChatRoom = (room: ChatRoom | null) => {
    // this causes opening room with long chat will make the page overflow
    // if (room && room.name !== "") {
    //   router.push(`?c=${room.id}`);
    // }
    setOpenRoom(room);
  };

  const closeChatRoom = () => {
    setOpenRoom(null);
  };

  const closeEditChatRoom = () => {
    setEditRoom(null);
  };

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const roomId = query.get("c");
    if (roomId) {
      viewModel.getRoomById(roomId).then((room) => {
        if (room) {
          setOpenRoom(room);
        } else {
        }
      });
    }
  }, []);

  return (
    <ChatRoomContext.Provider
      value={{
        editRoom,
        openRoom,
        openChatRoom,
        closeChatRoom,
        viewModel,
        openEditChatRoom,
        closeEditChatRoom,
      }}
    >
      {children}
    </ChatRoomContext.Provider>
  );
}
