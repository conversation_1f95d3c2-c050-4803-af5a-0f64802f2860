import { ChatFolder } from "@/model/chatFolder";
import ChatFolderListViewModel from "@/viewModel/chatFolderViewModel";
import React, { createContext, useState, useContext, ReactNode } from "react";

interface ChatFolderContextType {
  folder: ChatFolder | null;
  viewModel: ChatFolderListViewModel;
  openChatFolder: (folder: ChatFolder) => void;
  closeChatFolder: () => void;
}

const ChatFolderContext = createContext<ChatFolderContextType | undefined>(
  undefined
);

export function useChatFolder(): ChatFolderContextType {
  const context = useContext(ChatFolderContext);

  if (!context) {
    throw new Error("useChatFolder must be used within a ChatFolderProvider");
  }

  return context;
}

interface ChatFolderProviderProps {
  children: ReactNode;
}

export function ChatFolderProvider({
  children,
}: ChatFolderProviderProps): React.ReactNode {
  const [editFolder, setEditFolder] = useState<ChatFolder | null>(null);
  const viewModel = new ChatFolderListViewModel();

  const openChatFolder = (folder: ChatFolder) => {
    setEditFolder(folder);
  };

  const closeChatFolder = () => {
    setEditFolder(null);
  };

  return (
    <ChatFolderContext.Provider
      value={{ folder: editFolder, viewModel, openChatFolder, closeChatFolder }}
    >
      {children}
    </ChatFolderContext.Provider>
  );
}
