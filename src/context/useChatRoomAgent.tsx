import { AIAgent } from "@/model/ai_agent";
import { ChatRoom } from "@/model/chatroom";
import { Serialize } from "@/utils/Serialization";
import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";

interface ChatRoomAIAgentContextType {
  selectedAgent: AIAgent | null;
  setSelectedAgent: (agent: AIAgent | null) => void;
  setChatRoom: (chatRoom: ChatRoom) => void;
}

const ChatRoomAIAgentContext = createContext<
  ChatRoomAIAgentContextType | undefined
>(undefined);

export function useChatRoomAIAgent(): ChatRoomAIAgentContextType {
  const context = useContext(ChatRoomAIAgentContext);

  if (!context) {
    throw new Error("useAIAgent must be used within a AIAgentProvider");
  }

  return context;
}

interface AIAgentProviderProps {
  children: ReactNode;
}

export function ChatRoomAIAgentProvider({
  children,
}: AIAgentProviderProps): React.ReactNode {
  const [selectedAgent, setSelectedAgent] = useState<AIAgent | null>(null);

  function onSetChatRoom(chatRoom: ChatRoom) {
    setSelectedAgent(chatRoom.aiAgent || null);
  }

  return (
    <ChatRoomAIAgentContext.Provider
      value={{ selectedAgent, setSelectedAgent, setChatRoom: onSetChatRoom }}
    >
      {children}
    </ChatRoomAIAgentContext.Provider>
  );
}
