import { tomio_model } from "@/app/const/tomio_model";
import { Model } from "@/model/ai_model";
import { Serialize } from "@/utils/Serialization";
import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";

interface ModelContextType {
  models: Array<Model>;
  defaultModel: Model;
  setModels: (models: Array<Model>) => void;
}

const ModelContext = createContext<ModelContextType | undefined>(undefined);

export function useModel(): ModelContextType {
  const context = useContext(ModelContext);

  if (!context) {
    throw new Error("useModel must be used within a ModelProvider");
  }

  return context;
}

interface ModelProviderProps {
  children: ReactNode;
}

export function ModelProvider({
  children,
}: ModelProviderProps): React.ReactNode {
  const [models, setModelsState] = useState<Array<Model>>([]);

  const setModels = (models: Array<Model>) => {
    setModelsState(models);
    const serializedModels = Serialize.serialize(models);
    localStorage.setItem(
      "models",
      JSON.stringify(Array.from(serializedModels))
    );
  };

  const fetchModelsFromAPI = async () => {
    try {
      const response = await fetch("/api/v1/models");
      if (!response.ok) {
        throw new Error("Failed to fetch models");
      }
      const apiModels: Model[] = await response.json();

      const localModels = fetchLocalModels();

      const modelMap = new Map();

      localModels.forEach((localModel) => {
        modelMap.set(localModel.id, localModel);
      });

      apiModels.forEach((apiModel) => {
        if (modelMap.has(apiModel.id)) {
          const mergedModel = { ...modelMap.get(apiModel.id), ...apiModel };
          modelMap.set(apiModel.id, mergedModel);
        } else {
          modelMap.set(apiModel.id, apiModel);
        }
      });

      const updatedModels = Array.from(modelMap.values());

      setModels(updatedModels);
    } catch (error) {
      console.error("Error fetching models:", error);
    }
  };

  const fetchLocalModels = (): Model[] => {
    const localModels = localStorage.getItem("models");
    if (localModels) {
      const uint8Array = new Uint8Array(JSON.parse(localModels));
      const models = Serialize.deserialize(uint8Array) as Model[];
      return [tomio_model, ...models];
    } else {
      return [];
    }
  };

  useEffect(() => {
    const localModels = fetchLocalModels();
    setModelsState(localModels);

    fetchModelsFromAPI();
  }, []);

  return (
    <ModelContext.Provider
      value={{ models, defaultModel: tomio_model, setModels }}
    >
      {children}
    </ModelContext.Provider>
  );
}
