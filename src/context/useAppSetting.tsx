import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import {
  AppearanceSetting,
  AppSetting,
  AppSettingTheme,
} from "@/model/appSetting";
import defaultAppearanceSetting from "@/consts/defaultAppearanceSetting.json";
import { isEqual } from "lodash";

interface AppSettingsContextType {
  settings: AppSetting;
  setSettings: React.Dispatch<React.SetStateAction<AppSetting>>;
  resetAllSettings: () => void;
}

const AppSettingsContext = createContext<AppSettingsContextType | null>(null);

interface AppSettingsContextType {
  settings: AppSetting;
  setSettings: React.Dispatch<React.SetStateAction<AppSetting>>;
  resetAllSettings: () => void;
}

const DEFAULT_SETTING: AppSetting = {
  // FIXME: Probably handling as unknown is not best approach
  appearance: defaultAppearanceSetting as unknown as AppearanceSetting,
  ai: {
    autoGenerateChatTitle: true,
    streamAIResponse: true,
    autoContinueResponse: true,
    autoSaveChat: true,
    submitWithEnter: true,
    autoFocusInput: true,
    defaultTone: "neutral",
    defaultSystemPrompt: "",
    defaultModel: "",
    enableWebAccess: true,
    enablePlugins: true,
    smartRetry: true,
    showTokenUsage: false,
  },
  data: {
    analytics: {
      enabled: false,
      provider: undefined,
      isCustom: false,
    },
    storage: {
      enabled: false,
      provider: undefined,
      isCustom: false,
    },
  },
  audio: {
    textToSpeech: {
      enabled: false,
      provider: undefined,
      isCustom: false,
      playButtonForMessage: false,
      autoPlay: false,
    },
    speechToText: {
      enabled: false,
      language: "en-US",
      autoRecord: false,
      continueRecordAfterAI: false,
      autoSend: false,
      provider: undefined,
      isCustom: false,
    },
  },
};

type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

function deepMerge(
  defaultSettings: AppSetting,
  userSettings: DeepPartial<AppSetting>
): AppSetting {
  return Object.entries(defaultSettings).reduce((acc, [key, defaultValue]) => {
    if (Object.prototype.hasOwnProperty.call(userSettings, key)) {
      const userValue = userSettings[key as keyof AppSetting];

      if (
        typeof defaultValue === "object" &&
        defaultValue !== null &&
        !Array.isArray(defaultValue)
      ) {
        acc[key as keyof AppSetting] = deepMerge(
          defaultValue as any,
          userValue as any
        ) as any;
      } else {
        acc[key as keyof AppSetting] =
          userValue !== undefined ? userValue : defaultValue;
      }
    } else {
      acc[key as keyof AppSetting] = defaultValue;
    }

    return acc;
  }, {} as AppSetting);
}

export const AppSettingsProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [settings, setSettings] = useState<AppSetting>(DEFAULT_SETTING);

  function resetAllSettings() {
    localStorage.setItem("appSettings", JSON.stringify(DEFAULT_SETTING));
    setSettings(DEFAULT_SETTING);
  }

  useEffect(() => {
    const storedSettings = localStorage.getItem("appSettings");
    if (storedSettings) {
      const parsedSettings = JSON.parse(storedSettings);
      const mergedSettings = deepMerge(DEFAULT_SETTING, parsedSettings);
      setSettings(mergedSettings);
    }
  }, []);

  useEffect(() => {
    if (isEqual(settings, DEFAULT_SETTING)) {
      return;
    }
    localStorage.setItem("appSettings", JSON.stringify(settings));
  }, [settings]);

  return (
    <AppSettingsContext.Provider
      value={{ settings, setSettings, resetAllSettings }}
    >
      {children}
    </AppSettingsContext.Provider>
  );
};

export const useAppSettings = () => {
  const context = useContext(AppSettingsContext);
  if (context === null) {
    throw new Error(
      "useAppSettings must be used within an AppSettingsProvider"
    );
  }
  return context;
};
