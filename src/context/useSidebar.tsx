"use client";

import React, {
  createContext,
  useContext,
  useState,
  useRef,
  MutableRefObject,
} from "react";

type SidebarOpenState = {
  left: boolean;
  right: boolean;
};

type SidebarContextType = {
  open: SidebarOpenState;
  isOpenOne: boolean;
  setOpen: (type: "left" | "right", isOpen: boolean) => void;
  refs: {
    left: MutableRefObject<HTMLElement | null>;
    right: MutableRefObject<HTMLElement | null>;
  };
};

const SidebarContext = createContext<SidebarContextType | null>(null);

export const SidebarProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const leftRef = useRef<HTMLElement | null>(null);
  const rightRef = useRef<HTMLElement | null>(null);

  const [open, setOpenState] = useState<SidebarOpenState>({
    left: false,
    right: false,
  });

  const setOpen = (type: "left" | "right", isOpen: boolean) => {
    setOpenState((prev) => ({ ...prev, [type]: isOpen }));
  };

  return (
    <SidebarContext.Provider
      value={{
        open,
        isOpenOne: open.left || open.right,
        setOpen,
        refs: { left: leftRef, right: rightRef },
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebarContext = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebarContext must be used within a SidebarProvider");
  }
  return context;
};
