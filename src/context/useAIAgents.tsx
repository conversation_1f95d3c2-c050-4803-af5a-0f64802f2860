import { AIAgent } from "@/model/ai_agent";
import { Serialize } from "@/utils/Serialization";
import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
} from "react";

interface AIAgentContextType {
  agents: Array<AIAgent>;
}

const AIAgentContext = createContext<AIAgentContextType | undefined>(undefined);

export function useAIAgent(): AIAgentContextType {
  const context = useContext(AIAgentContext);

  if (!context) {
    throw new Error("useAIAgent must be used within a AIAgentProvider");
  }

  return context;
}

interface AIAgentProviderProps {
  children: ReactNode;
}

export function AIAgentProvider({
  children,
}: AIAgentProviderProps): React.ReactNode {
  const [agents, setAIAgentsState] = useState<Array<AIAgent>>([]);

  const setAIAgents = (agents: Array<AIAgent>) => {
    setAIAgentsState(agents);
    const serializedAIAgents = Serialize.serialize(agents);
    localStorage.setItem(
      "agents",
      JSON.stringify(Array.from(serializedAIAgents))
    );
  };

  const fetchAIAgentsFromAPI = async () => {
    try {
      const response = await fetch("/api/v1/agents");
      if (!response.ok) {
        throw new Error("Failed to fetch agents");
      }
      const apiAIAgents: AIAgent[] = await response.json();

      setAIAgents(apiAIAgents);
    } catch (error) {
      console.error("Error fetching agents:", error);
    }
  };

  useEffect(() => {
    fetchAIAgentsFromAPI();
  }, []);

  return (
    <AIAgentContext.Provider value={{ agents }}>
      {children}
    </AIAgentContext.Provider>
  );
}
