import React, { createContext, useContext, useState } from "react";

export const FeedbackContext = createContext<
  | {
      showFeedbackPopup: boolean;
      setShowFeedbackPopup: (show: boolean) => void;
    }
  | undefined
>(undefined);

export const FeedbackProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [showFeedbackPopup, setShowFeedbackPopup] = useState(false);

  return (
    <FeedbackContext.Provider
      value={{ showFeedbackPopup, setShowFeedbackPopup }}
    >
      {children}
    </FeedbackContext.Provider>
  );
};

export const useFeedback = () => {
  const context = useContext(FeedbackContext);

  if (!context) {
    throw new Error("useFeedback must be used inside the FeedbackProvider");
  }

  return context;
};
