import { ChatFolder } from "@/model/chatFolder";
import { ChatFolderRepo } from "@/repo/chatFolder";
import { v4 as uuidv4 } from "uuid";

export default class ChatFolderListViewModel {
  onFoldersUpdated: () => void = () => {};

  private folders: ChatFolder[] = [];

  constructor() {}

  async loadFolders(): Promise<void> {
    const dbFolders = await ChatFolderRepo.getInstance()
      .getChatFolderRepo()
      .getChatFolders();
    this.folders = dbFolders;
    this.onFoldersUpdated();
  }

  async createNewFolder(): Promise<ChatFolder> {
    const newFolder: ChatFolder = {
      id: "",
      folderName: `New Folder`,
      folderDescription: "",
      createdAt: new Date(),
      rooms: [],
    };
    return newFolder;
  }

  async saveNewFolder(_newFolder: ChatFolder): Promise<void> {
    const newFolder = {
      ..._newFolder,
      id: uuidv4(),
    };
    await ChatFolderRepo.getInstance()
      .getChatFolderRepo()
      .saveChatFolder(newFolder);
    this.folders = [newFolder, ...this.folders];
    this.onFoldersUpdated();
  }

  async deleteChatRoom(id: string) {
    ChatFolderRepo.getInstance().getChatFolderRepo().deleteChatFolder(id);
    this.onFoldersUpdated();
  }

  getFolders(): ChatFolder[] {
    return this.folders;
  }

  async updateFolder(folder: ChatFolder): Promise<void> {
    await ChatFolderRepo.getInstance()
      .getChatFolderRepo()
      .updateChatFolder(folder.id, folder.folderName, folder.folderDescription);
    this.loadFolders();
  }
}
