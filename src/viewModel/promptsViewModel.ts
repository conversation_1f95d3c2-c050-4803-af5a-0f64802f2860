import { PromptData } from "@/model/promptData";
import { PromptLibraryRepo } from "@/repo/promptLibrary";
import { v4 as uuidv4 } from "uuid";

export default class PromptLibraryListViewModel {
  onPromptsUpdated: () => void = () => {};

  private prompts: PromptData[] = [];

  constructor() {}

  async loadPrompts(): Promise<void> {
    const dbpromts = await PromptLibraryRepo.getInstance()
      .getPromptLibraryRepo()
      .getPromptLibrarys();
    this.prompts = dbpromts;
    this.onPromptsUpdated();
  }

  async createNewPropmt(): Promise<PromptData> {
    const newPrompt: PromptData = {
      id: "",
      promptName: "",
      promptDescription: "",
      promptInstruction: "",
      createdAt: new Date(),
    };
    return newPrompt;
  }

  async addNewPrompt(prompt: PromptData): Promise<void> {
    const newPrompt: PromptData = {
      id: uuidv4(),
      promptName: prompt.promptName,
      promptDescription: prompt.promptDescription,
      promptInstruction: prompt.promptInstruction,
      createdAt: prompt.createdAt,
    };
    await PromptLibraryRepo.getInstance()
      .getPromptLibraryRepo()
      .addPromptLibrary(newPrompt);
    this.loadPrompts();
  }

  getPrompts(): PromptData[] {
    return this.prompts;
  }

  async updatePrompt(prompt: PromptData): Promise<void> {
    await PromptLibraryRepo.getInstance()
      .getPromptLibraryRepo()
      .updatePromptLibrary(
        prompt.id,
        prompt.promptName,
        prompt.promptDescription,
        prompt.promptInstruction
      );
    this.loadPrompts();
  }

  async deletePrompt(prompt: PromptData): Promise<void> {
    await PromptLibraryRepo.getInstance()
      .getPromptLibraryRepo()
      .deletePromptLibrary(prompt.id);
    this.loadPrompts();
  }
}
