import { DisplayedMessage } from "@/model/displayedMessage";
import { PromptData } from "@/model/promptData";
import { SendMessage } from "@/model/sendMessage";
import LocalRepo from "@/repo/chatMessages/local";
import { v4 as uuidv4 } from "uuid";
import { <PERSON><PERSON>and<PERSON> } from "@/utils/AIHandler";
import { Model } from "@/model/ai_model";
import { Plugin } from "@/model/plugin";
import { Tool } from "@/utils/AIModelStream";
import { AIAgent } from "@/model/ai_agent";
import { ModelVendorAPI } from "@/utils/scripts_ai";

type ChatMessagesRepository = LocalRepo;

export default class ChatRoomViewModel {
  private id: string;
  private sendMessages: SendMessage[] = [];
  private displayedMessages: DisplayedMessage[] = [];
  private model: Model;
  private aiHandler: AIHandler;
  private streamController: AbortController | null = null;
  user: string;
  repo: ChatMessagesRepository;
  private worker = new Worker("worker.js");

  constructor(user: string, repo: ChatMessagesRepository, model: Model) {
    this.id = uuidv4();
    this.user = user;
    this.repo = repo;
    this.model = model;
    this.aiHandler = new AIHandler(
      model.endpoint,
      model.headers,
      model.keepLocal,
      model.config.model,
      model.config.temperature,
      model.config.top_p,
      model.config.frequencyPenalty,
      model.config.presencePenalty,
      model.config.maxTokens,
      model.config.n,
      undefined,
      model.model_vendor_api as ModelVendorAPI
    );
  }

  onMessagesUpdated: () => void = () => {};
  setLoading: (value: boolean) => void = () => {};
  setWriting: (value: boolean) => void = () => {};
  setShowFollowButton: (value: boolean) => void = () => {};
  onDidSendUserMessage: () => void = () => {};
  onAutoscroll: () => void = () => {};

  getDisplayedMessages(): Readonly<DisplayedMessage[]> {
    return this.displayedMessages;
  }

  updateHandler(model: Model, plugins: Plugin[]) {
    try {
      this.model = model;

      const tools: Tool[] = plugins
        .map((plugin) => {
          try {
            return JSON.parse(plugin.spec);
          } catch (parseError) {
            console.error(
              `Failed to parse plugin spec for plugin ${plugin.name}:`,
              parseError
            );
            return null; // Handle or skip invalid plugin specs as needed
          }
        })
        .filter(Boolean) as Tool[]; // Filter out any null values from failed parses

      this.aiHandler = new AIHandler(
        model.endpoint,
        model.headers,
        model.keepLocal,
        model.config.model,
        model.config.temperature,
        model.config.top_p,
        model.config.frequencyPenalty,
        model.config.presencePenalty,
        model.config.maxTokens,
        model.config.n,
        tools.length > 0 ? tools : undefined,
        model.model_vendor_api as ModelVendorAPI
      );
    } catch (e) {
      console.error("Error initializing AIHandler:", e);
    }
  }

  resetMessages = () => {
    this.sendMessages = [];
    this.displayedMessages = [];
    this.repo.deleteAllChatMessages();
    this.onMessagesUpdated();
  };

  prepareMessageForSharing = () => {
    return this.displayedMessages;
  };

  clearContext = () => {
    this.sendMessages = [];
    this.onMessagesUpdated();
  };

  deleteMessage(id: string) {
    const toRemove = this.displayedMessages.find((m) => m.messageId === id);
    this.sendMessages = this.sendMessages.filter(
      (m) => m.content !== toRemove?.content
    );
    this.displayedMessages = this.displayedMessages.filter(
      (e) => e.messageId !== toRemove?.messageId
    );
    this.repo.deleteChatMessage(id);
    this.onMessagesUpdated();
  }

  async appendMessages(messages: DisplayedMessage[]) {
    for (const message of messages) {
      await this.repo.addChatMessage(message);
    }
    this.displayedMessages = [...this.displayedMessages, ...messages];
    this.onMessagesUpdated();
  }

  async replaceMessages(messages: DisplayedMessage[]) {
    await this.repo.deleteAllChatMessages();
    for (const message of messages) {
      await this.repo.addChatMessage(message);
    }
    this.displayedMessages = messages;
    this.onMessagesUpdated();
  }

  regenerateMessages = async () => {
    let messageCount = this.displayedMessages.length;
    if (messageCount == 0) {
      return;
    }
    let lastUserMessageIndex = 0;
    let i = this.displayedMessages.length - 1;

    while (messageCount > 0) {
      const message = this.displayedMessages[i];
      await this.repo.deleteChatMessage(message.messageId);
      if (message.role === "user") {
        lastUserMessageIndex = i;
        break;
      }
      i--;
      messageCount--;
    }

    const lastUserMessage = this.displayedMessages[lastUserMessageIndex];
    if (lastUserMessage.role !== "user") {
      return;
    }

    this.displayedMessages.splice(lastUserMessageIndex);
    this.sendMessages.splice(lastUserMessageIndex);
    this.sendMessage(
      lastUserMessage.content,
      lastUserMessage.prompt,
      lastUserMessage.plugins
    );

    this.onMessagesUpdated();
  };

  withPrompt(message: string, prompt: PromptData) {
    return `
            ${prompt.promptInstruction}
            '''
            ${message}
            '''
            `;
  }

  async loadMessages() {
    const messages = await this.repo.getChatMessages();
    this.displayedMessages = messages;
    this.sendMessages = messages.map((message) =>
      this.displayedMessageToSendMessage(message)
    );
    this.onMessagesUpdated();
  }

  displayedMessageToSendMessage(displayedMessage: DisplayedMessage) {
    const sendMessage: SendMessage = {
      role: displayedMessage.role,
      content: displayedMessage.prompt
        ? this.withPrompt(displayedMessage.content, displayedMessage.prompt)
        : displayedMessage.content || displayedMessage.info?.meta.tool_content,
      tool_call_id: displayedMessage.info?.meta.tool_call_id,
      tool_calls: displayedMessage.info?.meta.tool_calls,
    };
    return sendMessage;
  }
  async importMessage(message: {
    role: "user" | "assistant";
    content: string;
    source?: string;
  }) {
    const newImportedMessage: DisplayedMessage = {
      messageId: uuidv4(),
      role: message.role,
      content: message.content,
      prompt: null,
      timestamp: new Date(),
      isWriting: false,
      modelName: message.source || "",
      searchEngineQueries: [],
      plugins: [],
    };
    const newDisplayedMessages = [
      ...this.displayedMessages,
      newImportedMessage,
    ];
    await this.repo.addChatMessage(newImportedMessage);
    this.displayedMessages = newDisplayedMessages;
    this.onMessagesUpdated();
  }

  async sendMessage(
    message: string,
    prompt: PromptData | null = null,
    plugins: Plugin[],
    agent?: AIAgent
  ) {
    this.setLoading(true);

    const newAssistantDisplayedMessage: DisplayedMessage = {
      messageId: uuidv4(),
      role: "user",
      content: message,
      prompt: prompt,
      timestamp: new Date(),
      isWriting: false,
      modelName: "",
      searchEngineQueries: [],
      plugins: plugins,
    };

    const systemPrompt: SendMessage = {
      role: "system",
      content: agent?.systemInstruction || "",
    };

    const newSendMessages: SendMessage[] = [
      systemPrompt,
      ...(this.sendMessages.length == 0 ? [] : []),
      ...this.sendMessages,
      this.displayedMessageToSendMessage(newAssistantDisplayedMessage),
    ];

    const newDisplayedMessages = [
      ...this.displayedMessages,
      newAssistantDisplayedMessage,
    ];

    // MARK: User Send Message
    this.sendMessages = newSendMessages;
    this.displayedMessages = newDisplayedMessages;

    await this.repo.addChatMessage(newAssistantDisplayedMessage);

    this.onDidSendUserMessage();
    this.onMessagesUpdated();

    await this.__sendMessageAI(newSendMessages, newDisplayedMessages, plugins);
  }

  private async __sendMessageAI(
    newSendMessages: SendMessage[],
    newDisplayedMessages: DisplayedMessage[],
    plugins: Plugin[]
  ) {
    try {
      const last10messages = newSendMessages.slice(-10);

      this.setWriting(true);

      this.streamController = new AbortController();
      const { signal } = this.streamController;

      const stream = await this.aiHandler.completion(
        {
          messages: last10messages,
          user: this.user,
        },
        signal
      );

      const reader = stream.getReader();
      const decoder = new TextDecoder();
      let done = false;

      let lastMessage: string = "";
      this.setShowFollowButton(true);

      const newAssistantDisplayedMessage: DisplayedMessage = {
        messageId: uuidv4(),
        role: "assistant",
        content: lastMessage,
        timestamp: new Date(),
        isWriting: false,
        prompt: null,
        modelName: this.model.name,
        searchEngineQueries: [],
        plugins: [],
      };

      try {
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          if (signal.aborted) {
            break;
          }

          const chunkValue = value?.text;
          const toolCall = value?.toolCalls;
          if (chunkValue) {
            lastMessage = lastMessage + chunkValue;

            this.sendMessages = [
              ...newSendMessages,
              { role: "assistant", content: lastMessage },
            ];

            newAssistantDisplayedMessage.content = lastMessage;

            this.displayedMessages = [
              ...newDisplayedMessages,
              newAssistantDisplayedMessage,
            ];

            this.onMessagesUpdated();
          }
          if (toolCall) {
            newAssistantDisplayedMessage.info = {
              content: "Calling Tool",
              type: "info",
              meta: {
                tool_calls: Object.values(toolCall),
              },
            };

            this.displayedMessages = [
              ...newDisplayedMessages,
              newAssistantDisplayedMessage,
            ];

            this.onMessagesUpdated();

            this.worker.onmessage = async (event) => {
              const { id, result, error } = event.data;
              if (error) {
                newAssistantDisplayedMessage.info = {
                  type: "error",
                  content: error,
                  meta: {},
                };

                this.displayedMessages = [
                  ...newDisplayedMessages,
                  newAssistantDisplayedMessage,
                ];
                this.onMessagesUpdated();
                this.repo.updateChatMessage(newAssistantDisplayedMessage);
              } else {
                const newToolDisplayedMessage: DisplayedMessage = {
                  messageId: uuidv4(),
                  role: "tool",
                  content: "",
                  prompt: null,
                  timestamp: new Date(),
                  isWriting: false,
                  modelName: "",
                  searchEngineQueries: [],
                  plugins: [],
                  info: {
                    type: "info",
                    content: `Tool result: ${JSON.stringify(result)}`,
                    meta: {
                      tool_content: JSON.stringify(result),
                      tool_call_id: id,
                    },
                  },
                };
                this.displayedMessages = [
                  ...newDisplayedMessages,
                  newAssistantDisplayedMessage,
                  newToolDisplayedMessage,
                ];
                this.onMessagesUpdated();
                await this.repo.addChatMessage(newToolDisplayedMessage);
                await this.__sendMessageAI(
                  [
                    ...newSendMessages,
                    this.displayedMessageToSendMessage(
                      newAssistantDisplayedMessage
                    ),
                    this.displayedMessageToSendMessage(newToolDisplayedMessage),
                  ],
                  [...newDisplayedMessages, newToolDisplayedMessage],
                  plugins
                );
              }
            };

            const parsedPluginsDict = plugins.reduce((dict, plugin) => {
              const tool = JSON.parse(plugin.spec) as Tool;
              dict[tool.function.name] = plugin.source;
              return dict;
            }, {} as { [name: string]: string });

            for (const tool of Object.values(toolCall)) {
              const source = parsedPluginsDict[tool.function.name];
              if (source) {
                this.worker.postMessage({
                  id: tool.id,
                  source: source,
                  arguments: JSON.parse(tool.function.arguments),
                });
              }
            }
          }
          this.onAutoscroll();
          this.setLoading(false);
        }

        await this.repo.addChatMessage(newAssistantDisplayedMessage);
      } catch (readError) {
        // Handle stream reading errors, including AbortError
        if (readError instanceof Error && readError.name === "AbortError") {
          console.log("Stream reading was aborted");
        } else {
          console.error("Error reading from stream:", readError);
        }
      } finally {
        // Always clean up and update UI state
        reader.releaseLock();
        this.setWriting(false);
      }
    } catch (e) {
      console.error("Error in AI message processing:", e);
      this.setWriting(false);

      // Don't rethrow the error to prevent unhandled promise rejections
      // Instead, we could add error handling UI here if needed
    }
  }

  stop() {
    if (this.streamController) {
      try {
        this.streamController.abort();
        console.log("Stream controller aborted successfully");
      } catch (error) {
        console.error("Error aborting stream:", error);
      } finally {
        this.streamController = null;
        this.setWriting(false);
      }
    }
  }
}
