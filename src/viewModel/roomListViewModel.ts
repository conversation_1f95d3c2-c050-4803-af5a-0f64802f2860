import { Chat<PERSON>oom } from "@/model/chatroom";
import { ChatRoomRepo } from "@/repo/chatRoom";

export default class RoomListViewModel {
  onRoomsUpdated: () => void = () => {};

  constructor() {}

  async getRoomById(id: string): Promise<ChatRoom | null> {
    const room = await ChatRoomRepo.getInstance()
      .getChatRoomRepo()
      .getChatRoomById(id);
    return room;
  }

  async updateRoomName(id: string, roomName: string) {
    ChatRoomRepo.getInstance()
      .getChatRoomRepo()
      .updateChatRoom(id, { name: roomName });
    this.onRoomsUpdated();
  }

  async updateRoomTags(id: string, tags: string[]) {
    ChatRoomRepo.getInstance()
      .getChatRoomRepo()
      .updateChatRoom(id, { tags: tags });
    this.onRoomsUpdated();
  }

  async updateRoomModel(id: string, model: string) {
    ChatRoomRepo.getInstance().getChatRoomRepo().updateChatRoom(id, { model });
    this.onRoomsUpdated();
  }

  async updateRoomPlugins(id: string, plugins: string[]) {
    ChatRoomRepo.getInstance()
      .getChatRoomRepo()
      .updateChatRoom(id, { plugins });
    this.onRoomsUpdated();
  }

  async updateChatRoomFolder(
    roomId: string,
    newFolderId: string
  ): Promise<void> {
    ChatRoomRepo.getInstance()
      .getChatRoomRepo()
      .updateChatRoom(roomId, { folderId: newFolderId });
    this.onRoomsUpdated();
  }

  async deleteChatRoom(id: string) {
    ChatRoomRepo.getInstance().getChatRoomRepo().deleteChatRoom(id);
    this.onRoomsUpdated();
  }

  async createChatRoom(
    title: string,
    tags: string[],
    folderId: string
  ): Promise<ChatRoom> {
    return {
      id: "",
      folderId: folderId,
      name: title,
      tags: tags,
      createdAt: new Date(),
      messages: [],
      model: "",
      plugins: [],
    };
  }

  async addChatRoom(
    title: string,
    { folderId, tags }: { folderId?: string; tags?: string[] }
  ) {
    const chatRoomRepo = ChatRoomRepo.getInstance().getChatRoomRepo();
    const room = chatRoomRepo.saveChatRoom(title, { folderId, tags });
    this.onRoomsUpdated();
    return room;
  }
}
