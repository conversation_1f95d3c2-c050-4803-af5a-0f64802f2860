import Link from "next/link";
import { useRouter } from "next/router";

import "@/styles/globals.css";

export default function NotFound() {
  const { asPath, pathname } = useRouter();

  return (
    <div className="mx-auto mt-20 flex flex-col items-center space-y-8">
      <h1 className="text-4xl font-semibold">404 - Page Not Found</h1>
      <p className="text-gray-600">
        The realm of nothingness and emptiness awaits...
      </p>
      <p className="text-gray-600">
        Use the search box or explore the links below to continue your journey.
      </p>
      <input
        className="w-4/5 px-6 py-3 border rounded-full"
        type="search"
        placeholder="Search our universe..."
      />
      <div className="flex space-x-4">
        <Link href="/" passHref>
          <div className="text-blue-600 underline hover:text-red-500">
            Homepage
          </div>
        </Link>
        <Link href="/blog" passHref>
          <div className="text-blue-600 underline hover:text-red-500">Blog</div>
        </Link>
        <Link href="/contact" passHref>
          <div className="text-blue-600 underline hover:text-red-500">
            Contact Us
          </div>
        </Link>
      </div>
    </div>
  );
}
