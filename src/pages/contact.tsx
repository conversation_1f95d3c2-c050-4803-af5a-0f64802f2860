"use client";

import React from "react";
// import MarkdownContent from "@/components/MarkdownContent";
import type { GetStaticProps, InferGetStaticPropsType } from "next";

import "@/styles/globals.css";

type Content = {
  content: string;
};

export const getStaticProps = (async () => {
  const makdown = await require(`./contact.md`);
  const content = makdown.default;
  return { props: { content: { content } } };
}) satisfies GetStaticProps<{
  content: Content;
}>;

export default function ContactPage({
  content,
}: InferGetStaticPropsType<typeof getStaticProps>) {
  return (
    <div className="">
      <div className="flex flex-col items-center justify-center py-10">
        <a href="/" className="">
          back to home
        </a>
        <div className="w-1/2">
          {/* <MarkdownContent content={content.content}/> */}
        </div>
      </div>
    </div>
  );
}
