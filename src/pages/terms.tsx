"use client";

import React from "react";
// import MarkdownContent from "@/components/MarkdownContent";
import type { GetStaticProps, InferGetStaticPropsType } from "next";

import "@/styles/globals.css";
import Link from "next/link";

type Content = {
  content: string;
};

export const getStaticProps = (async () => {
  const makdown = await require(`./terms.md`);
  const content = makdown.default;
  return { props: { content: { content } } };
}) satisfies GetStaticProps<{
  content: Content;
}>;

export default function TermsPage({
  content,
}: InferGetStaticPropsType<typeof getStaticProps>) {
  return (
    <div className="w-full flex flex-col items-center ">
      <nav className="flex w-full justify-center py-5 my-20 sticky top-0 bg-white">
        <Link className="text-blue-500" href="/">
          Go back to TomioAI
        </Link>
      </nav>
      <main className="w-3xl max-w-3xl">
        {/* <MarkdownContent content={content.content} /> */}
      </main>
    </div>
  );
}
