import Link from "next/link";
import "@/styles/globals.css";
import { Button } from "@/components/Elements/Button";
import { useState } from "react";
const FAQPage = () => {
  interface FAQ {
    Q: string;
    A: string;
  }
  const generalFAQs: FAQ[] = [
    {
      Q: "Is this app free?",
      A: "TomioAI.com is free to use with some basic features. You will need to have a working OpenAI API Key in order to use the app. When you use the OpenAI API Key, you pay directly to OpenAI for the amount of credits/tokens you use. TomioAI.com has premium features that can be unlocked with a one-time purchase.",
    },
    {
      Q: "Is the License Key one-time purchase? Do I need to renew it?",
      A: "Yes, license key is one-time purchase and valid forever. It is not a subscription and you don't need to renew it. You will also be provided with an offline static web app (self-host) after you purchase so you can self-host and use the app anytime anywhere you want. Enjoy!",
    },
    {
      Q: "Where can I get an OpenAI API Key?",
      A: "You can signup directly with OpenAI at https://platform.openai.com/signup",
    },
    {
      Q: "Do I need to pay for ChatGPT Plus ($20/month) to use TomioAI.com?",
      A: "No! A ChatGPT Plus subscription is not needed. You just need to have an OpenAI's API Key. You can see more info on how to get one here: https://openai.com/blog/openai-api",
    },
    {
      Q: "Do I need to pay for OpenAI for a ChatGPT API Key?",
      A: "Yes. You need to have an OpenAI account and a valid API key to use ChatGPT. We don't sell API keys.",
    },
    {
      Q: "How does this app work?",
      A: "This is a static web app, it doesn't have any backend server. When you enter your API key, it will be stored locally and securely on your browser. All API requests are sent directly from your browser to OpenAI server to interact with ChatGPT. Think of this as a HTTP client for your ChatGPT API with a lot of convenience features.",
    },
    {
      Q: "How is the API key handled?",
      A: "Your API is safe and stored locally on your device. This is a static app, which means that it doesn't have a backend. All the data is stored in your browser's local storage. Requests to Open AI's API is sent directly from your current browser (check the Network tab in your console if you want to see it).",
    },
    {
      Q: "Is it ok to give my OpenAI API Key to TomioAI? Does OpenAI allow this use case?",
      A: "Yes. TomioAI only stores your API Key locally and never sends your API Key anywhere. OpenAI allows use cases where the API key is stored locally in the user's device. You can see this official response from OpenAI's staff here: https://community.openai.com/t/openais-bring-your-own-key-policy/14538/4",
    },
    {
      Q: "Is my API Key encrypted in local storage?",
      A: `TomioAI offers additional encryption for your API Key. You can enable encryption with a password by clicking the "OpenAI API Key" button in the sidebar and selecting "Encrypt API key...". Your API Key will be encrypted using the AES algorithm provided by the open-source CryptoJS library (https://github.com/brix/crypto-js). TomioAI only provides an encryption feature for your API key. Chat messages, prompts, AI characters, and other elements are stored using the standard local storage of your browser, which may or may not include encryption, depending on your browser. The encryption process are done entirely locally on your device, there is no backend server. If you use TomioAI on multiple devices, you will need to encrypt your API key on all of them, you can also set different passwords on different devices.`,
    },
    {
      Q: "What's the difference between ChatGPT Plus and ChatGPT API via TomioAI?",
      A: "Under the hood, ChatGPT Plus and ChatGPT API offer the same model and the same quality. You can view this on their official announcement here: https://platform.openai.com/docs/guides/chat. The initial system instruction can be a little bit different, which can be configured to make the AI output different messages from time to time.",
    },
    {
      Q: "Can I use GPT-4 in TomioAI?",
      A: "TomioAI does not give you access to the GPT-4 model automatically. If you have access to the GPT-4 model via API, you can use it on TomioAI. Note that GPT-4 API access is not the same as the ChatGPT Plus subscription. To get access to the GPT-4 API, you can sign-up for the GPT-4 API access here: https://openai.com/waitlist/gpt-4-api.",
    },
    {
      Q: "I have ChatGPT Plus, will it cost me more to use ChatGPT API via TomioAI?",
      A: "The ChatGPT API costs soooooo little you won't even notice it. Typical chat conversation is about 1000 characters, which costs ~$0.001. That's less than a cent. You can have thousands of chats and it will only costs you like $1. See official pricing here: https://openai.com/pricing#language-models",
    },
    {
      Q: "How many chats can I have?",
      A: "You can have as many chats as you want. The only limit is your OpenAI API key's limit and your browser storage limit (technical term: Local Storage). Web browser gives you some limited data storage, the actual limit is different for each browser. Typically, you can save thousands of chat conversations without problems, but that's not guaranteed. When the browser storage runs out, you will need to delete old chats to save the new ones. We will provide a separate service to sync and backup your chats soon. Adding the website to your home screen may increase the storage capacity.",
    },
    {
      Q: "How can I sync my chat conversations across devices?",
      A: "You can export the chats and import it on another device. You can also enable TomioAI Cloud for seamless syncing across devices. TomioAI Cloud is a free service provided to all TomioAI users. You can enable it by clicking the cloud icon at the bottom of the sidebar.",
    },
    {
      Q: "Why does sometimes TomioAI responds differently than the default OpenAI's ChatGPT event with the same prompt?",
      A: `TomioAI uses the same underlying model as OpenAI's default chat app. For the same prompt, it can produce different responses each time. The default chat app by OpenAI may also have some initial prompt settings that are different from TomioAI. If you don't get the response you expects, try "Re-generate Response" or tweaking the prompt.`,
    },
    {
      Q: "Can I self-host it?",
      A: "Yes. After you make a purchase, you will receive a link to download the static web app and the instruction how to deploy it on your own server. Note that you will only receive the compiled code of the app, the full source code is not available because the app is not open-source. You can deploy the app anywhere without having to update any code or settings.",
    },
    {
      Q: "Do I have access to the full source code if I want to self-host?",
      A: "No. The license key only grants you the permission to use and deploy the app on your own server. You do not have permissions to modify or redistribute the code. The full source code is not available for sale as the app is not open-source. You will only receive the compiled code of the app, you can deploy the app anywhere without having to update any code or settings.",
    },
    {
      Q: "What can I customize in the self-host version?",
      A: "As of now, you cannot customize anything. But I'm working on making it possible to set custom UI and branding soon. Stay tuned!",
    },
    {
      Q: "License Key vs. API Key",
      A: "License Key is used to activate the premium features of TomioAI.com. API Key refers to OpenAI ChatGPT API Key, and is used to connect to OpenAI's API to interact with ChatGPT. You need an API Key to use the app. The License Key is only needed if you want to use the premium features.",
    },
  ];
  const webSearchFAQs: FAQ[] = [
    {
      Q: "How Web Search works?",
      A: "When you enable Web Search, TomioAI will use the function call feature provided by OpenAI models how to perform a search query using the selected search engine (which is Google by default). The model will then attempt to run a search command only when necessary and use the search result to answer your question.",
    },
    {
      Q: "How does TomioAI execute the search?",
      A: "TomioAI will use the search engine's API to execute the search query. The search requests are sent directly from your browser to the search API without any intermediate servers. Your privacy is 100% protected. No one can see your search query and search result except you and the search engine itself.",
    },
    {
      Q: "What about my privacy? Who can see my search query?",
      A: "The search requests are sent directly from your browser to the search API without any intermediate servers. Your privacy is 100% protected. No one can see your search query and search result except you and the search engine itself.",
    },
    {
      Q: "What search engines are supported?",
      A: "TomioAI currently only supports Google search engine. We will add more search engines in the future. ",
    },
    {
      Q: "Where can I get the search engine API Key?",
      A: "The official website is https://developers.google.com/custom-search/v1/overview. Also, you can follow the instructions on this page https://tdinh.notion.site/How-to-get-Search-Engine-ID-and-API-Key-on-Programmable-Search-Engine-by-Google-b861a749b20f4fcdbc1449f92ad9ed9a",
    },
    {
      Q: "What are the limits? How many searches can I do?",
      A: "The default free plan of Programmable Search Engine includes 100 searches per day for free. If you need more, you may sign up for billing in the Google API Console. https://cloud.google.com/billing/docs/how-to/manage-billing-account",
    },
    {
      Q: "Does Web Search require GPT-4? Can I use it with GPT-3.5?",
      A: "Web Search is best used with GPT-4 because it has a larger context length and can store more search results and can pull out information from search result more reliably. However, you can still Web Search with GPT-3.5 without problems (most of the time).",
    },
    {
      Q: "How much more tokens are used if I enable Web Search? ",
      A: "The base instructions for Web Search contains ~600 tokens. The search result will be added to the context and will be used to answer your question. The more search results you get, the more tokens will be used. The average number of tokens used for Web Search is ~800 tokens. ",
    },
  ];

  const [expandedQuestionsGeneral, setExpandedQuestionsGeneral] = useState<
    number | null
  >(null);
  const [expandedQuestionsWeb, setExpandedQuestionsWeb] = useState<
    number | null
  >(null);

  const toggleExpandedQuestionGeneral = (index: number) => {
    if (isQuestionExpandedGeneral(index)) {
      setExpandedQuestionsGeneral(null);
    } else {
      setExpandedQuestionsGeneral(index);
      setExpandedQuestionsWeb(null);
    }
  };

  const isQuestionExpandedGeneral = (index: number) => {
    return expandedQuestionsGeneral === index;
  };

  // Lakukan hal serupa untuk bagian "Web Search FAQs"
  const toggleExpandedQuestionWeb = (index: number) => {
    if (isQuestionExpandedWeb(index)) {
      setExpandedQuestionsWeb(null);
    } else {
      setExpandedQuestionsWeb(index);
      setExpandedQuestionsGeneral(null);
    }
  };

  const isQuestionExpandedWeb = (index: number) => {
    return expandedQuestionsWeb === index;
  };

  return (
    <div className="w-full flex flex-col items-center ">
      <nav className="flex w-full justify-center py-5 my-20 sticky top-0 bg-white">
        <Link className="text-blue-500" href="/">
          Go back to TomioAI
        </Link>
      </nav>
      <main className="w-3xl max-w-3xl">
        <h1 className="w-full text-center font-bold text-6xl mb-10">FAQs</h1>
        <p className="">
          We trained a GPT-4 model to answer your questions based on our{" "}
          <Link className="text-blue-500" href="/faqs">
            Faqs
          </Link>
          ,{" "}
          <Link className="text-blue-500" href="/terms">
            Term of Service
          </Link>
          , and{" "}
          <Link className="text-blue-500" href="/privacy">
            Privacy Policy
          </Link>
          .
        </p>
        <div className="input flex w-full pt-3 pb-10 border-b gap-2 my-4">
          <input
            type="text"
            className="w-full border rounded-md pl-2 "
            placeholder="Ask a question..."
          />
          <Button>Ask</Button>
        </div>
        <div className="pt-16 w-full ">
          <h2 className="w-full text-center font-bold text-4xl my-6">
            General FAQs
          </h2>
          <h3 className="py-3 text-3xl font-bold">
            Frequently asked questions
          </h3>
          {generalFAQs.map((faq, index) => (
            <div
              className="py-5 border-t cursor-pointer"
              key={index}
              onClick={() => toggleExpandedQuestionGeneral(index)}
            >
              <span className="flex w-full justify-between">
                <h4
                  className={`font-semibold text-sm ${
                    !isQuestionExpandedGeneral(index) ? "text-gray-500" : ""
                  }`}
                >
                  {faq.Q}
                </h4>
                <button>{isQuestionExpandedGeneral(index) ? "-" : "+"}</button>
              </span>
              <h4
                className={`mt-3 text-sm transition-all text-gray-500 duration-500 ease-in-out ${
                  isQuestionExpandedGeneral(index)
                    ? "h-auto"
                    : "max-h-0 opacity-0 overflow-hidden"
                }`}
              >
                {faq.A}
              </h4>
            </div>
          ))}
        </div>
        <div className="pt-5 w-full ">
          <h2 className="w-full text-center font-bold text-4xl my-6 mb-10">
            Web Search FAQs
          </h2>
          {webSearchFAQs.map((faq, index) => (
            <div
              className="py-5 border-t cursor-pointer"
              key={index}
              onClick={() => toggleExpandedQuestionWeb(index)}
            >
              <span className="flex w-full justify-between">
                <h4
                  className={`font-semibold text-sm ${
                    !isQuestionExpandedWeb(index) ? "text-gray-500" : ""
                  }`}
                >
                  {faq.Q}
                </h4>
                <button>{isQuestionExpandedWeb(index) ? "-" : "+"}</button>
              </span>
              <h4
                className={`mt-3 text-sm transition-all text-gray-500 duration-500 ease-in-out ${
                  isQuestionExpandedWeb(index)
                    ? "h-auto"
                    : "max-h-0 opacity-0 overflow-hidden"
                }`}
              >
                {faq.A}
              </h4>
            </div>
          ))}
        </div>
      </main>
    </div>
  );
};

export default FAQPage;
