# Privacy Policy

## For TomioAI.com Static Chat App

How we handle your data for free users:

- All of your data include API keys, chat history, chat messages are stored locally on your browser (using Local Storage).
- The entire app is a static website, we don't have a backend server to collect data to the server side.
- When you send a message on tomioai.com, the website sends a request to OpenAI API endpoints using the API key you provided.
- Requests are sent directly from your browser to OpenAI's server using secured transport protocol HTTPS. There is no middle server and no one else can see the requests, responses and their content.
- For Anthropic models, requests are proxied through TomioAI proxy server to Anthropic's server. This is because Anthropic doesn't support direct request from your browser. We don't log and store any data from the requests. The proxy server purely forwards your request to <PERSON><PERSON><PERSON>'s server and does nothing else. For maximum privacy, you can setup your own proxy server for Anthropic models by going to the Setting panel (click the gear icon) → Advanced Settings → Anthropic Chat Completions Endpoint.
- There is strictly no external JavaScript run on TomioAI.com.

How we handle your data for paid users:  
Privacy policy for paid users are the same as free users, with the following additional points:

- When you purchase a license key, we store your email address and the license key in our database. We use this information to send you license key and to verify your license key when you use it on TomioAI.com.
- When you enter the license key to unlock the premium features, TomioAI.com will send a request from your browser to our License Server to verify the license key and get the information about the license key's owner to display on the app.
- When a new version of TomioAI.com is available, we will send you an email to notify you about new features and updates. You can unsubscribe from the email list at any time.

Third-party services we use on tomioai.com:

- We use **Vercel.com** to host our website.
- We use **Vercel Analytics by Vercel.com** to collect anonymous analytics data (like how many visitors a day, etc.). This data is only collected from TomioAI.com and not from the self-hosted version.
- We use **Sentry by Sentry.io** to collect anonymous crash log and error reports from the application. This information helps us to improve the app and fix bugs.
- We use **lemonsqueezy.com** to process payments for our premium features. We don't store any of your payment data.

## For TomioAI Cloud:

- TomioAI Cloud is an opt-in only service we provide to help you conveniently share your chat conversation with friends via a link that is publicly accessible from the internet. Other features include sync chat history between devices and backup chat history.
- TomioAI Cloud is integrated to TomioAI.com and will only work when you explicitly use the Share/Sync/Backup feature, and pick TomioAI Could as the Share/Sync/Backup option. You will be asked to opt-in to TomioAI Cloud.
- Note that you can use other services to share and sync your chat. TomioAI Cloud is just one of the options and is not mandatory.
- When you **Share** your chats using TomioAI Cloud, we will store your the chat conversation you shared on our server. And it will become publicly available on the internet with a secret link, possibly accessible by search engines.
- We only store the chats that you **shared**, we don't store any other data on TomioAI Cloud's server (no API Key, no License Key, no other chats).
- If you enable TomioAI Cloud for Sync and Backup, we will store all of your chats, prompts, messages, AI characters, bookmarks to the TomioAI Cloud server. All communication with TomioAI Cloud server are encrypted using HTTPS, all of your data is stored securely on our cloud database provided by planetscale.com and is encrypted at rest.
- If you enable TomioAI Cloud for Sync and Backup, we will use Cookie to store your logged in state in order to provide you a seamless sync experience without having to login again. Our cookie will expires after 30 days without any sync activities.

## For TomioAI via Setapp

_Note: this folder only apply to you if you use the TomioAI via Setapp at https://setapp.com/apps/tomioai._

- The version of TomioAI you download from Setapp is the exact same version as the one you use from TomioAI.com.
- The Setapp version comes with a free access to the GPT-3.5 model, which is included in your Setapp subscription. This model is shown in the app under the name: **"Setapp (GPT-3.5)"**
- When you use the **Setapp (GPT-3.5)** model, you don't need to provide any API key. The app will automatically use the API key provided by Setapp so that you can use the model for free. This means all of your requests to OpenAI when using this model are proxied to TomioAI's proxy server and Setapp's server. The proxy server's sole purpose is to hide away the secret API key and apply rate limits to abuse and for other security purposes. We don't log or store any of your chat data on our server. Please refer to Setapp's privacy policy for more information on how Setapp manage your chat data.
- When you use the other models that are NOT **Setapp (GPT-3.5)**, your requests will be sent directly from your local device to OpenAI/Anthropic without any proxy or intermediate server.

## For TomioAI Custom Deployment:

_Note: this folder only apply to you if you use the TomioAI Custom Deployment at https://tomioai.com/custom._

- Glossary: "The product" means the TomioAI Custom Deployment product; "Chat Instance" means the AI Chat interface you created when using the product. "You/the admin" the people who create a new chat instance and have the permission to config and customize the chat instance; "Your users" means the people who uses the Chat instance, sending messages and use the chat features on your chat instance, this could be your teammate, your users, your community members, etc.
- When you create a new chat instance on https://tomioai.com/custom, we will collect a few information about you, including your email address, your name, and the name of your chat instance. We use this information to send you an email with the link to your chat instance.
- Everything we collect is stored securely on our server, we never share your information with anyone else or any 3rd party that we are using. When you use the product, if we ever need to share your information with 3rd party, for example, to link your billing info with your chat instance, we will ask for your explicit permission first (by checking a "I understand" checkbox).
- We use lemonsqueezy.com to process payments for our premium features. We don't store any of your payment data.
- We use Vercel.com to host your chat instance.
- The chat instance you created will be used by your users/team/community. When your users login to the chat instance, we will collect their email address to verify their identity. We use this information to send them an email with the login link. We will also collect their name, avatar, and other information only if they provide to us. We use this information to display their name and avatar on the chat instance.
- When you setup your chat instance, we will collect your OpenAI API Key. We use your API Key to connect to OpenAI API to generate the AI chat responses when your users use your AI chat instance. We will never share your OpenAI API Key with anyone else or any 3rd party services we use. Your users will not see your OpenAI API key. We do not use your OpenAI Key for any other purposes.
- When your users login to your chat instance, we will send them an email with the login code. The email will be sent from "<EMAIL>", with your name and email address will be shown to the users as the "Reply-to" address. This is to help the users recogine you and contact support if needed. Noted that only people who are explicit added by you in the Admin Panel can login.
- When your users chat via your chat instance, their chat history and messages are passed through and stored on our server. We keep the chat history and messages for 30 days. You can turn off the chat history and messages logging or extend the store duration in the Admin Panel. Note that even if the chat history and messages logging are turned off, the messages are still passing through our server and will be kept in the server's memory in a short amount of time (typically for the duration of the request or the streaming duration). This is necessary to pass the messages through our server, so we can avoid exposing your OpenAI API key to the end users. We use Vercel.com to host our server where the messages are passed through.
- Your users' chat history data, custom prompts, libraries, bookmarks are stored on their local device when they use your chat instance. Your users can choose to setup backup & sync using various services we offer in the chat interface, including TomioAI Cloud (Backup/Sync/Share).
- Your users cannot see each other data and chat history.

---

Your privacy is important to us. It is tomioai.com's policy to respect your privacy regarding any information we may collect from you across our website, https://tomioai.com, and other sites we own and operate.

We only ask for personal information when we truly need it to provide a service to you. We collect it by fair and lawful means, with your knowledge and consent. We also let you know why we're collecting it and how it will be used.

We only retain collected information for as long as necessary to provide you with your requested service on tomioai.com. What data we store, we'll protect within commercially acceptable means to prevent loss and theft, as well as unauthorised access, disclosure, copying, use or modification.

We don't share any personally identifying information publicly or with third-parties, except when required to by law.

Our website may link to external sites that are not operated by us. Please be aware that we have no control over the content and practices of these sites, and cannot accept responsibility or liability for their respective privacy policies.

You are free to refuse our request for your personal information, with the understanding that we may be unable to provide you with some of your desired services.

Your continued use of our website will be regarded as acceptance of our practices around privacy and personal information. If you have any questions about how we handle user data and personal information, feel free to contact us.

This policy is effective as of 30 June 2023.
