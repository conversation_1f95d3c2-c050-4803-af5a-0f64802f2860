import { repo } from "@/repo/waitlist";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { email, feature } = req.body;

  const validEmailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!validEmailRegex.test(email)) {
    return res.status(400).json({ error: "Invalid email" });
  }

  const existingRefereal = await repo.getReferralCode(email);

  const referralCode = existingRefereal || generateReferralCode(7);

  await repo.saveWaitlist(email, feature, referralCode);

  res.status(200).json({
    message: "Email added to waitlist",
    referal: referralCode,
  });
}

function generateReferralCode(length: number): string {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let referralCode = "";

  for (let i = 0; i < length; i++) {
    const randomNumber = Math.floor(Math.random() * characters.length);
    referralCode += characters.charAt(randomNumber);
  }

  return referralCode;
}
