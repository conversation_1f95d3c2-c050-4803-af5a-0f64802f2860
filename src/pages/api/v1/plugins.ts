import MongoExtensionRepository from "@/repo/backend/plugin/mongo";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const repository = new MongoExtensionRepository();

  try {
    const { name, description, filterType, sortBy, sortOrder } = req.query;

    const pluginItems = await repository.getExtensionItems({
      name: name as string,
      description: description as string,
      filterType: filterType as string,
      sortBy: sortBy as string,
      sortOrder: sortOrder as "asc" | "desc",
    });

    res.status(200).json(pluginItems);
  } catch (error) {
    console.error("Error retrieving pluginItems from the database:", error);
    res.status(500).json({ error: "Failed to retrieve pluginItems" });
  }
}
