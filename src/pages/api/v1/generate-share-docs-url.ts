import MongoShareDocumentsRepository from "@/repo/backend/shareDocuments/mongo";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  const repository = new MongoShareDocumentsRepository();

  try {
    const blocks = req.body?.blocks;
    const markdown = req.body?.markdown;
    const html = req.body?.html;

    if (!Array.isArray(blocks)) {
      return res
        .status(400)
        .json({ error: "Missing or invalid 'blocks' array" });
    }

    const id = await repository.saveSharedDocuments({
      blocks,
      markdown,
      html,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    });

    res.status(201).json({ id: id });
  } catch (err) {
    console.error("Error sharing chat:", err);
    res.status(500).json({ error: "Failed to store chat history" });
  }
}
