import MongoModelRepository from "@/repo/backend/model/mongo";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const repository = new MongoModelRepository();

  try {
    const models = await repository.getModels();
    res.status(200).json(models);
  } catch (error) {
    console.error("Error retrieving models from the database:", error);
    res.status(500).json({ error: "Failed to retrieve models" });
  }
}
