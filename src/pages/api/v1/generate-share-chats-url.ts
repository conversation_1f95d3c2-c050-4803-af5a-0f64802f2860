import MongoShareChatsRepository from "@/repo/backend/shareChats/mongo";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  const repository = new MongoShareChatsRepository();

  try {
    const messages = req.body?.messages;

    if (!Array.isArray(messages)) {
      return res
        .status(400)
        .json({ error: "Missing or invalid 'messages' array" });
    }

    const id = await repository.saveSharedMessages({
      messages,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    });

    res.status(201).json({ id: id });
  } catch (err) {
    console.error("Error sharing chat:", err);
    res.status(500).json({ error: "Failed to store chat history" });
  }
}
