import { generateAppearanceCss } from "@/lib/generate-css";
import { uploadAppearanceCss } from "@/lib/upload-object-css";
import { NextApiRequest, NextApiResponse } from "next";
import { v4 as uuidv4 } from "uuid";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") return res.status(405).end("Method Not Allowed");

  const newAppearaceId = uuidv4();

  const {
    appearance,
    userId = null,
    orgId = null,
    appearanceId: oldAppearanceID,
  } = req.body;

  console.log("OLD appearanceID", oldAppearanceID);

  if (!appearance)
    return res.status(400).json({ error: "Missing appearance settings" });

  // Generate CSS from appearance JSON
  const css = generateAppearanceCss(appearance);

  // Upload to your CDN (switchable)
  const cssUrl = await uploadAppearanceCss(newAppearaceId, css);

  // TODO: save metadata in DB (userId, orgId, appearanceId, cssUrl)

  // Optionally set cookie if not set
  if (!req.cookies.appearance_id) {
    res.setHeader(
      "Set-Cookie",
      `appearance_id=${newAppearaceId}; Path=/; Max-Age=31536000`
    );
  }

  return res.status(200).json({ newAppearaceId, cssUrl });
}
