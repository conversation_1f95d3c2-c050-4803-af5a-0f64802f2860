import { uploadBugReport } from "@/lib/upload-object-bug-report";
import { IncomingForm } from "formidable";
import { NextApiRequest, NextApiResponse } from "next";
import fs from "fs/promises";

// Disable Next.js's default body parser for file upload
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") return res.status(405).end("Method Not Allowed");

  const form = new IncomingForm({ keepExtensions: true });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error("Form parsing error:", err);
      return res.status(500).json({ error: "Failed to parse form" });
    }

    const markdown = Array.isArray(fields.markdown)
      ? fields.markdown[0]
      : fields.markdown;
    const clientInfoRaw = Array.isArray(fields.clientInfo)
      ? fields.clientInfo[0]
      : fields.clientInfo;
    const clientInfo = clientInfoRaw ? JSON.parse(clientInfoRaw) : {};

    const screenshot = files.screenshot?.[0] ?? files.screenshot;
    const screenshotFile = Array.isArray(screenshot)
      ? screenshot[0]
      : screenshot;

    if (!markdown || !screenshotFile?.filepath) {
      return res.status(400).json({
        error: "Missing required fields: markdown or screenshot",
      });
    }

    try {
      // Read the screenshot as a buffer
      const screenshotBuffer = await fs.readFile(screenshotFile.filepath);

      const result = await uploadBugReport({
        markdown,
        clientInfo,
        screenshot: {
          buffer: screenshotBuffer,
          originalFilename: screenshotFile.originalFilename || "screenshot.png",
          mimetype: screenshotFile.mimetype || "image/png",
        },
      });

      res.status(200).json({
        status: "ok",
        bugId: result.bugId,
        urls: result.urls,
      });
    } catch (error) {
      console.error("Upload failed:", error);
      res.status(500).json({ error: "Failed to upload bug report" });
    }
  });
}
