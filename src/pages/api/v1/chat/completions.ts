import { Role, SendMessage } from "@/model/sendMessage";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { AIModelStream, AIModelStreamPayload } from "@/utils/AIModelStream";
import { ModelStreamResponse } from "@/utils/ModelStreamResponse";
import {
  createParser,
  ParsedEvent,
  ReconnectInterval,
} from "eventsource-parser";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

if (!process.env.OPENAI_API_KEY) {
  throw new Error("Missing Environment Variable OPENAI_API_KEY");
}

// https://stackoverflow.com/a/78251823
export const config = {
  runtime: "edge",
  // runtime: "nodejs"
};

export default async function handler(request: NextRequest) {
  let body: AIModelStreamPayload;
  try {
    // body = requestBodySchema.parse(await req.json());
    body = await request.json();
  } catch (e) {
    console.log(e);
    return new Response("Invalid request body", { status: 400 });
  }

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${process.env.OPENAI_API_KEY ?? ""}`,
  };

  if (process.env.OPENAI_API_ORG) {
    headers["OpenAI-Organization"] = process.env.OPENAI_API_ORG;
  }

  const originBody = body as unknown as AIModelStreamPayload;
  const payload: AIModelStreamPayload = {
    ...originBody,
    model: "gpt-4o",
  };

  const res = await fetch("https://api.openai.com/v1/chat/completions", {
    headers,
    method: "POST",
    body: JSON.stringify(payload),
  });

  const encoder = new TextEncoder();
  const decoder = new TextDecoder();

  const newid = `tomiocmpl-${uuidv4()}`;
  const stream = new ReadableStream<Uint8Array>({
    async start(controller) {
      function onParse(event: ParsedEvent | ReconnectInterval) {
        if (event.type === "event") {
          const data = event.data;

          if (data === "[DONE]") {
            controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
            controller.close();
            return;
          }
          try {
            const oldEventData: ModelStreamResponse = JSON.parse(data);
            const newEventData: ModelStreamResponse = {
              ...oldEventData,
              id: newid,
              model: body.model,
            };
            const text = JSON.stringify(newEventData) + "\n\n";
            controller.enqueue(encoder.encode(`data: ${text}`));
          } catch (e) {
            controller.error(e);
          }
        }
      }

      const parser = createParser(onParse);
      for await (const chunk of res.body as any) {
        parser.feed(decoder.decode(chunk));
      }
    },
  });

  return new NextResponse(stream, {
    headers: { "Content-Type": "text/event-stream" },
  });
}
