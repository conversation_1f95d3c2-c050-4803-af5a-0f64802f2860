import MongoAIAgentRepository from "@/repo/backend/agent/mongo";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const repository = new MongoAIAgentRepository();

  try {
    const agents = await repository.getAIAgents();
    res.status(200).json(agents);
  } catch (error) {
    console.error("Error retrieving agents from the database:", error);
    res.status(500).json({ error: "Failed to retrieve agents" });
  }
}
