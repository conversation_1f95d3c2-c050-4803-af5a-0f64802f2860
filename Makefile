# Usage:
# make migrate_init ENV=local
# make migrate_init ENV=dev
# make migrate_init ENV=prod

ENV ?= local
ENV_FILE = .env.$(ENV)

update_env:
	@echo "Using environment: $(ENV)"
	@if [ -f $(ENV_FILE) ]; then \
		cp $(ENV_FILE) .env; \
		echo "Updated .env from $(ENV_FILE)"; \
	else \
		echo "Error: $(ENV_FILE) does not exist"; \
		exit 1; \
	fi

migrate_init: update_env
	bunx prisma migrate dev --name init

# make migrate ENV=local NAME=add_user_engagement_fields
migrate: update_env
	@if [ "$(NAME)" = "migration" ]; then \
		echo "❌ Please provide a migration NAME (e.g. make migrate NAME=add_field)"; \
		exit 1; \
	fi
	@echo "🚀 Running Prisma migration: $(NAME)"
	bunx prisma migrate dev --name $(NAME)

migrate_create: update_env
	@if [ -z "$(NAME)" ]; then \
		echo "❌ Please provide a migration NAME (e.g. make migrate_create NAME=add_field)"; \
		exit 1; \
	fi
	@echo "📝 Creating migration files only: $(NAME)"
	bunx prisma migrate dev --create-only --name $(NAME)