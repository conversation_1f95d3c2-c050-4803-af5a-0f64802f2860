# MongoDB-based project - Prisma migrations removed
# Environment management utilities

ENV ?= local
ENV_FILE = .env.$(ENV)

update_env:
	@echo "Using environment: $(ENV)"
	@if [ -f $(ENV_FILE) ]; then \
		cp $(ENV_FILE) .env; \
		echo "Updated .env from $(ENV_FILE)"; \
	else \
		echo "Error: $(ENV_FILE) does not exist"; \
		exit 1; \
	fi

# MongoDB doesn't require migrations like SQL databases
# Database schema is handled dynamically by the application