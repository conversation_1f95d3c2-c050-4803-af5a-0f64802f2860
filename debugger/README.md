# Debug GPT Event Stream Data

### Debug file

Run inside this directory

```python
python3 event_stream_reader.py --joined
```

### Create data.txt file before run

data.txt sample content

```
data: {"id":"chatcmpl-7n2Oe7PNIMnxmiCsDOENst9mqZInB","object":"chat.completion.chunk","created":1691921640,"model":"gpt-3.5-turbo-0613","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}

data: {"id":"chatcmpl-7n2Oe7PNIMnxmiCsDOENst9mqZInB","object":"chat.completion.chunk","created":1691921640,"model":"gpt-3.5-turbo-0613","choices":[{"index":0,"delta":{"content":"H"},"finish_reason":null}]}

data: {"id":"chatcmpl-7n2Oe7PNIMnxmiCsDOENst9mqZInB","object":"chat.completion.chunk","created":1691921640,"model":"gpt-3.5-turbo-0613","choices":[{"index":0,"delta":{"content":"1"},"finish_reason":null}]}
```
