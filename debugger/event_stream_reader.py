import argparse
import json
import os

# Parse command-line arguments
parser = argparse.ArgumentParser(description='Process event stream data.')
parser.add_argument('--file', default='data.txt', help='Path to the data file')
parser.add_argument('--joined', action='store_true', help='Merge content values before printing')
args = parser.parse_args()

data_file = args.file
content_values = []

# Check if the data file exists, and create it if not
if not os.path.exists(data_file):
    open(data_file, 'w').close()

# Read data from the specified file
with open(data_file, 'r') as file:
    for line in file:
        line = line.replace("data:", "").strip()  # Remove "data:" and trim whitespace
        
        try:
            data_entry = json.loads(line)
            choices = data_entry.get("choices", [])
            if choices and isinstance(choices, list) and "delta" in choices[0]:
                content = choices[0]["delta"].get("content", "")
                content_values.append(content)
        except json.JSONDecodeError:
            continue  # Skip lines that are not valid JSON

# Choose the print format based on the --joined option
if args.joined:
    merged_content = " ".join(content_values)
    print(merged_content)
else:
    for content in content_values:
        print(content)

# python script.py --file custom_data.txt --joined
