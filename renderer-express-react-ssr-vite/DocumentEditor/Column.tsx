import { defaultProps, insertOrUpdateBlock } from "@blocknote/core";
import {
  createReactBlockSpec,
  ReactCustomBlockRenderProps,
} from "@blocknote/react";
import { LayoutTemplate } from "lucide-react";
import { useState } from "react";
import { MyBlockNoteEditor } from "./schema";
import { twMerge } from "tailwind-merge";

export const ColumnLayout = createReactBlockSpec(
  {
    type: "columnLayout",
    propsSchemaValidator: {
      columns: {
        validate: (val: number) => val >= 2 && val <= 4,
      },
    },
    propSchema: {
      columns: {
        default: 2,
      },
      columnWidths: {
        default: "",
      },
    },
    content: "inline",
  },
  {
    render: (props) => {
      const { columns, columnWidths } = props.block.props;
      const [isEditing, setIsEditing] = useState(false);
      const [tempColumns, setTempColumns] = useState(columns);
      const [tempWidths, setTempWidths] = useState<string[]>(
        Array(tempColumns)
          .fill("")
          .map((_, i) => {
            if (
              columnWidths.replaceAll("[", "").replaceAll("]", "").split(",") &&
              columnWidths[i] !== undefined
            ) {
              return columnWidths[i].toString();
            }
            return "";
          })
      );

      const handleSave = () => {
        const parsedWidths = tempWidths.map(
          (w) => parseFloat(w) || 100 / tempColumns
        );
        props.editor.updateBlock(props.block, {
          type: "columnLayout",
          props: {
            columns: tempColumns,
            columnWidths: `[${parsedWidths
              .map((e) => e.toString() + "%")
              .join(",")}]`,
          },
        });
        setIsEditing(false);
      };

      const isSSR = typeof window === "undefined";

      return (
        <div
          contentEditable={false}
          className={twMerge(
            "relative column-layout-block p-2 border rounded bg-red-500",
            isSSR && "bg-blue-400"
          )}
        >
          <div className="flex justify-between items-center mb-2">
            <strong>Column Layout {typeof window}</strong>
            <button
              onClick={() => setIsEditing((v) => !v)}
              className="text-xs text-blue-500 hover:underline"
            >
              Configure
            </button>
          </div>

          <div className="flex w-full gap-2">
            {Array.from({ length: columns }).map((_, i) => {
              const width =
                Array.isArray(columnWidths) && columnWidths[i] !== undefined
                  ? `${columnWidths[i]}%`
                  : `${100 / columns}%`;

              return (
                <div
                  key={i}
                  // style={{ width }}
                  // className="border border-dashed p-2 min-h-[50px] bg-white"
                >
                  Column {i + 1}
                </div>
              );
            })}
          </div>

          {isEditing && (
            <div className="absolute top-full left-0 bg-white shadow border p-4 mt-2 z-50 w-72">
              <div className="mb-2">
                <label className="text-xs font-medium">Number of Columns</label>
                <select
                  className="w-full text-sm border rounded px-2 py-1"
                  value={tempColumns}
                  onChange={(e) => {
                    const val = parseInt(e.target.value);
                    setTempColumns(val);
                    setTempWidths(Array(val).fill(""));
                  }}
                >
                  {[2, 3, 4].map((n) => (
                    <option key={n} value={n}>
                      {n}
                    </option>
                  ))}
                </select>
              </div>

              {tempWidths.map((val, i) => (
                <div className="mb-1" key={i}>
                  <label className="block text-xs">
                    Column {i + 1} Width (%)
                  </label>
                  <input
                    type="number"
                    value={val}
                    onChange={(e) => {
                      const newWidths = [...tempWidths];
                      newWidths[i] = e.target.value;
                      setTempWidths(newWidths);
                    }}
                    className="w-full text-sm border rounded px-2 py-1"
                    min={1}
                    max={100}
                  />
                </div>
              ))}

              <div className="flex justify-end gap-2 mt-2">
                <button
                  onClick={() => setIsEditing(false)}
                  className="text-xs text-gray-500 hover:underline"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="text-xs text-blue-600 hover:underline font-semibold"
                >
                  Save
                </button>
              </div>
            </div>
          )}
        </div>
      );
    },
  }
);

export const insertColumnLayoutItem = (editor: MyBlockNoteEditor) => ({
  title: "Insert Column Layout",
  onItemClick: () => {
    insertOrUpdateBlock(editor, {
      type: "columnLayout",
      props: {
        columns: 2,
        columnWidths: "[50%,50%]",
      },
    });
  },
  aliases: ["columns", "layout", "grid"],
  group: "Layout",
  icon: <LayoutTemplate size={18} />,
  subtext: "Insert a multi-column layout block.",
});
