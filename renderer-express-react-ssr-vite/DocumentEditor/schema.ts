import { BlockNoteSchema, defaultBlockSpecs } from "@blocknote/core";
import { insertSubpageLinkItem, LinkPage } from "./LinkPage";
import { ColumnLayout, insertColumnLayoutItem } from "./Column";
import { DefaultReactSuggestionItem, getDefaultReactSlashMenuItems } from "@blocknote/react";

export const schema = BlockNoteSchema.create({
  blockSpecs: {
    ...defaultBlockSpecs,
    linkPage: LinkPage,
    columnLayout: ColumnLayout,
  },
});

export type MyBlockNoteEditor = typeof schema.BlockNoteEditor;
export type MyBlock = typeof schema.Block;


export const getCustomSlashMenuItems = (
  editor: MyBlockNoteEditor
): DefaultReactSuggestionItem[] => [
  ...getDefaultReactSlashMenuItems(editor),
  insertSubpageLinkItem(editor),
  insertColumnLayoutItem(editor),
];