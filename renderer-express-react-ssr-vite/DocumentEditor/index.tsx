"use client";

import { BlockNoteView } from "@blocknote/mantine";
import {
  BlockNoteEditor,
  BlockNoteSchema,
  defaultBlockSpecs,
  filterSuggestionItems,
  insertOrUpdateBlock,
  PartialBlock,
} from "@blocknote/core";
import { useEffect, useMemo, useState } from "react";
import {
  DefaultReactSuggestionItem,
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
} from "@blocknote/react";
import { LucideLink } from "lucide-react";
import { DEFAULT_CONTENT } from "./demo";
import { getCustomSlashMenuItems, schema } from "./schema";

interface DocumentEditorProps {
  storageKey: string;
}

export default function DocumentEditor({ storageKey }: DocumentEditorProps) {
  const [initialContent, setInitialContent] = useState<
    PartialBlock[] | "loading"
  >("loading");

  useEffect(() => {
    const stored = localStorage.getItem(storageKey);
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          setInitialContent(parsed);
          return;
        }
      } catch (err) {
        console.error("Failed to parse stored blocks:", err);
      }
    }
    setInitialContent(DEFAULT_CONTENT);
  }, [storageKey]);

  const editor = useMemo(() => {
    if (initialContent === "loading") return undefined;
    return BlockNoteEditor.create({ initialContent, schema });
  }, [initialContent]);

  const saveToStorage = () => {
    if (editor) {
      const content = editor.document;
      localStorage.setItem(storageKey, JSON.stringify(content));
    }
  };

  if (!editor) {
    return (
      <div className="flex justify-center items-center h-full py-40">
        <div className="animate-spin rounded-full h-10 w-10 border-4 border-blue-500 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <BlockNoteView editor={editor} slashMenu={false} onChange={saveToStorage}>
      <SuggestionMenuController
        triggerCharacter={"/"}
        getItems={async (query) =>
          filterSuggestionItems(getCustomSlashMenuItems(editor), query)
        }
      />
    </BlockNoteView>
  );
}
