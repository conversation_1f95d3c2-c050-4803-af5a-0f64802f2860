import {
  BlockFromConfig,
  CustomBlockConfig,
  defaultProps,
  InlineContentSchema,
  insertOrUpdateBlock,
  StyleSchema,
} from "@blocknote/core";
import {
  createReactBlockSpec,
  ReactCustomBlockRenderProps,
} from "@blocknote/react";
import { NotebookText } from "lucide-react";
import { FunctionComponent, useState } from "react";
import { MyBlockNoteEditor } from "./schema";

export const LinkPage = createReactBlockSpec(
  {
    type: "linkPage",
    propSchema: {
      textAlignment: defaultProps.textAlignment,
      textColor: defaultProps.textColor,
      url: { type: "string", default: "" },
      linkText: { type: "string", default: "Click here" },
    },
    content: "inline",
  },
  {
    render: (props) => {
      const { url, linkText } = props.block.props;
      const [opened, setOpened] = useState(false);
      const [tempUrl, setTempUrl] = useState(url);
      const [tempLinkText, setTempLinkText] = useState(linkText);

      const saveChanges = () => {
        props.editor.updateBlock(props.block, {
          type: "linkPage",
          props: {
            url: tempUrl,
            linkText: tempLinkText,
          },
        });
        setOpened(false);
      };

      return (
        <div
          className="relative linkPage-block flex space-x-2 items-center hover:bg-gray-200 p-2 rounded-md w-full"
          contentEditable={false}
        >
          <div onClick={() => setOpened((v) => !v)} className="cursor-pointer">
            <NotebookText size={20} />
          </div>

          <button
            // href={url}
            // target="_blank"
            // rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            {linkText}
          </button>

          {opened && (
            <div className="absolute mt-4 ml-4 top-full p-2 rounded bg-white shadow border z-50">
              <div className="mb-2">
                <label className="block text-xs font-semibold mb-1">
                  Link Text
                </label>
                <input
                  type="text"
                  value={tempLinkText}
                  onChange={(e) => setTempLinkText(e.target.value)}
                  className="border rounded px-2 py-1 w-64 text-sm"
                />
              </div>
              <div className="mb-2">
                <label className="block text-xs font-semibold mb-1">URL</label>
                <input
                  type="text"
                  value={tempUrl}
                  onChange={(e) => setTempUrl(e.target.value)}
                  className="border rounded px-2 py-1 w-64 text-sm"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setOpened(false)}
                  className="text-sm text-gray-600 hover:underline"
                >
                  Cancel
                </button>
                <button
                  onClick={saveChanges}
                  className="text-sm text-blue-600 hover:underline font-medium"
                >
                  Save
                </button>
              </div>
            </div>
          )}
        </div>
      );
    },
  }
);

export const insertSubpageLinkItem = (editor: MyBlockNoteEditor) => ({
  title: "Insert Subpage Link",
  onItemClick: () => {
    const id = crypto.randomUUID();
    insertOrUpdateBlock(editor, {
      type: "linkPage",
      props: {
        url: `/documents/${id}`,
        linkText: "Go to Subpage",
      },
    });
  },
  aliases: ["subpage", "page", "link"],
  group: "Page",
  icon: <NotebookText size={18} />,
  subtext: "Insert a link to a subpage.",
});
