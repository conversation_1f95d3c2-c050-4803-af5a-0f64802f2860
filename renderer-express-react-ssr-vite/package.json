{"name": "vite-react-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node server", "build": "npm run build:client && npm run build:server", "build:client": "vite build --outDir dist/client", "build:server": "vite build --ssr src/entry-server.jsx --outDir dist/server", "preview": "cross-env NODE_ENV=production node server"}, "dependencies": {"@blocknote/server-util": "latest", "chokidar": "^4.0.3", "compression": "^1.8.0", "dotenv": "^16.5.0", "express": "^5.0.1", "mermaid": "^11.6.0", "pino": "^9.6.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2", "sirv": "^3.0.1"}, "devDependencies": {"@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "vite": "^6.1.1"}}