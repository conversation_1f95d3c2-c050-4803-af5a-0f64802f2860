import "./index.css";
import { StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";

const mermaidDiagram = window.__MERMAID_DIAGRAM__;
const blocks = window.__BLOCKS__;

hydrateRoot(
  document.getElementById("root"),
  <StrictMode>
    <>
      {mermaidDiagram && <Mermaid mermaidDiagram={mermaidDiagram} />}
      {blocks && <BlockNote blocks={blocks} />}
    </>
  </StrictMode>
);
