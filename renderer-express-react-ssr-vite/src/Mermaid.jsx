import "./App.css";
import { useState, useEffect, useRef } from "react";
import reactLogo from "./assets/react.svg";
import mermaid from "mermaid";

function Mermaid({ mermaidDiagram }) {
  const [count, setCount] = useState(0);
  const mermaidRef = useRef(null);

  useEffect(() => {
    if (mermaidDiagram && mermaidRef.current) {
      // Initialize mermaid and render the diagram
      mermaid.initialize({ startOnLoad: true });
      mermaid.contentLoaded(); // This will render any Mermaid diagrams in the HTML
    }
  }, [mermaidDiagram]);

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src="/vite.svg" className="logo" alt="Vite logo" />
        </a>
        <a href="https://reactjs.org" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.jsx</code> and save to test HMR
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>

      {/* Render the Mermaid diagram here */}
      {mermaidDiagram && (
        <div ref={mermaidRef} className="mermaid">
          {mermaidDiagram}
        </div>
      )}
    </>
  );
}

export default Mermaid;
