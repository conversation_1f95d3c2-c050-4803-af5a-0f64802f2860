import { StrictMode } from "react";
import { renderToString } from "react-dom/server";
import Mermaid from "./Mermaid";
import BlockNote, { renderBlocksToHTML } from "./BlockNote";

/**
 * @param {string} _url
 * @param {{ mermaidDiagram?: string, blocks?: any[] }} options
 */
export async function render(_url, { mermaidDiagram, blocks }) {
  let blockNoteHtml = null;

  if (blocks) {
    blockNoteHtml = await renderBlocksToHTML(blocks);
  }

  const html = renderToString(
    <StrictMode>
      <>
        {mermaidDiagram && <Mermaid mermaidDiagram={mermaidDiagram} />}
        {blockNoteHtml && <BlockNote htmlContent={blockNoteHtml} />}
      </>
    </StrictMode>
  );

  return { html };
}
