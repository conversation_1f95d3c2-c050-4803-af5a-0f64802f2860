import React from "react";
import { ServerBlockNoteEditor } from "@blocknote/server-util";
import { schema } from "./../DocumentEditor/schema";

export const renderBlocksToHTML = async (blocks) => {
  const editor = ServerBlockNoteEditor.create({ schema });
  const html = await editor.blocksToFullHTML(blocks);
  return html;
};

export default function BlockNote({ htmlContent }) {
  return (
    <div className="bn-container">
      <div
        className="bn-default-styles"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </div>
  );
}
