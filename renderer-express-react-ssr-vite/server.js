import fs from "node:fs/promises";
import path from "node:path";
import express from "express";
import pino from "pino";
import pinoHttp from "pino-http";
import { loadEnv } from "./utils/envLoader.js";

const envDirPath = path.resolve("./..");
loadEnv(envDirPath);

const logger = pino({
  transport: {
    target: "pino-pretty",
  },
});

const isProduction = process.env.NODE_ENV === "production";
const port = process.env.EXPRESS_RENDERER_BASE_URL_PORT || 5173;
const base = process.env.BASE || "/";

const templateHtml = isProduction
  ? await fs.readFile("./dist/client/index.html", "utf-8")
  : "";

const app = express();
app.use(express.json());

app.use(pinoHttp({ logger }));

let vite;
if (!isProduction) {
  const { createServer } = await import("vite");
  vite = await createServer({
    server: { middlewareMode: true },
    appType: "custom",
    base,
  });
  app.use(vite.middlewares);
} else {
  const compression = (await import("compression")).default;
  const sirv = (await import("sirv")).default;
  app.use(compression());
  app.use(base, sirv("./dist/client", { extensions: [] }));
}

async function getMermaidDiagram() {
  try {
    const mermaidFilePath = path.resolve("./mermaid.json");
    const fileContent = await fs.readFile(mermaidFilePath, "utf-8");
    const parsedContent = JSON.parse(fileContent);

    if (parsedContent && parsedContent.diagram) {
      return parsedContent.diagram;
    } else {
      throw new Error("Invalid Mermaid diagram content");
    }
  } catch (error) {
    logger.error({ err: error }, "Error reading Mermaid diagram file");
    return null;
  }
}

app.get("/mermaid/:id", async (req, res) => {
  const { id } = req.params;

  try {
    const mermaidDiagram = await getMermaidDiagram();

    const url = req.originalUrl.replace(base, "");
    let template;
    let render;

    if (!isProduction) {
      template = await fs.readFile("./index.html", "utf-8");
      template = await vite.transformIndexHtml(url, template);
      render = (await vite.ssrLoadModule("/src/entry-server.jsx")).render;
    } else {
      template = templateHtml;
      render = (await import("./dist/server/entry-server.js")).render;
    }

    const rendered = await render(url, { mermaidDiagram });

    const html = template
      .replace(`<!--app-head-->`, rendered.head ?? "")
      .replace(`<!--app-html-->`, rendered.html ?? "");

    res.status(200).set({ "Content-Type": "text/html" }).send(html);
  } catch (e) {
    vite?.ssrFixStacktrace(e);
    logger.error({ err: e }, "Error rendering /mermaid/:id");
    res.status(500).end(e.stack);
  }
});

app.post("/documents/render/:id", async (req, res) => {
  const { blocks } = req.body;

  try {
    const url = req.originalUrl.replace(base, "");
    let template;
    let render;

    if (!isProduction) {
      template = await fs.readFile("./index.html", "utf-8");
      template = await vite.transformIndexHtml(url, template);
      render = (await vite.ssrLoadModule("/src/entry-server.jsx")).render;
    } else {
      template = templateHtml;
      render = (await import("./dist/server/entry-server.js")).render;
    }

    const rendered = await render(url, { blocks });

    res.status(200).json({ html: rendered });
  } catch (e) {
    vite?.ssrFixStacktrace(e);
    logger.error({ err: e }, "Error rendering document");
    res.status(500).end(e.stack);
  }
});

app.listen(port, () => {
  logger.info(`Server started at ${process.env.EXPRESS_RENDERER_BASE_URL}`);
});
