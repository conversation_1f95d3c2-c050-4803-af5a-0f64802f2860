import { config } from 'dotenv';
import fs from 'node:fs';
import path from 'node:path';

/**
 * Load environment variables from a file or directory.
 * @param {string} envPath - File path or directory path.
 */
export function loadEnv(envPath) {
  const isFile = envPath.endsWith('.env') || (fs.existsSync(envPath) && fs.lstatSync(envPath).isFile());

  if (isFile) {
    console.log(`🔍 Single env file provided: ${envPath}`);
    loadSingleEnvFile(envPath);
    watchFile(envPath); // Watch for file changes
  } else {
    console.log(`🔍 Scanning directory for env files: ${envPath}`);

    let files = fs.readdirSync(envPath)
      .filter(f => f.startsWith('.env'))
      .sort(envPrioritySort);

    if (files.length === 0) {
      console.warn('⚠️ No env files found.');
      return;
    }

    // Get the highest priority file (first file in the sorted list)
    const highestPriorityFile = files[0];
    const highestPriorityFilePath = path.join(envPath, highestPriorityFile);

    console.log(`🗂️ Highest priority env file found: ${highestPriorityFile}`);
    loadSingleEnvFile(highestPriorityFilePath);
    watchFile(highestPriorityFilePath); // Watch the highest priority env file
  }
}

/**
 * Load a single environment file.
 * @param {string} envFilePath - The path to the environment file.
 */
function loadSingleEnvFile(envFilePath) {
  const result = config({ path: envFilePath });
  if (result.error) {
    console.error(`❌ Failed to load env file: ${envFilePath}`, result.error);
  } else {
    console.log(`✅ Loaded env file: ${envFilePath}`);
  }
}

/**
 * Watch the environment file for changes and reload if necessary.
 * @param {string} envFilePath - The path to the environment file.
 */
function watchFile(envFilePath) {
  fs.watch(envFilePath, (eventType, filename) => {
    if (eventType === 'change') {
      console.log(`🔄 Env file changed: ${filename}`);
      loadSingleEnvFile(envFilePath); // Reload environment variables
    }
  });

  console.log(`👁️ Watching for changes in: ${envFilePath}`);
}

/**
 * Custom sorting for env files.
 * Priority: local > development > staging > test > production > others
 */
function envPrioritySort(a, b) {
  const order = ['local', 'development', 'staging', 'test', 'production'];

  const aIndex = order.findIndex(o => a.includes(o));
  const bIndex = order.findIndex(o => b.includes(o));

  if (aIndex === -1 && bIndex === -1) {
    return a.localeCompare(b); // normal sort if unknown
  }
  if (aIndex === -1) return 1;
  if (bIndex === -1) return -1;

  return aIndex - bIndex;
}
