importScripts("https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js");

self.onmessage = async function (event) {
  const { id, source, arguments: args } = event.data;

  try {
    const func = new Function("axios", "return " + source)(self.axios);

    if (typeof func !== "function") {
      throw new Error("The source code did not produce a valid function");
    }

    if (typeof args !== "object" || args === null || Array.isArray(args)) {
      throw new Error("Arguments must be a JSON object");
    }

    const result = await func(args);

    self.postMessage({ id, result });
  } catch (error) {
    self.postMessage({ error: error.message });
  }
};
