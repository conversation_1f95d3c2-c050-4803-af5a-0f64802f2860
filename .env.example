#############################
# OpenAI Configuration
#############################
OPENAI_API_KEY=sk-abc123xyz456example789
OPENAI_API_ORG=org-abc123example

#############################
# MongoDB Configuration
#############################
MONGODB_URI=mongodb+srv://user:<EMAIL>/?retryWrites=true&w=majority
MONGODB_DBNAME=myappdb

#############################
# Express Renderer Service
#############################
EXPRESS_RENDERER_BASE_URL=http://localhost:5176
EXPRESS_RENDERER_BASE_URL_PORT=5176

#############################
# Encryption
#############################
CRYPTO_SECRET_KEY=8b74f9c02ae14338b0fd4e6c4c20a3d1

#############################
# File Upload / CDN Configuration
#############################
UPLOAD_TARGET=vercel                          # or "local" or "cloudflare"
CDN_BASE_URL=https://cdn.myapp.com            # Replace with your actual CDN URL

#############################
# PostgreSQL / Prisma
#############################
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/mydatabase?sslmode=require"
# Optional: Use if your deployment provider requires a direct DB URL
# DIRECT_URL="postgresql://postgres:<EMAIL>:5432/mydatabase?sslmode=require"

#############################
# NextAuth Configuration
#############################
NEXTAUTH_SECRET="9a1b2c3d4e5f6g7h8i9j0kexamplekey"
NEXTAUTH_URL="http://localhost:3000"

#############################
# OAuth Providers
#############################

# Google OAuth
GOOGLE_CLIENT_ID="**********-abcde.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="GOCSPX-ExampleSecretKey123456"

# GitHub OAuth
GITHUB_CLIENT_ID="Iv1.**********abcdef"
GITHUB_CLIENT_SECRET="abcdef**********abcdef**********abcdef12"

#############################
# Email Configuration (Optional)
#############################
# EMAIL_SERVER="smtp://user:<EMAIL>:587"
# EMAIL_FROM="<EMAIL>"


RESEND_API_KEY=re_92******ejDfbGR